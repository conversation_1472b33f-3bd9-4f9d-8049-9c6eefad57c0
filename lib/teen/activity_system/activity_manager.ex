defmodule Teen.ActivitySystem.ActivityManager do
  @moduledoc """
  活动管理器

  负责管理活动的生命周期，包括：
  - 活动启动和停止
  - 定时任务处理
  - 活动状态监控
  - 自动奖励发放
  """

  use GenServer
  require Logger

  alias Teen.ActivitySystem.{
    ActivityService,
    UserActivityParticipation,
    RewardClaimRecord,
    ActivityStatistics,
    TaskProgressService
  }

  alias Teen.Events.GameEvent

  @check_interval :timer.minutes(5)

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  手动触发活动检查
  """
  def check_activities do
    GenServer.cast(__MODULE__, :check_activities)
  end

  @doc """
  获取活动管理器状态
  """
  def get_status do
    GenServer.call(__MODULE__, :get_status)
  end

  @doc """
  处理用户游戏完成事件
  """
  def handle_game_completed(user_id, game_id, game_result) do
    GenServer.cast(__MODULE__, {:game_completed, user_id, game_id, game_result})
  end

  @doc """
  处理用户充值事件
  """
  def handle_user_recharged(user_id, amount) do
    GenServer.cast(__MODULE__, {:user_recharged, user_id, amount})
  end

  @doc """
  处理用户登录事件
  """
  def handle_user_login(user_id) do
    GenServer.cast(__MODULE__, {:user_login, user_id})
  end

  # GenServer 回调

  @impl true
  def init(_opts) do
    Logger.info("🎯 [ACTIVITY_MANAGER] 活动管理器启动")

    # 订阅所有相关事件
    subscribe_to_events()

    # 启动定时检查
    schedule_check()

    state = %{
      last_check: DateTime.utc_now(),
      processed_events: 0,
      active_activities: %{}
    }

    {:ok, state}
  end

  @impl true
  def handle_call(:get_status, _from, state) do
    status = %{
      last_check: state.last_check,
      processed_events: state.processed_events,
      active_activities_count: map_size(state.active_activities),
      uptime: DateTime.diff(DateTime.utc_now(), state.last_check, :second)
    }

    {:reply, status, state}
  end

  @impl true
  def handle_cast(:check_activities, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 执行活动检查")

    new_state = %{
      state
      | last_check: DateTime.utc_now(),
        processed_events: state.processed_events + 1
    }

    # 执行各种活动检查
    check_expired_activities()
    check_daily_resets()
    process_auto_rewards()

    {:noreply, new_state}
  end

  @impl true
  def handle_cast({:game_completed, user_id, game_id, game_result}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理游戏完成事件 - 用户: #{user_id}, 游戏: #{game_id}")

    # 更新游戏任务进度
    update_game_task_progress(user_id, game_id, game_result)

    # 更新免费任务进度
    update_free_bonus_progress(user_id, game_id, game_result)

    new_state = %{state | processed_events: state.processed_events + 1}
    {:noreply, new_state}
  end

  @impl true
  def handle_cast({:user_recharged, user_id, amount}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理用户充值事件 - 用户: #{user_id}, 金额: #{amount}")

    # 更新充值任务进度
    update_recharge_task_progress(user_id, amount)

    # 更新周卡进度
    update_weekly_card_progress(user_id, amount)

    # 更新充值转盘进度
    update_recharge_wheel_progress(user_id, amount)

    # 更新刮刮卡进度
    update_scratch_card_progress(user_id, amount)

    new_state = %{state | processed_events: state.processed_events + 1}
    {:noreply, new_state}
  end

  @impl true
  def handle_cast({:user_login, user_id}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理用户登录事件 - 用户: #{user_id}")

    # 更新七日登录进度
    update_seven_day_progress(user_id)

    # 检查VIP礼包
    check_vip_gift_eligibility(user_id)

    new_state = %{state | processed_events: state.processed_events + 1}
    {:noreply, new_state}
  end

  @impl true
  def handle_info(:check_activities, state) do
    # 定时检查
    GenServer.cast(self(), :check_activities)
    schedule_check()
    {:noreply, state}
  end

  # 私有函数

  defp schedule_check do
    Process.send_after(self(), :check_activities, @check_interval)
  end

  defp check_expired_activities do
    # 检查过期的活动
    Logger.debug("🎯 [ACTIVITY_MANAGER] 检查过期活动")
  end

  defp check_daily_resets do
    # 检查需要每日重置的活动
    Logger.debug("🎯 [ACTIVITY_MANAGER] 检查每日重置")
  end

  defp process_auto_rewards do
    # 处理自动奖励发放
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理自动奖励")
  end

  defp update_game_task_progress(user_id, game_id, game_result) do
    # 查找相关的游戏任务
    case Teen.ActivitySystem.GameTask.list_by_game(%{game_id: game_id}) do
      {:ok, tasks} ->
        Enum.each(tasks, fn task ->
          progress_delta = calculate_game_progress_delta(task, game_result)

          if progress_delta > 0 do
            ActivityService.update_progress(user_id, :game_task, task.id, progress_delta)
          end
        end)

      {:error, _} ->
        Logger.warning("🎯 [ACTIVITY_MANAGER] 无法获取游戏任务列表")
    end
  end

  defp calculate_game_progress_delta(task, game_result) do
    case task.task_type do
      # 每完成一局游戏 +1
      :game_rounds ->
        1

      :win_rounds ->
        if Map.get(game_result, :is_win, false), do: 1, else: 0

      _ ->
        0
    end
  end

  defp update_free_bonus_progress(user_id, game_id, game_result) do
    # 更新免费任务进度
    case Teen.ActivitySystem.FreeBonusTask.get_by_game(game_id) do
      {:ok, tasks} ->
        Enum.each(tasks, fn task ->
          win_amount = Map.get(game_result, :win_amount, Decimal.new("0"))

          if Decimal.compare(win_amount, task.required_win_coins) >= 0 do
            ActivityService.update_progress(user_id, :free_bonus_task, task.id, 1)
          end
        end)

      {:error, _} ->
        :ok
    end
  end

  defp update_recharge_task_progress(user_id, amount) do
    # 更新充值任务进度
    ActivityService.update_progress(user_id, :recharge_task, nil, amount)
  end

  defp update_weekly_card_progress(user_id, amount) do
    # 更新周卡进度
    ActivityService.update_progress(user_id, :weekly_card, nil, amount)
  end

  defp update_recharge_wheel_progress(user_id, amount) do
    # 更新充值转盘进度
    ActivityService.update_progress(user_id, :recharge_wheel, nil, amount)
  end

  defp update_scratch_card_progress(user_id, amount) do
    # 更新刮刮卡进度
    ActivityService.update_progress(user_id, :scratch_card, nil, amount)
  end

  defp update_seven_day_progress(user_id) do
    # 更新七日登录进度
    ActivityService.update_progress(user_id, :seven_day_task, nil, 1)
  end

  defp check_vip_gift_eligibility(user_id) do
    # 检查VIP礼包资格
    ActivityService.participate_activity(user_id, :vip_gift)
  end
  # ==================== 事件订阅和处理 ====================

  defp subscribe_to_events do
    # 订阅用户登录事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:user_login))

    # 订阅充值事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:user_recharge))

    # 订阅游戏完成事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:game_completed))

    # 订阅游戏获胜事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:game_won))

    # 订阅VIP等级提升事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:vip_level_up))

    # 订阅用户分享事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:user_share))

    # 订阅用户提现事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, GameEvent.broadcast_topic(:user_withdrawal))

    Logger.info("🎯 [ACTIVITY_MANAGER] 事件订阅完成")
  end

  # 处理游戏事件
  @impl true
  def handle_info({:game_event, %GameEvent{event_type: :user_login} = event}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理用户登录事件: #{event.user_id}")

    # 异步处理任务进度更新
    Task.start(fn ->
      TaskProgressService.update_seven_day_login(event.user_id)
    end)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  @impl true
  def handle_info({:game_event, %GameEvent{event_type: :user_recharge} = event}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理充值事件: #{event.user_id}")

    Task.start(fn ->
      try do
        # 提取充值金额，处理可能的数据结构差异
        amount =
          case event.event_data do
            %{amount: amt} -> amt
            amt when is_integer(amt) -> amt
            data -> Map.get(data, :amount, data)
          end

        TaskProgressService.update_recharge_tasks(event.user_id, amount)

        # 更新VIP经验值 - GameManagement使用UUID
        Teen.GameManagement.update_user_recharge(event.user_id, amount)

        # 更新刮刮卡充值进度
        Teen.ActivitySystem.ScratchCardService.update_user_recharge(
          event.user_id,
          Decimal.new(amount)
        )
      rescue
        error ->
          Logger.error("🎯 [ACTIVITY_MANAGER] 处理充值事件失败: #{inspect(error)}")
      end
    end)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  @impl true
  def handle_info({:game_event, %GameEvent{event_type: :game_completed} = event}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理游戏完成事件: #{event.user_id}")

    Task.start(fn ->
      game_id = event.event_data.game_id
      TaskProgressService.update_game_count_tasks(event.user_id, game_id)
    end)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  @impl true
  def handle_info({:game_event, %GameEvent{event_type: :game_won} = event}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理游戏获胜事件: #{event.user_id}")

    Task.start(fn ->
      game_id = event.event_data.game_id
      TaskProgressService.update_game_win_tasks(event.user_id, game_id)
    end)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  @impl true
  def handle_info({:game_event, %GameEvent{event_type: :vip_level_up} = event}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 处理VIP等级提升事件: #{event.user_id}")

    Task.start(fn ->
      new_level = event.event_data.new_level
      TaskProgressService.update_vip_tasks(event.user_id, new_level)
    end)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  # 处理其他事件类型
  @impl true
  def handle_info({:game_event, event}, state) do
    Logger.debug("🎯 [ACTIVITY_MANAGER] 收到未处理的事件: #{event.event_type}")
    {:noreply, state}
  end

  # 私有辅助函数

  defp get_user_numeric_id(user_id) do
    case Cypridina.Accounts.User.get_by_id(user_id) do
      {:ok, user} -> {:ok, user.numeric_id}
      {:error, reason} -> {:error, reason}
    end
  end
end
