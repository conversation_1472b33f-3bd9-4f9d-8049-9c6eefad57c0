defmodule Teen.ActivitySystem.RewardDistributionService do
  @moduledoc """
  统一奖励发放服务

  处理各种类型奖励的发放，包括：
  - 积分/金币
  - 现金
  - 道具
  - VIP经验
  - 其他自定义奖励
  """

  require Logger
  alias Teen.ActivitySystem.RewardClaimRecord

  @doc """
  发放奖励给用户

  ## 参数
  - user_id: 用户ID
  - rewards: 奖励列表，格式：
    [
      %{type: :coins, amount: 1000, description: "金币奖励"},
      %{type: :cash, amount: 500, description: "现金奖励"},
      %{type: :item, item_id: "item_001", quantity: 5, description: "道具奖励"}
    ]
  - source: 奖励来源信息
    %{
      source_type: :cdkey,  # 来源类型
      source_id: "cdkey_id", # 来源ID
      activity_name: "CDKEY兑换", # 活动名称
      metadata: %{} # 额外元数据
    }

  ## 返回值
  {:ok, %{total_amount: 总金额, distributed_rewards: 已发放奖励列表}}
  {:error, reason}
  """
  def distribute_rewards(user_id, rewards, source) when is_list(rewards) do
    Logger.info("开始发放奖励 - 用户: #{user_id}, 奖励数量: #{length(rewards)}")

    # 验证用户存在
    with {:ok, _user} <- validate_user(user_id),
         {:ok, processed_rewards} <- process_rewards(user_id, rewards, source) do
      # 计算总金额（仅计算金币和现金）
      total_amount = calculate_total_amount(processed_rewards)

      Logger.info("奖励发放成功 - 用户: #{user_id}, 总金额: #{total_amount}")

      {:ok,
       %{
         total_amount: total_amount,
         distributed_rewards: processed_rewards,
         message: "奖励发放成功"
       }}
    else
      {:error, reason} = error ->
        Logger.error("奖励发放失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
        error
    end
  end

  @doc """
  发放单个奖励
  """
  def distribute_single_reward(user_id, reward, source) do
    distribute_rewards(user_id, [reward], source)
  end

  # 私有函数

  defp validate_user(user_id) do
    case Cypridina.Accounts.User.get_by_id(user_id) do
      {:ok, user} -> {:ok, user}
      {:error, _} -> {:error, :user_not_found}
    end
  end

  defp process_rewards(user_id, rewards, source) do
    results =
      Enum.map(rewards, fn reward ->
        process_single_reward(user_id, reward, source)
      end)

    # 检查是否有失败的奖励
    failed_rewards = Enum.filter(results, fn {status, _} -> status == :error end)

    if Enum.empty?(failed_rewards) do
      successful_rewards = Enum.map(results, fn {:ok, reward} -> reward end)
      {:ok, successful_rewards}
    else
      # 如果有失败的奖励，返回第一个错误
      {_, reason} = List.first(failed_rewards)
      {:error, reason}
    end
  end

  defp process_single_reward(user_id, reward, source) do
    case reward.type do
      :coins ->
        distribute_coins(user_id, reward, source)

      :cash ->
        distribute_cash(user_id, reward, source)

      :points ->
        distribute_points(user_id, reward, source)

      :vip_exp ->
        distribute_vip_exp(user_id, reward, source)

      :item ->
        distribute_item(user_id, reward, source)

      _ ->
        Logger.warning("未知奖励类型: #{reward.type}")
        {:error, :unknown_reward_type}
    end
  end

  defp distribute_coins(user_id, reward, source) do
    amount = reward.amount || 0
    description = reward.description || "金币奖励"

    case Cypridina.Accounts.add_points(user_id, amount,
           transaction_type: :bonus,
           description: description,
           metadata: build_metadata(source, reward)
         ) do
      {:ok, _transaction} ->
        # 记录奖励发放
        record_reward_distribution(user_id, reward, source)

        {:ok,
         %{
           type: :coins,
           amount: amount,
           description: description,
           status: :distributed
         }}

      {:error, reason} ->
        {:error, "金币发放失败: #{inspect(reason)}"}
    end
  end

  defp distribute_cash(user_id, reward, source) do
    amount = reward.amount || 0
    description = reward.description || "现金奖励"

    # 这里需要根据实际的现金系统进行调整
    case Cypridina.Accounts.add_balance(user_id, amount,
           transaction_type: :bonus,
           description: description,
           metadata: build_metadata(source, reward)
         ) do
      {:ok, _transaction} ->
        record_reward_distribution(user_id, reward, source)

        {:ok,
         %{
           type: :cash,
           amount: amount,
           description: description,
           status: :distributed
         }}

      {:error, reason} ->
        {:error, "现金发放失败: #{inspect(reason)}"}
    end
  end

  defp distribute_points(user_id, reward, source) do
    amount = reward.amount || 0
    description = reward.description || "积分奖励"

    case Cypridina.Accounts.add_points(user_id, amount,
           transaction_type: :bonus,
           description: description,
           metadata: build_metadata(source, reward)
         ) do
      {:ok, _transaction} ->
        record_reward_distribution(user_id, reward, source)

        {:ok,
         %{
           type: :points,
           amount: amount,
           description: description,
           status: :distributed
         }}

      {:error, reason} ->
        {:error, "积分发放失败: #{inspect(reason)}"}
    end
  end

  defp distribute_vip_exp(user_id, reward, source) do
    amount = reward.amount || 0
    description = reward.description || "VIP经验奖励"

    # TODO: 实现VIP经验发放逻辑
    Logger.info("发放VIP经验: 用户=#{user_id}, 经验=#{amount}")

    record_reward_distribution(user_id, reward, source)

    {:ok,
     %{
       type: :vip_exp,
       amount: amount,
       description: description,
       status: :distributed
     }}
  end

  defp distribute_item(user_id, reward, source) do
    item_id = reward.item_id
    quantity = reward.quantity || 1
    description = reward.description || "道具奖励"

    # TODO: 实现道具发放逻辑
    Logger.info("发放道具: 用户=#{user_id}, 道具=#{item_id}, 数量=#{quantity}")

    record_reward_distribution(user_id, reward, source)

    {:ok,
     %{
       type: :item,
       item_id: item_id,
       quantity: quantity,
       description: description,
       status: :distributed
     }}
  end

  defp record_reward_distribution(user_id, reward, source) do
    reward_data = %{
      reward_type: reward.type,
      reward_amount: reward.amount || 0,
      reward_description: reward.description,
      distributed_at: DateTime.utc_now()
    }

    # 如果是道具奖励，添加道具信息
    reward_data =
      if reward.type == :item do
        Map.merge(reward_data, %{
          item_id: reward.item_id,
          quantity: reward.quantity
        })
      else
        reward_data
      end

    case RewardClaimRecord.create(%{
           user_id: user_id,
           activity_type: source.source_type,
           activity_id: source.source_id,
           reward_type: reward.type,
           reward_amount: reward.amount || 0,
           reward_data: reward_data
         }) do
      {:ok, record} ->
        Logger.debug("奖励记录创建成功: #{record.id}")
        {:ok, record}

      {:error, reason} ->
        Logger.warning("奖励记录创建失败: #{inspect(reason)}")
        # 不影响奖励发放，只是记录失败
        {:ok, :record_failed}
    end
  end

  defp build_metadata(source, reward) do
    base_metadata = %{
      "operation" => "reward_distribution",
      "source_type" => source.source_type,
      "source_id" => source.source_id,
      "activity_name" => source.activity_name,
      "reward_type" => reward.type,
      "distributed_at" => DateTime.utc_now() |> DateTime.to_iso8601()
    }

    # 合并额外的元数据
    Map.merge(base_metadata, source.metadata || %{})
  end

  defp calculate_total_amount(rewards) do
    rewards
    |> Enum.filter(fn reward -> reward.type in [:coins, :cash, :points] end)
    |> Enum.reduce(0, fn reward, acc -> acc + (reward.amount || 0) end)
  end

  @doc """
  格式化奖励列表用于显示
  """
  def format_rewards_for_display(rewards) when is_list(rewards) do
    Enum.map(rewards, fn reward ->
      case reward.type do
        :coins -> "金币 #{reward.amount}"
        :cash -> "现金 #{reward.amount}"
        :points -> "积分 #{reward.amount}"
        :vip_exp -> "VIP经验 #{reward.amount}"
        :item -> "#{reward.item_id} x#{reward.quantity}"
        _ -> "未知奖励"
      end
    end)
    |> Enum.join(", ")
  end

  @doc """
  解析奖励配置
  从各种格式的奖励配置中解析出标准格式
  """
  def parse_reward_config(%{"rewards" => rewards}) when is_list(rewards) do
    parsed_rewards =
      Enum.map(rewards, fn reward ->
        case reward do
          # 处理 Teen.Reward 结构体
          %{type: type, amount: amount} = reward_struct ->
            %{
              type: type,
              amount: amount,
              description: Map.get(reward_struct, :description, "#{type}奖励")
            }

          # 处理普通 map
          %{"type" => type, "amount" => amount} = reward_map ->
            %{
              type: String.to_existing_atom(type),
              amount: amount,
              description: Map.get(reward_map, "description", "#{type}奖励")
            }

          _ ->
            nil
        end
      end)
      |> Enum.filter(&(!is_nil(&1)))

    {:ok, parsed_rewards}
  end

  def parse_reward_config(reward_config) when is_map(reward_config) do
    rewards = []

    # 解析金币奖励
    rewards =
      if reward_config["coins"] || reward_config[:coins] do
        amount = reward_config["coins"] || reward_config[:coins]
        [%{type: :coins, amount: amount, description: "金币奖励"} | rewards]
      else
        rewards
      end

    # 解析现金奖励
    rewards =
      if reward_config["cash"] || reward_config[:cash] do
        amount = reward_config["cash"] || reward_config[:cash]
        [%{type: :cash, amount: amount, description: "现金奖励"} | rewards]
      else
        rewards
      end

    # 解析积分奖励
    rewards =
      if reward_config["points"] || reward_config[:points] do
        amount = reward_config["points"] || reward_config[:points]
        [%{type: :points, amount: amount, description: "积分奖励"} | rewards]
      else
        rewards
      end

    # 解析道具奖励
    rewards =
      if reward_config["items"] || reward_config[:items] do
        items = reward_config["items"] || reward_config[:items]

        item_rewards =
          Enum.map(items, fn {item_id, quantity} ->
            %{type: :item, item_id: item_id, quantity: quantity, description: "道具奖励"}
          end)

        rewards ++ item_rewards
      else
        rewards
      end

    {:ok, Enum.reverse(rewards)}
  end

  def parse_reward_config(_), do: {:error, :invalid_reward_config}
end
