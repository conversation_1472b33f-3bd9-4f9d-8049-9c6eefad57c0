defmodule Teen.Live.ActivitySystem.UserLossRebateLive do
  @moduledoc """
  用户损失返利管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.UserLossRebate
    layout({Teen.Layouts, :admin})

    fields do
      field :user_id do
        module Backpex.Fields.Text
        label("用户ID")
        searchable(true)
      end

      field :last_settlement_at do
        # module Backpex.Fields.DateTime
        label("上次结算时间")
        # format("{YYYY}-{0M}-{0D} {h24}:{m}:{s}")
      end

      field :total_bet_amount do
        module Backpex.Fields.Number
        label("总下注金额")
        render(fn assigns ->
          ~H"""
          <span class="font-mono text-sm">
            ¥<%= Decimal.div(@value, 100) |> Decimal.to_string(:normal) %>
          </span>
          """
        end)
      end

      field :total_win_amount do
        module Backpex.Fields.Number
        label("总获胜金额")
        render(fn assigns ->
          ~H"""
          <span class="font-mono text-sm">
            ¥<%= Decimal.div(@value, 100) |> Decimal.to_string(:normal) %>
          </span>
          """
        end)
      end

      field :net_loss_amount do
        module Backpex.Fields.Number
        label("净损失金额")
        render(fn assigns ->
          ~H"""
          <span class="font-mono text-sm text-error">
            ¥<%= Decimal.div(@value, 100) |> Decimal.to_string(:normal) %>
          </span>
          """
        end)
      end

      field :rebate_amount do
        module Backpex.Fields.Number
        label("可领取返利")
        render(fn assigns ->
          ~H"""
          <span class="font-mono text-sm text-success font-bold">
            ¥<%= Decimal.div(@value, 100) |> Decimal.to_string(:normal) %>
          </span>
          """
        end)
      end

      field :vip_level do
        module Backpex.Fields.Number
        label("VIP等级")
        render(fn assigns ->
          ~H"""
          <div class="badge badge-primary">VIP <%= @value %></div>
          """
        end)
      end

      field :rebate_rate do
        module Backpex.Fields.Number
        label("返利比例")
        render(fn assigns ->
          ~H"""
          <span class="text-sm"><%= @value %>%</span>
          """
        end)
      end

      # field :updated_at do
      #   module Backpex.Fields.DateTime
      #   label("更新时间")
      #   format("{YYYY}-{0M}-{0D} {h24}:{m}:{s}")
      # end
    end
    # filters do
    #   filter :user_id do
    #     module Backpex.Filters.Text
    #     label("用户ID")
    #   end

    #   filter :has_rebate do
    #     module Backpex.Filters.Boolean
    #     label("有可领取返利")
    #     filter(fn query, %{value: true} ->
    #       Ash.Query.filter(query, rebate_amount > 0)
    #     end)
    #   end
    # end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "用户损失返利"

  @impl Backpex.LiveResource
  def plural_name, do: "用户损失返利管理"

  # @impl Backpex.LiveResource
  # def item_actions(_) do
  #   [
  #     show: %{
  #       module: Backpex.ItemActions.Show,
  #       only: [:row]
  #     },
  #     settle_rebate: %{
  #       module: Teen.ItemActions.SettleRebateAction,
  #       only: [:row],
  #       label: "结算返利",
  #       icon: "hero-calculator"
  #     },
  #     claim_rebate: %{
  #       module: Teen.ItemActions.ClaimRebateAction,
  #       only: [:row],
  #       label: "领取返利",
  #       icon: "hero-gift"
  #     }
  #   ]
  # end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> false  # 不允许手动创建
      :edit -> false  # 不允许手动编辑
      :delete -> true  # 不允许删除
      _ -> true
    end
    true
  end
end
