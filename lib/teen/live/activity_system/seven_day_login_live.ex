defmodule Teen.Live.ActivitySystem.SevenDayLoginLive do
  @moduledoc """
  七日登录奖励管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.SevenDayTask
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :day_number do
        module Backpex.Fields.Number
        label("第几天")
      end

      field :rewards do
        module Teen.Live.Fields.RewardField
        label("奖励配置")
      end

      field :is_special do
        module Backpex.Fields.Boolean
        label("是否特殊奖励")
      end

      field :description do
        module Backpex.Fields.Textarea
        label("描述")
      end
      field :is_cyclic do
        module Backpex.Fields.Boolean
        label("是否循环")
      end

      field :status do
        module Backpex.Fields.Select
        label("状态")

        options([
          {"启用", :enabled},
          {"禁用", :disabled}
        ])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "七日登录奖励"

  @impl Backpex.LiveResource
  def plural_name, do: "七日登录奖励"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
