defmodule Teen.Live.ActivitySystem.CdkeyLive do
  use AshBackpex.LiveResource

  backpex do
    resource Teen.ActivitySystem.Cdkey
    load []
    layout({Teen.Layouts, :admin})

    fields do
      field :code

      field :status do
        module Backpex.Fields.Select

        options([
          {"激活", :active},
          {"停用", :inactive},
          {"过期", :expired}
        ])
      end

      field :max_uses
      field :used_count

      field :rewards do
        module Teen.Live.Fields.RewardField
      end

      field :creator
      field :valid_from
      field :valid_to
    end

    filters do
      filter :status do
        module Teen.Live.Filters.CdkeyStatusFilter
      end

      filter :creator do
        module Teen.Live.Filters.CdkeyCreatorFilter
      end

      filter :validity do
        module Teen.Live.Filters.CdkeyValidityFilter
      end
    end

    resource_actions do
      resource_action(:batch_create, Teen.Live.Actions.CdkeyBatchCreateAction)
      resource_action(:export, Teen.Live.Actions.CdkeyExportAction)
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "CDKEY"

  @impl Backpex.LiveResource
  def plural_name, do: "CDKEY管理"

  # @impl Backpex.LiveResource
end
