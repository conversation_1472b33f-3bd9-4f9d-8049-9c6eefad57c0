defmodule Teen.Live.ActivitySystem.ScratchCardActivityLive do
  @moduledoc """
  刮刮卡活动管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.ScratchCardActivity
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :activity_title do
        module Backpex.Fields.Text
        label("活动标题")
      end

      field :claimable_count do
        module Backpex.Fields.Number
        label("可领取数量")
      end

      field :card_type do
        module Backpex.Fields.Select
        label("卡片类型")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :cost_amount do
        module Backpex.Fields.Number
        label("购买费用")
      end

      field :rewards do
        module Teen.Live.Fields.RewardField
        label("奖励配置")
      end

      field :reward_probability do
        module Backpex.Fields.Number
        label("奖励概率（%）")
      end

      field :daily_limit do
        module Backpex.Fields.Number
        label("每日限制次数")
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "刮刮卡活动"

  @impl Backpex.LiveResource
  def plural_name, do: "刮刮卡活动"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
