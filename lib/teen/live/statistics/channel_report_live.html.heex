<div class="container mx-auto p-6">
  <!-- 页面标题和操作栏 -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">渠道报表</h1>
      <p class="text-gray-600 mt-1">基于渠道包ID的统计报表</p>
    </div>
    
    <div class="flex gap-3">
      <!-- 日期选择器 -->
      <div class="form-control">
        <input 
          type="date" 
          class="input input-bordered"
          value={Date.to_iso8601(@selected_date)}
          phx-change="change_date"
          name="date"
        />
      </div>
      
      <!-- 刷新按钮 -->
      <button 
        class="btn btn-primary"
        phx-click="refresh"
        disabled={@loading}
      >
        <.icon name="hero-arrow-path" class="w-4 h-4" />
        <%= if @loading, do: "刷新中...", else: "刷新" %>
      </button>
      
      <!-- 导出按钮 -->
      <button 
        class="btn btn-secondary"
        phx-click="export"
      >
        <.icon name="hero-document-arrow-down" class="w-4 h-4" />
        导出
      </button>
    </div>
  </div>

  <!-- 统计卡片 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-primary">
        <.icon name="hero-building-storefront" class="w-8 h-8" />
      </div>
      <div class="stat-title">渠道总数</div>
      <div class="stat-value text-primary"><%= @stats.total_channels %></div>
      <div class="stat-desc">活跃渠道: <%= @stats.active_channels %></div>
    </div>

    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-secondary">
        <.icon name="hero-users" class="w-8 h-8" />
      </div>
      <div class="stat-title">用户总数</div>
      <div class="stat-value text-secondary"><%= @stats.total_users %></div>
      <div class="stat-desc">所有渠道用户</div>
    </div>

    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-success">
        <.icon name="hero-banknotes" class="w-8 h-8" />
      </div>
      <div class="stat-title">总收入</div>
      <div class="stat-value text-success"><%= format_currency(@stats.total_revenue) %></div>
      <div class="stat-desc">所有渠道收入</div>
    </div>

    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-info">
        <.icon name="hero-calendar-days" class="w-8 h-8" />
      </div>
      <div class="stat-title">报表日期</div>
      <div class="stat-value text-info text-lg"><%= Date.to_iso8601(@selected_date) %></div>
      <div class="stat-desc">数据统计日期</div>
    </div>
  </div>

  <!-- 加载状态 -->
  <%= if @loading do %>
    <div class="flex justify-center items-center py-12">
      <span class="loading loading-spinner loading-lg"></span>
      <span class="ml-3 text-lg">加载报表数据中...</span>
    </div>
  <% else %>
    <!-- 报表表格 -->
    <div class="bg-base-100 shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="table table-zebra w-full">
          <thead>
            <tr class="bg-base-200">
              <th>渠道ID</th>
              <th>渠道名称</th>
              <th>包名</th>
              <th class="text-right">总用户数</th>
              <th class="text-right">活跃用户</th>
              <th class="text-right">今日新增</th>
              <th class="text-right">在线用户</th>
              <th class="text-right">总收入</th>
              <th class="text-right">今日收入</th>
              <th class="text-right">总游戏局数</th>
              <th class="text-right">今日游戏</th>
              <th class="text-right">平均会话</th>
              <th class="text-right">留存率</th>
              <th class="text-right">活跃率</th>
            </tr>
          </thead>
          <tbody>
            <%= if Enum.empty?(@reports) do %>
              <tr>
                <td colspan="14" class="text-center py-8 text-gray-500">
                  <.icon name="hero-inbox" class="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <div>暂无报表数据</div>
                  <div class="text-sm">请检查是否有渠道配置或用户数据</div>
                </td>
              </tr>
            <% else %>
              <%= for report <- @reports do %>
                <tr class="hover">
                  <td class="font-mono"><%= report.channel_id %></td>
                  <td class="font-medium"><%= report.channel_name %></td>
                  <td class="text-sm text-gray-600"><%= report.package_name || "-" %></td>
                  <td class="text-right font-medium"><%= report.total_users %></td>
                  <td class="text-right">
                    <span class="badge badge-success badge-sm"><%= report.active_users %></span>
                  </td>
                  <td class="text-right">
                    <span class="badge badge-info badge-sm"><%= report.new_users_today %></span>
                  </td>
                  <td class="text-right">
                    <span class="badge badge-warning badge-sm"><%= report.online_users %></span>
                  </td>
                  <td class="text-right font-medium text-success">
                    <%= format_currency(report.total_revenue) %>
                  </td>
                  <td class="text-right text-success">
                    <%= format_currency(report.revenue_today) %>
                  </td>
                  <td class="text-right"><%= report.total_games_played %></td>
                  <td class="text-right"><%= report.games_played_today %></td>
                  <td class="text-right">
                    <%= if report.avg_session_time do %>
                      <%= Decimal.round(report.avg_session_time, 1) %>分钟
                    <% else %>
                      -
                    <% end %>
                  </td>
                  <td class="text-right">
                    <%= if report.retention_rate do %>
                      <span class="badge badge-outline"><%= format_percentage(report.retention_rate) %></span>
                    <% else %>
                      -
                    <% end %>
                  </td>
                  <td class="text-right">
                    <%= if report.activity_rate do %>
                      <span class="badge badge-outline"><%= format_percentage(report.activity_rate) %></span>
                    <% else %>
                      -
                    <% end %>
                  </td>
                </tr>
              <% end %>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 报表说明 -->
    <div class="mt-8 bg-base-100 shadow rounded-lg p-6">
      <h3 class="text-lg font-semibold mb-4">报表说明</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <h4 class="font-medium mb-2">用户指标</h4>
          <ul class="space-y-1 text-gray-600">
            <li>• <strong>总用户数</strong>: 该渠道注册的所有用户</li>
            <li>• <strong>活跃用户</strong>: 24小时内有活动的用户</li>
            <li>• <strong>今日新增</strong>: 今日新注册的用户数</li>
            <li>• <strong>在线用户</strong>: 当前在线的用户数</li>
          </ul>
        </div>
        <div>
          <h4 class="font-medium mb-2">收入指标</h4>
          <ul class="space-y-1 text-gray-600">
            <li>• <strong>总收入</strong>: 该渠道用户的累计充值金额</li>
            <li>• <strong>今日收入</strong>: 今日该渠道的充值金额</li>
            <li>• <strong>平均会话</strong>: 用户平均在线时长</li>
            <li>• <strong>留存率</strong>: 活跃用户占总用户的比例</li>
          </ul>
        </div>
      </div>
    </div>
  <% end %>
</div>
