defmodule Teen.Live.Statistics.ReportDashboardLive do
  @moduledoc """
  统一报表管理界面

  支持查看不同类型的报表：
  - 渠道报表
  - 系统日报表
  - 系统月报表
  - 平台日报表
  - 平台月报表
  - 在线统计
  - 实时数据
  """

  use CypridinaWeb, :live_view
  import Phoenix.Component
  import Phoenix.LiveView

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 设置定时刷新实时数据
      Process.send_after(self(), :refresh_realtime, 5_000)
    end

    socket =
      socket
      |> assign(:page_title, "报表中心")
      |> assign(:current_url, "/admin/reports")
      |> assign(:loading, true)
      |> assign(:selected_report_type, "channel")
      |> assign(:selected_date, Date.utc_today())
      |> assign(:selected_month, Date.utc_today())
      |> assign(:selected_platform, "all")
      |> assign(:report_data, %{})
      |> assign(:realtime_data, %{})
      |> assign(:online_stats, %{})
      |> assign(:fluid?, true)
      |> load_initial_data()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_info(:refresh_realtime, socket) do
    # 设置下次刷新
    Process.send_after(self(), :refresh_realtime, 5_000)

    socket =
      socket
      |> load_realtime_data()
      |> load_online_statistics()

    {:noreply, socket}
  end

  @impl true
  def handle_event("change_report_type", %{"type" => type}, socket) do
    socket =
      socket
      |> assign(:selected_report_type, type)
      |> assign(:loading, true)
      |> load_report_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("change_date", %{"date" => date_str}, socket) do
    case Date.from_iso8601(date_str) do
      {:ok, date} ->
        socket =
          socket
          |> assign(:selected_date, date)
          |> assign(:loading, true)
          |> load_report_data()

        {:noreply, socket}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "无效的日期格式")}
    end
  end

  @impl true
  def handle_event("change_month", %{"year" => year_str, "month" => month_str}, socket) do
    with {year, ""} <- Integer.parse(year_str),
         {month, ""} <- Integer.parse(month_str),
         {:ok, date} <- Date.new(year, month, 1) do

      socket =
        socket
        |> assign(:selected_month, date)
        |> assign(:loading, true)
        |> load_report_data()

      {:noreply, socket}
    else
      _ ->
        {:noreply, put_flash(socket, :error, "无效的年月格式")}
    end
  end

  @impl true
  def handle_event("change_platform", %{"platform" => platform}, socket) do
    socket =
      socket
      |> assign(:selected_platform, platform)
      |> assign(:loading, true)
      |> load_report_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    socket =
      socket
      |> assign(:loading, true)
      |> load_report_data()
      |> load_realtime_data()
      |> load_online_statistics()
      |> put_flash(:info, "报表数据已刷新")

    {:noreply, socket}
  end

  @impl true
  def handle_event("export", _params, socket) do
    # 实现导出功能
    {:noreply, put_flash(socket, :info, "导出功能开发中")}
  end

  # 加载初始数据
  defp load_initial_data(socket) do
    socket
    |> load_report_data()
    |> load_realtime_data()
    |> load_online_statistics()
  end

  # 加载报表数据
  defp load_report_data(socket) do
    report_type = socket.assigns.selected_report_type
    selected_date = socket.assigns.selected_date
    selected_month = socket.assigns.selected_month
    selected_platform = socket.assigns.selected_platform

    report_data = case report_type do
      "channel" ->
        load_channel_reports(selected_date)

      "system_daily" ->
        load_system_daily_report(selected_date)

      "system_monthly" ->
        load_system_monthly_report(selected_month)

      "platform_daily" ->
        load_platform_daily_reports(selected_date, selected_platform)

      "platform_monthly" ->
        load_platform_monthly_reports(selected_month, selected_platform)

      _ ->
        %{error: "不支持的报表类型"}
    end

    socket
    |> assign(:report_data, report_data)
    |> assign(:loading, false)
  end

  # 加载实时数据
  defp load_realtime_data(socket) do
    case Teen.Statistics.SystemSummaryReport.get_realtime_data() do
      {:ok, data} ->
        assign(socket, :realtime_data, data)

      {:error, _} ->
        assign(socket, :realtime_data, %{error: "获取实时数据失败"})
    end
  end

  # 加载在线统计
  defp load_online_statistics(socket) do
    case Teen.Statistics.SystemSummaryReport.get_online_statistics() do
      {:ok, stats} ->
        assign(socket, :online_stats, stats)

      {:error, _} ->
        assign(socket, :online_stats, %{error: "获取在线统计失败"})
    end
  end

  # 加载渠道报表
  defp load_channel_reports(date) do
    case Teen.Statistics.ChannelReport.get_all_channel_reports(%{report_date: date}) do
      {:ok, reports} ->
        %{
          type: "channel",
          data: reports,
          summary: calculate_channel_summary(reports)
        }

      {:error, reason} ->
        %{type: "channel", error: reason}
    end
  end

  # 加载系统日报表
  defp load_system_daily_report(date) do
    case Teen.Statistics.SystemSummaryReport.generate_system_daily_report(%{report_date: date}) do
      {:ok, report} ->
        %{
          type: "system_daily",
          data: report
        }

      {:error, reason} ->
        %{type: "system_daily", error: reason}
    end
  end

  # 加载系统月报表
  defp load_system_monthly_report(month_date) do
    case Teen.Statistics.SystemSummaryReport.generate_system_monthly_report(%{
      year: month_date.year,
      month: month_date.month
    }) do
      {:ok, report} ->
        %{
          type: "system_monthly",
          data: report
        }

      {:error, reason} ->
        %{type: "system_monthly", error: reason}
    end
  end

  # 加载平台日报表
  defp load_platform_daily_reports(date, platform) do
    # 这里应该按平台维度加载报表数据
    # 简化处理，返回基础结构
    %{
      type: "platform_daily",
      data: [],
      platform: platform,
      date: date
    }
  end

  # 加载平台月报表
  defp load_platform_monthly_reports(month_date, platform) do
    # 这里应该按平台维度加载月报表数据
    # 简化处理，返回基础结构
    %{
      type: "platform_monthly",
      data: [],
      platform: platform,
      month: month_date
    }
  end

  # 计算渠道报表汇总
  defp calculate_channel_summary(reports) do
    if Enum.empty?(reports) do
      %{
        total_channels: 0,
        total_users: 0,
        total_revenue: Decimal.new(0),
        active_channels: 0
      }
    else
      %{
        total_channels: length(reports),
        total_users: Enum.reduce(reports, 0, fn report, acc -> acc + (report.active_users || 0) end),
        total_revenue: Enum.reduce(reports, Decimal.new(0), fn report, acc ->
          Decimal.add(acc, report.total_recharge_amount || Decimal.new(0))
        end),
        active_channels: Enum.count(reports, fn report -> (report.active_users || 0) > 0 end)
      }
    end
  end

  # 格式化货币
  defp format_currency(amount) when is_struct(amount, Decimal) do
    "¥#{Decimal.round(amount, 2)}"
  end

  defp format_currency(amount) when is_number(amount) do
    "¥#{:erlang.float_to_binary(amount / 1.0, decimals: 2)}"
  end

  defp format_currency(_), do: "¥0.00"

  # 格式化百分比
  defp format_percentage(value) when is_struct(value, Decimal) do
    "#{Decimal.round(value, 2)}%"
  end

  defp format_percentage(value) when is_number(value) do
    "#{:erlang.float_to_binary(value / 1.0, decimals: 2)}%"
  end

  defp format_percentage(_), do: "0%"

  # 获取报表类型选项
  defp get_report_type_options do
    [
      {"渠道报表", "channel"},
      {"系统日报表", "system_daily"},
      {"系统月报表", "system_monthly"},
      {"平台日报表", "platform_daily"},
      {"平台月报表", "platform_monthly"}
    ]
  end

  # 获取平台选项
  defp get_platform_options do
    [
      {"全部平台", "all"},
      {"Android", "android"},
      {"iOS", "ios"},
      {"Web", "web"},
      {"PC", "pc"}
    ]
  end

  # 渲染渠道报表
  defp render_channel_reports(assigns) do
    ~H"""
    <div class="p-6">
      <%= if Map.has_key?(@report_data, :error) do %>
        <div class="alert alert-error">
          <.icon name="hero-exclamation-triangle" class="w-6 h-6" />
          <span>加载渠道报表失败: <%= @report_data.error %></span>
        </div>
      <% else %>
        <!-- 渠道报表汇总 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div class="stat">
            <div class="stat-title">总渠道数</div>
            <div class="stat-value"><%= @report_data.summary.total_channels %></div>
          </div>
          <div class="stat">
            <div class="stat-title">活跃渠道</div>
            <div class="stat-value"><%= @report_data.summary.active_channels %></div>
          </div>
          <div class="stat">
            <div class="stat-title">总用户数</div>
            <div class="stat-value"><%= @report_data.summary.total_users %></div>
          </div>
          <div class="stat">
            <div class="stat-title">总收入</div>
            <div class="stat-value"><%= format_currency(@report_data.summary.total_revenue) %></div>
          </div>
        </div>

        <!-- 渠道报表详细数据 -->
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr class="bg-base-200">
                <th>渠道ID</th>
                <th>渠道名称</th>
                <th class="text-right">新增设备</th>
                <th class="text-right">新增注册</th>
                <th class="text-right">有效新增</th>
                <th class="text-right">活跃用户</th>
                <th class="text-right">充值人数</th>
                <th class="text-right">充值金额</th>
                <th class="text-right">付费率</th>
                <th class="text-right">ARPU</th>
                <th class="text-right">次留</th>
              </tr>
            </thead>
            <tbody>
              <%= if Enum.empty?(@report_data.data) do %>
                <tr>
                  <td colspan="11" class="text-center py-8 text-gray-500">
                    <.icon name="hero-inbox" class="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <div>暂无渠道报表数据</div>
                  </td>
                </tr>
              <% else %>
                <%= for report <- @report_data.data do %>
                  <tr class="hover">
                    <td class="font-mono"><%= report.channel_id %></td>
                    <td class="font-medium"><%= report.channel_name %></td>
                    <td class="text-right"><%= report.new_devices || 0 %></td>
                    <td class="text-right">
                      <span class="badge badge-info badge-sm"><%= report.new_registrations || 0 %></span>
                    </td>
                    <td class="text-right">
                      <span class="badge badge-success badge-sm"><%= report.valid_new_users || 0 %></span>
                    </td>
                    <td class="text-right font-medium"><%= report.active_users || 0 %></td>
                    <td class="text-right"><%= report.paying_users || 0 %></td>
                    <td class="text-right font-medium text-success">
                      <%= format_currency(report.total_recharge_amount || 0) %>
                    </td>
                    <td class="text-right">
                      <%= if report.payment_rate do %>
                        <span class="badge badge-outline"><%= format_percentage(report.payment_rate) %></span>
                      <% else %>
                        -
                      <% end %>
                    </td>
                    <td class="text-right">
                      <%= if report.arpu do %>
                        <%= format_currency(report.arpu) %>
                      <% else %>
                        -
                      <% end %>
                    </td>
                    <td class="text-right">
                      <%= if report.next_day_retention do %>
                        <span class="badge badge-outline"><%= format_percentage(report.next_day_retention) %></span>
                      <% else %>
                        -
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>
      <% end %>
    </div>
    """
  end

  # 渲染系统日报表
  defp render_system_daily_report(assigns) do
    ~H"""
    <div class="p-6">
      <%= if Map.has_key?(@report_data, :error) do %>
        <div class="alert alert-error">
          <.icon name="hero-exclamation-triangle" class="w-6 h-6" />
          <span>加载系统日报表失败: <%= @report_data.error %></span>
        </div>
      <% else %>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 用户指标 -->
          <div class="stats shadow">
            <div class="stat">
              <div class="stat-title">新增设备</div>
              <div class="stat-value"><%= @report_data.data.new_devices || 0 %></div>
            </div>
            <div class="stat">
              <div class="stat-title">新增注册</div>
              <div class="stat-value"><%= @report_data.data.new_registrations || 0 %></div>
            </div>
            <div class="stat">
              <div class="stat-title">有效新增</div>
              <div class="stat-value"><%= @report_data.data.valid_new_users || 0 %></div>
            </div>
            <div class="stat">
              <div class="stat-title">活跃用户</div>
              <div class="stat-value"><%= @report_data.data.active_users || 0 %></div>
            </div>
          </div>

          <!-- 收入指标 -->
          <div class="stats shadow">
            <div class="stat">
              <div class="stat-title">充值人数</div>
              <div class="stat-value"><%= @report_data.data.paying_users || 0 %></div>
            </div>
            <div class="stat">
              <div class="stat-title">新增充值额</div>
              <div class="stat-value"><%= format_currency(@report_data.data.new_recharge_amount || 0) %></div>
            </div>
            <div class="stat">
              <div class="stat-title">付费率</div>
              <div class="stat-value"><%= format_percentage(@report_data.data.payment_rate || 0) %></div>
            </div>
            <div class="stat">
              <div class="stat-title">ARPU</div>
              <div class="stat-value"><%= format_currency(@report_data.data.arpu || 0) %></div>
            </div>
          </div>

          <!-- 留存指标 -->
          <div class="stats shadow">
            <div class="stat">
              <div class="stat-title">次日留存</div>
              <div class="stat-value"><%= format_percentage(@report_data.data.next_day_retention || 0) %></div>
            </div>
            <div class="stat">
              <div class="stat-title">3日留存</div>
              <div class="stat-value"><%= format_percentage(@report_data.data.day3_retention || 0) %></div>
            </div>
            <div class="stat">
              <div class="stat-title">7日留存</div>
              <div class="stat-value"><%= format_percentage(@report_data.data.day7_retention || 0) %></div>
            </div>
            <div class="stat">
              <div class="stat-title">30日留存</div>
              <div class="stat-value"><%= format_percentage(@report_data.data.day30_retention || 0) %></div>
            </div>
          </div>

          <!-- 其他指标 -->
          <div class="stats shadow">
            <div class="stat">
              <div class="stat-title">退充比</div>
              <div class="stat-value"><%= format_percentage(@report_data.data.withdrawal_recharge_ratio || 0) %></div>
            </div>
            <div class="stat">
              <div class="stat-title">游戏局数</div>
              <div class="stat-value"><%= @report_data.data.games_played_today || 0 %></div>
            </div>
            <div class="stat">
              <div class="stat-title">平均会话</div>
              <div class="stat-value">
                <%= if @report_data.data.avg_session_time do %>
                  <%= Decimal.round(@report_data.data.avg_session_time, 1) %>分钟
                <% else %>
                  0分钟
                <% end %>
              </div>
            </div>
            <div class="stat">
              <div class="stat-title">渠道数</div>
              <div class="stat-value"><%= @report_data.data.total_channels || 0 %></div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # 渲染系统月报表
  defp render_system_monthly_report(assigns) do
    report_data = assigns.report_data

    ~H"""
    <div class="p-6">
      <%= if Map.has_key?(report_data, :error) do %>
        <div class="alert alert-error">
          <.icon name="hero-exclamation-triangle" class="w-6 h-6" />
          <span>加载系统月报表失败: <%= report_data.error %></span>
        </div>
      <% else %>
        <div class="text-center py-8">
          <.icon name="hero-chart-bar" class="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h3 class="text-lg font-semibold mb-2">系统月报表</h3>
          <p class="text-gray-600">月度数据汇总功能开发中...</p>
        </div>
      <% end %>
    </div>
    """
  end

  # 渲染平台日报表
  defp render_platform_daily_reports(assigns) do
    report_data = assigns.report_data

    ~H"""
    <div class="p-6">
      <div class="text-center py-8">
        <.icon name="hero-device-phone-mobile" class="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 class="text-lg font-semibold mb-2">平台日报表</h3>
        <p class="text-gray-600">平台: <%= report_data.platform %></p>
        <p class="text-gray-600">日期: <%= report_data.date %></p>
        <p class="text-gray-600 mt-4">平台维度报表功能开发中...</p>
      </div>
    </div>
    """
  end

  # 渲染平台月报表
  defp render_platform_monthly_reports(assigns) do
    report_data = assigns.report_data

    ~H"""
    <div class="p-6">
      <div class="text-center py-8">
        <.icon name="hero-calendar-days" class="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 class="text-lg font-semibold mb-2">平台月报表</h3>
        <p class="text-gray-600">平台: <%= report_data.platform %></p>
        <p class="text-gray-600">月份: <%= report_data.month.year %>年<%= report_data.month.month %>月</p>
        <p class="text-gray-600 mt-4">平台月度报表功能开发中...</p>
      </div>
    </div>
    """
  end
end
