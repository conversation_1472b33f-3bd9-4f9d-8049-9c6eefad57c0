<div class="container mx-auto p-6">
  <!-- 页面标题和控制栏 -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">报表中心</h1>
      <p class="text-gray-600 mt-1">全面的数据统计和分析报表</p>
    </div>
    
    <div class="flex gap-3">
      <!-- 刷新按钮 -->
      <button 
        class="btn btn-primary"
        phx-click="refresh"
        disabled={@loading}
      >
        <.icon name="hero-arrow-path" class="w-4 h-4" />
        <%= if @loading, do: "刷新中...", else: "刷新" %>
      </button>
      
      <!-- 导出按钮 -->
      <button 
        class="btn btn-secondary"
        phx-click="export"
      >
        <.icon name="hero-document-arrow-down" class="w-4 h-4" />
        导出
      </button>
    </div>
  </div>

  <!-- 实时数据卡片 -->
  <div class="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-4 gap-6 mb-8">
    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-primary">
        <.icon name="hero-users" class="w-8 h-8" />
      </div>
      <div class="stat-title text-base-content/70">在线用户</div>
      <div class="stat-value text-primary"><%= @online_stats[:online_users] || 0 %></div>
      <div class="stat-desc text-base-content/50">实时统计</div>
    </div>

    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-secondary">
        <.icon name="hero-user-plus" class="w-8 h-8" />
      </div>
      <div class="stat-title text-base-content/70">今日新增</div>
      <div class="stat-value text-secondary"><%= @realtime_data[:new_registrations] || 0 %></div>
      <div class="stat-desc text-base-content/50">注册用户</div>
    </div>

    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-success">
        <.icon name="hero-banknotes" class="w-8 h-8" />
      </div>
      <div class="stat-title text-base-content/70">今日充值</div>
      <div class="stat-value text-success"><%= format_currency(@realtime_data[:new_recharge_amount] || 0) %></div>
      <div class="stat-desc text-base-content/50">充值金额</div>
    </div>

    <div class="stat bg-base-100 shadow rounded-lg">
      <div class="stat-figure text-info">
        <.icon name="hero-puzzle-piece" class="w-8 h-8" />
      </div>
      <div class="stat-title text-base-content/70">今日游戏</div>
      <div class="stat-value text-info"><%= @realtime_data[:games_played_today] || 0 %></div>
      <div class="stat-desc text-base-content/50">游戏局数</div>
    </div>
  </div>

  <!-- 报表类型选择和筛选条件 -->
  <div class="bg-base-100 shadow rounded-lg p-6 mb-8">
    <div class="flex flex-wrap gap-4 items-end">
      <!-- 报表类型选择 -->
      <div class="form-control">
        <label class="label">
          <span class="label-text text-base-content/80 font-medium">报表类型</span>
        </label>
        <select 
          class="select select-bordered"
          phx-change="change_report_type"
          name="type"
        >
          <%= for {label, value} <- get_report_type_options() do %>
            <option value={value} selected={@selected_report_type == value}>
              <%= label %>
            </option>
          <% end %>
        </select>
      </div>

      <!-- 日期选择（日报表用） -->
      <%= if @selected_report_type in ["channel", "system_daily", "platform_daily"] do %>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-base-content/80 font-medium">报表日期</span>
          </label>
          <input 
            type="date" 
            class="input input-bordered"
            value={Date.to_iso8601(@selected_date)}
            phx-change="change_date"
            name="date"
          />
        </div>
      <% end %>

      <!-- 月份选择（月报表用） -->
      <%= if @selected_report_type in ["system_monthly", "platform_monthly"] do %>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-base-content/80 font-medium">报表月份</span>
          </label>
          <div class="flex gap-2">
            <input 
              type="number" 
              class="input input-bordered w-20"
              placeholder="年"
              value={@selected_month.year}
              phx-change="change_month"
              name="year"
              min="2020"
              max="2030"
            />
            <input 
              type="number" 
              class="input input-bordered w-16"
              placeholder="月"
              value={@selected_month.month}
              phx-change="change_month"
              name="month"
              min="1"
              max="12"
            />
          </div>
        </div>
      <% end %>

      <!-- 平台选择（平台报表用） -->
      <%= if @selected_report_type in ["platform_daily", "platform_monthly"] do %>
        <div class="form-control">
          <label class="label">
            <span class="label-text text-base-content/80 font-medium">平台</span>
          </label>
          <select 
            class="select select-bordered"
            phx-change="change_platform"
            name="platform"
          >
            <%= for {label, value} <- get_platform_options() do %>
              <option value={value} selected={@selected_platform == value}>
                <%= label %>
              </option>
            <% end %>
          </select>
        </div>
      <% end %>
    </div>
  </div>

  <!-- 报表内容区域 -->
  <div class="bg-base-100 shadow rounded-lg overflow-hidden">
    <%= if @loading do %>
      <div class="flex justify-center items-center py-12">
        <span class="loading loading-spinner loading-lg"></span>
        <span class="ml-3 text-lg text-base-content/70">加载报表数据中...</span>
      </div>
    <% else %>
      <%= case @selected_report_type do %>
        <% "channel" -> %>
          <%= render_channel_reports(assigns) %>
        <% "system_daily" -> %>
          <%= render_system_daily_report(assigns) %>
        <% "system_monthly" -> %>
          <%= render_system_monthly_report(assigns) %>
        <% "platform_daily" -> %>
          <%= render_platform_daily_reports(assigns) %>
        <% "platform_monthly" -> %>
          <%= render_platform_monthly_reports(assigns) %>
        <% _ -> %>
          <div class="p-8 text-center text-gray-500">
            <.icon name="hero-exclamation-triangle" class="w-12 h-12 mx-auto mb-4 opacity-50" />
            <div>不支持的报表类型</div>
          </div>
      <% end %>
    <% end %>
  </div>
</div>
