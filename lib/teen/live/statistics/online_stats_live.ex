defmodule Teen.Live.Statistics.OnlineStatsLive do
  @moduledoc """
  在线统计页面

  显示用户在线情况的详细统计：
  - 在线用户列表
  - 在线时长统计
  - 地区分布
  - 设备类型分布
  """

  use CypridinaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 设置定时刷新
      Process.send_after(self(), :refresh_stats, 2_000)
    end

    socket =
      socket
      |> assign(:page_title, "在线统计")
      |> assign(:current_url, "/admin/online-stats")
      |> assign(:loading, true)
      |> assign(:online_users, [])
      |> assign(:stats, %{})
      |> assign(:fluid?, true)
      |> load_online_data()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_info(:refresh_stats, socket) do
    # 设置下次刷新
    Process.send_after(self(), :refresh_stats, 10_000)
    
    socket = load_online_data(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    socket =
      socket
      |> assign(:loading, true)
      |> load_online_data()
      |> put_flash(:info, "数据已刷新")

    {:noreply, socket}
  end

  defp load_online_data(socket) do
    online_users = generate_mock_online_users()
    
    stats = %{
      total_online: length(online_users),
      avg_session_time: calculate_avg_session_time(online_users),
      device_distribution: calculate_device_distribution(online_users),
      region_distribution: calculate_region_distribution(online_users),
      last_update: DateTime.utc_now()
    }

    socket
    |> assign(:online_users, online_users)
    |> assign(:stats, stats)
    |> assign(:loading, false)
  end

  defp generate_mock_online_users do
    user_count = :rand.uniform(50) + 20
    
    Enum.map(1..user_count, fn i ->
      online_duration = :rand.uniform(7200) + 300  # 5分钟到2小时
      
      %{
        id: i,
        username: "user#{:rand.uniform(10000)}",
        avatar: nil,
        online_duration: online_duration,
        device_type: Enum.random(["Android", "iOS", "Web", "PC"]),
        region: Enum.random(["北京", "上海", "广州", "深圳", "杭州", "成都", "武汉", "西安"]),
        current_game: if(:rand.uniform(10) > 3, do: Enum.random(["Teen Patti", "Crash", "龙虎斗", "炸金花"]), else: nil),
        last_activity: DateTime.utc_now() |> DateTime.add(-:rand.uniform(300), :second)
      }
    end)
    |> Enum.sort_by(& &1.online_duration, :desc)
  end

  defp calculate_avg_session_time(users) do
    if Enum.empty?(users) do
      0
    else
      total_time = Enum.reduce(users, 0, fn user, acc -> acc + user.online_duration end)
      div(total_time, length(users))
    end
  end

  defp calculate_device_distribution(users) do
    users
    |> Enum.group_by(& &1.device_type)
    |> Enum.map(fn {device, user_list} ->
      {device, length(user_list)}
    end)
    |> Enum.sort_by(fn {_, count} -> count end, :desc)
  end

  defp calculate_region_distribution(users) do
    users
    |> Enum.group_by(& &1.region)
    |> Enum.map(fn {region, user_list} ->
      {region, length(user_list)}
    end)
    |> Enum.sort_by(fn {_, count} -> count end, :desc)
  end

  defp format_duration(seconds) when is_integer(seconds) do
    hours = div(seconds, 3600)
    minutes = div(rem(seconds, 3600), 60)
    remaining_seconds = rem(seconds, 60)

    cond do
      hours > 0 -> "#{hours}小时#{minutes}分钟"
      minutes > 0 -> "#{minutes}分钟#{remaining_seconds}秒"
      true -> "#{remaining_seconds}秒"
    end
  end

  defp format_duration(_), do: "0秒"

  defp format_time(datetime) do
    datetime
    |> DateTime.shift_zone!("Asia/Shanghai")
    |> Calendar.strftime("%H:%M:%S")
  end

  defp get_device_icon(device_type) do
    case device_type do
      "Android" -> "hero-device-phone-mobile"
      "iOS" -> "hero-device-phone-mobile"
      "Web" -> "hero-globe-alt"
      "PC" -> "hero-computer-desktop"
      _ -> "hero-device-phone-mobile"
    end
  end

  defp get_device_color(device_type) do
    case device_type do
      "Android" -> "text-green-600"
      "iOS" -> "text-blue-600"
      "Web" -> "text-purple-600"
      "PC" -> "text-gray-600"
      _ -> "text-gray-600"
    end
  end
end
