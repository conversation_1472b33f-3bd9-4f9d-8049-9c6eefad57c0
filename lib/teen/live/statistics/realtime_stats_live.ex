defmodule Teen.Live.Statistics.RealtimeStatsLive do
  @moduledoc """
  实时统计页面

  显示系统的实时数据，包括：
  - 在线用户数
  - 今日新增用户
  - 今日充值金额
  - 今日游戏局数
  - 实时收入统计
  """

  use CypridinaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 设置定时刷新
      Process.send_after(self(), :refresh_stats, 1_000)
    end

    socket =
      socket
      |> assign(:page_title, "实时统计")
      |> assign(:current_url, "/admin/realtime-stats")
      |> assign(:loading, true)
      |> assign(:stats, %{})
      |> assign(:fluid?, true)
      |> load_realtime_stats()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_info(:refresh_stats, socket) do
    # 设置下次刷新
    Process.send_after(self(), :refresh_stats, 5_000)
    
    socket = load_realtime_stats(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    socket =
      socket
      |> assign(:loading, true)
      |> load_realtime_stats()
      |> put_flash(:info, "数据已刷新")

    {:noreply, socket}
  end

  defp load_realtime_stats(socket) do
    stats = %{
      online_users: get_online_users_count(),
      today_new_users: get_today_new_users(),
      today_revenue: get_today_revenue(),
      today_games: get_today_games_count(),
      active_rooms: get_active_rooms_count(),
      total_users: get_total_users_count(),
      last_update: DateTime.utc_now()
    }

    socket
    |> assign(:stats, stats)
    |> assign(:loading, false)
  end

  defp get_online_users_count do
    # 模拟数据
    :rand.uniform(2000) + 500
  end

  defp get_today_new_users do
    # 模拟数据
    :rand.uniform(200) + 50
  end

  defp get_today_revenue do
    # 模拟数据
    Decimal.new(:rand.uniform(50000) + 10000)
  end

  defp get_today_games_count do
    # 模拟数据
    :rand.uniform(5000) + 1000
  end

  defp get_active_rooms_count do
    # 模拟数据
    :rand.uniform(50) + 10
  end

  defp get_total_users_count do
    # 模拟数据
    :rand.uniform(10000) + 50000
  end

  defp format_currency(amount) when is_struct(amount, Decimal) do
    "¥#{Decimal.round(amount, 2)}"
  end

  defp format_currency(amount) when is_number(amount) do
    "¥#{:erlang.float_to_binary(amount / 1.0, decimals: 2)}"
  end

  defp format_currency(_), do: "¥0.00"

  defp format_time(datetime) do
    datetime
    |> DateTime.shift_zone!("Asia/Shanghai")
    |> Calendar.strftime("%H:%M:%S")
  end
end
