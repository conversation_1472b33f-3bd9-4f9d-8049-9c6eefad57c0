defmodule Teen.Live.Statistics.ChannelReportLive do
  @moduledoc """
  渠道报表管理页面

  基于LiveView实现的渠道报表展示界面
  不使用数据库，动态生成基于渠道包ID的报表数据
  """

  use CypridinaWeb, :live_view
  import Phoenix.Component
  import Phoenix.LiveView

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 设置定时刷新
      Process.send_after(self(), :refresh_data, 30_000)
    end

    socket =
      socket
      |> assign(:page_title, "渠道报表")
      |> assign(:current_url, "/admin/channel-reports")
      |> assign(:loading, true)
      |> assign(:reports, [])
      |> assign(:selected_date, Date.utc_today())
      |> assign(:fluid?, true)
      |> load_channel_reports()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_info(:refresh_data, socket) do
    # 设置下次刷新
    Process.send_after(self(), :refresh_data, 30_000)

    {:noreply, load_channel_reports(socket)}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    {:noreply,
     socket
     |> assign(:loading, true)
     |> load_channel_reports()
     |> put_flash(:info, "报表数据已刷新")}
  end

  @impl true
  def handle_event("export", _params, socket) do
    # 这里可以实现导出功能
    {:noreply, put_flash(socket, :info, "导出功能开发中")}
  end

  @impl true
  def handle_event("change_date", %{"date" => date_str}, socket) do
    case Date.from_iso8601(date_str) do
      {:ok, date} ->
        {:noreply,
         socket
         |> assign(:selected_date, date)
         |> assign(:loading, true)
         |> load_channel_reports()}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "无效的日期格式")}
    end
  end

  defp load_channel_reports(socket) do
    selected_date = socket.assigns[:selected_date] || Date.utc_today()

    case Teen.Statistics.ChannelReport.get_all_channel_reports(%{report_date: selected_date}) do
      {:ok, reports} ->
        socket
        |> assign(:reports, reports)
        |> assign(:loading, false)
        |> assign(:stats, calculate_summary_stats(reports))

      {:error, _reason} ->
        socket
        |> assign(:reports, [])
        |> assign(:loading, false)
        |> assign(:stats, %{total_channels: 0, total_users: 0, total_revenue: Decimal.new(0), active_channels: 0})
        |> put_flash(:error, "加载报表数据失败")
    end
  end

  defp calculate_summary_stats(reports) do
    total_channels = length(reports)
    total_users = Enum.reduce(reports, 0, fn report, acc -> acc + (report.total_users || 0) end)
    total_revenue = Enum.reduce(reports, Decimal.new(0), fn report, acc ->
      Decimal.add(acc, report.total_revenue || Decimal.new(0))
    end)
    active_channels = Enum.count(reports, fn report -> (report.active_users || 0) > 0 end)

    %{
      total_channels: total_channels,
      total_users: total_users,
      total_revenue: total_revenue,
      active_channels: active_channels
    }
  end

  defp format_currency(amount) when is_struct(amount, Decimal) do
    "¥#{Decimal.round(amount, 2)}"
  end

  defp format_currency(amount) when is_number(amount) do
    "¥#{:erlang.float_to_binary(amount / 1.0, decimals: 2)}"
  end

  defp format_currency(_), do: "¥0.00"

  defp format_percentage(value) when is_struct(value, Decimal) do
    "#{Decimal.round(value, 2)}%"
  end

  defp format_percentage(value) when is_number(value) do
    "#{:erlang.float_to_binary(value / 1.0, decimals: 2)}%"
  end

  defp format_percentage(_), do: "0%"
end
