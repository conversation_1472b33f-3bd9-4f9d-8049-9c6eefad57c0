<div class="container mx-auto p-6">
  <!-- 页面标题和控制栏 -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">实时统计</h1>
      <p class="text-gray-600 mt-1">系统实时数据监控</p>
    </div>
    
    <div class="flex gap-3 items-center">
      <!-- 最后更新时间 -->
      <div class="text-sm text-gray-500">
        最后更新: <%= if @stats[:last_update], do: format_time(@stats.last_update), else: "--" %>
      </div>
      
      <!-- 刷新按钮 -->
      <button 
        class="btn btn-primary"
        phx-click="refresh"
        disabled={@loading}
      >
        <.icon name="hero-arrow-path" class="w-4 h-4" />
        <%= if @loading, do: "刷新中...", else: "刷新" %>
      </button>
    </div>
  </div>

  <!-- 实时数据卡片 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
    <!-- 在线用户 -->
    <div class="stat bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg rounded-xl border border-blue-200">
      <div class="stat-figure text-blue-600">
        <.icon name="hero-users" class="w-10 h-10" />
      </div>
      <div class="stat-title text-blue-800">在线用户</div>
      <div class="stat-value text-blue-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= @stats[:online_users] || 0 %>
        <% end %>
      </div>
      <div class="stat-desc text-blue-600">实时在线</div>
    </div>

    <!-- 今日新增 -->
    <div class="stat bg-gradient-to-br from-green-50 to-green-100 shadow-lg rounded-xl border border-green-200">
      <div class="stat-figure text-green-600">
        <.icon name="hero-user-plus" class="w-10 h-10" />
      </div>
      <div class="stat-title text-green-800">今日新增</div>
      <div class="stat-value text-green-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= @stats[:today_new_users] || 0 %>
        <% end %>
      </div>
      <div class="stat-desc text-green-600">新注册用户</div>
    </div>

    <!-- 今日收入 -->
    <div class="stat bg-gradient-to-br from-yellow-50 to-yellow-100 shadow-lg rounded-xl border border-yellow-200">
      <div class="stat-figure text-yellow-600">
        <.icon name="hero-banknotes" class="w-10 h-10" />
      </div>
      <div class="stat-title text-yellow-800">今日收入</div>
      <div class="stat-value text-yellow-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= format_currency(@stats[:today_revenue] || 0) %>
        <% end %>
      </div>
      <div class="stat-desc text-yellow-600">充值金额</div>
    </div>

    <!-- 今日游戏 -->
    <div class="stat bg-gradient-to-br from-purple-50 to-purple-100 shadow-lg rounded-xl border border-purple-200">
      <div class="stat-figure text-purple-600">
        <.icon name="hero-puzzle-piece" class="w-10 h-10" />
      </div>
      <div class="stat-title text-purple-800">今日游戏</div>
      <div class="stat-value text-purple-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= @stats[:today_games] || 0 %>
        <% end %>
      </div>
      <div class="stat-desc text-purple-600">游戏局数</div>
    </div>

    <!-- 活跃房间 -->
    <div class="stat bg-gradient-to-br from-red-50 to-red-100 shadow-lg rounded-xl border border-red-200">
      <div class="stat-figure text-red-600">
        <.icon name="hero-building-office" class="w-10 h-10" />
      </div>
      <div class="stat-title text-red-800">活跃房间</div>
      <div class="stat-value text-red-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= @stats[:active_rooms] || 0 %>
        <% end %>
      </div>
      <div class="stat-desc text-red-600">正在游戏</div>
    </div>

    <!-- 总用户数 -->
    <div class="stat bg-gradient-to-br from-indigo-50 to-indigo-100 shadow-lg rounded-xl border border-indigo-200">
      <div class="stat-figure text-indigo-600">
        <.icon name="hero-user-group" class="w-10 h-10" />
      </div>
      <div class="stat-title text-indigo-800">总用户数</div>
      <div class="stat-value text-indigo-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= @stats[:total_users] || 0 %>
        <% end %>
      </div>
      <div class="stat-desc text-indigo-600">累计注册</div>
    </div>
  </div>

  <!-- 系统状态 -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- 系统健康状态 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title flex items-center gap-2">
          <.icon name="hero-heart" class="w-6 h-6 text-success" />
          系统健康状态
        </h2>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">服务器状态</span>
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span class="text-sm text-success font-medium">正常</span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">数据库连接</span>
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span class="text-sm text-success font-medium">正常</span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">缓存服务</span>
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span class="text-sm text-success font-medium">正常</span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">游戏服务</span>
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span class="text-sm text-success font-medium">正常</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title flex items-center gap-2">
          <.icon name="hero-bolt" class="w-6 h-6 text-warning" />
          快速操作
        </h2>
        
        <div class="grid grid-cols-2 gap-3">
          <.link
            navigate={~p"/admin/reports"}
            class="btn btn-outline btn-primary btn-sm"
          >
            <.icon name="hero-chart-bar" class="w-4 h-4" />
            查看报表
          </.link>
          
          <.link
            navigate={~p"/admin/users"}
            class="btn btn-outline btn-secondary btn-sm"
          >
            <.icon name="hero-users" class="w-4 h-4" />
            用户管理
          </.link>
          
          <.link
            navigate={~p"/admin/games"}
            class="btn btn-outline btn-accent btn-sm"
          >
            <.icon name="hero-puzzle-piece" class="w-4 h-4" />
            游戏管理
          </.link>
          
          <.link
            navigate={~p"/admin/payment-orders"}
            class="btn btn-outline btn-info btn-sm"
          >
            <.icon name="hero-credit-card" class="w-4 h-4" />
            支付订单
          </.link>
        </div>
      </div>
    </div>
  </div>
</div>
