<div class="container mx-auto p-6">
  <!-- 页面标题和控制栏 -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">在线统计</h1>
      <p class="text-gray-600 mt-1">用户在线情况详细统计</p>
    </div>
    
    <div class="flex gap-3 items-center">
      <!-- 最后更新时间 -->
      <div class="text-sm text-gray-500">
        最后更新: <%= if @stats[:last_update], do: format_time(@stats.last_update), else: "--" %>
      </div>
      
      <!-- 刷新按钮 -->
      <button 
        class="btn btn-primary"
        phx-click="refresh"
        disabled={@loading}
      >
        <.icon name="hero-arrow-path" class="w-4 h-4" />
        <%= if @loading, do: "刷新中...", else: "刷新" %>
      </button>
    </div>
  </div>

  <!-- 统计概览 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- 总在线用户 -->
    <div class="stat bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg rounded-xl border border-blue-200">
      <div class="stat-figure text-blue-600">
        <.icon name="hero-users" class="w-10 h-10" />
      </div>
      <div class="stat-title text-blue-800">在线用户</div>
      <div class="stat-value text-blue-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= @stats[:total_online] || 0 %>
        <% end %>
      </div>
      <div class="stat-desc text-blue-600">当前在线</div>
    </div>

    <!-- 平均在线时长 -->
    <div class="stat bg-gradient-to-br from-green-50 to-green-100 shadow-lg rounded-xl border border-green-200">
      <div class="stat-figure text-green-600">
        <.icon name="hero-clock" class="w-10 h-10" />
      </div>
      <div class="stat-title text-green-800">平均时长</div>
      <div class="stat-value text-green-900 text-lg">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= format_duration(@stats[:avg_session_time] || 0) %>
        <% end %>
      </div>
      <div class="stat-desc text-green-600">会话时长</div>
    </div>

    <!-- 游戏中用户 -->
    <div class="stat bg-gradient-to-br from-purple-50 to-purple-100 shadow-lg rounded-xl border border-purple-200">
      <div class="stat-figure text-purple-600">
        <.icon name="hero-puzzle-piece" class="w-10 h-10" />
      </div>
      <div class="stat-title text-purple-800">游戏中</div>
      <div class="stat-value text-purple-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= Enum.count(@online_users, fn user -> user.current_game != nil end) %>
        <% end %>
      </div>
      <div class="stat-desc text-purple-600">正在游戏</div>
    </div>

    <!-- 空闲用户 -->
    <div class="stat bg-gradient-to-br from-yellow-50 to-yellow-100 shadow-lg rounded-xl border border-yellow-200">
      <div class="stat-figure text-yellow-600">
        <.icon name="hero-pause" class="w-10 h-10" />
      </div>
      <div class="stat-title text-yellow-800">空闲用户</div>
      <div class="stat-value text-yellow-900">
        <%= if @loading do %>
          <span class="loading loading-dots loading-md"></span>
        <% else %>
          <%= Enum.count(@online_users, fn user -> user.current_game == nil end) %>
        <% end %>
      </div>
      <div class="stat-desc text-yellow-600">未在游戏</div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- 设备类型分布 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title flex items-center gap-2">
          <.icon name="hero-device-phone-mobile" class="w-6 h-6 text-info" />
          设备类型分布
        </h2>
        
        <div class="space-y-3">
          <%= for {device, count} <- @stats[:device_distribution] || [] do %>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <.icon name={get_device_icon(device)} class={"w-5 h-5 #{get_device_color(device)}"} />
                <span class="font-medium"><%= device %></span>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-sm font-bold"><%= count %></span>
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-info h-2 rounded-full" 
                    style={"width: #{if @stats[:total_online] > 0, do: (count / @stats[:total_online] * 100), else: 0}%"}
                  ></div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- 地区分布 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title flex items-center gap-2">
          <.icon name="hero-map-pin" class="w-6 h-6 text-success" />
          地区分布
        </h2>
        
        <div class="space-y-3">
          <%= for {region, count} <- @stats[:region_distribution] || [] do %>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <.icon name="hero-map-pin" class="w-4 h-4 text-success" />
                <span class="font-medium"><%= region %></span>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-sm font-bold"><%= count %></span>
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-success h-2 rounded-full" 
                    style={"width: #{if @stats[:total_online] > 0, do: (count / @stats[:total_online] * 100), else: 0}%"}
                  ></div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- 活动状态 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title flex items-center gap-2">
          <.icon name="hero-chart-pie" class="w-6 h-6 text-warning" />
          活动状态
        </h2>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">游戏中</span>
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-success rounded-full"></div>
              <span class="text-sm font-bold text-success">
                <%= Enum.count(@online_users, fn user -> user.current_game != nil end) %>
              </span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">大厅中</span>
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-warning rounded-full"></div>
              <span class="text-sm font-bold text-warning">
                <%= Enum.count(@online_users, fn user -> user.current_game == nil end) %>
              </span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">活跃用户</span>
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-info rounded-full"></div>
              <span class="text-sm font-bold text-info">
                <%= Enum.count(@online_users, fn user -> 
                  DateTime.diff(DateTime.utc_now(), user.last_activity, :second) < 300 
                end) %>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 在线用户列表 -->
  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title flex items-center gap-2">
        <.icon name="hero-user-group" class="w-6 h-6 text-primary" />
        在线用户列表
      </h2>
      
      <div class="overflow-x-auto">
        <table class="table table-zebra w-full">
          <thead>
            <tr class="bg-base-200">
              <th>用户名</th>
              <th>设备类型</th>
              <th>地区</th>
              <th>在线时长</th>
              <th>当前状态</th>
              <th>最后活动</th>
            </tr>
          </thead>
          <tbody>
            <%= if @loading do %>
              <tr>
                <td colspan="6" class="text-center py-8">
                  <span class="loading loading-spinner loading-lg"></span>
                  <div class="mt-2">加载中...</div>
                </td>
              </tr>
            <% else %>
              <%= if Enum.empty?(@online_users) do %>
                <tr>
                  <td colspan="6" class="text-center py-8 text-gray-500">
                    <.icon name="hero-inbox" class="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <div>暂无在线用户</div>
                  </td>
                </tr>
              <% else %>
                <%= for user <- Enum.take(@online_users, 50) do %>
                  <tr class="hover">
                    <td>
                      <div class="flex items-center gap-3">
                        <div class="avatar placeholder">
                          <div class="bg-primary text-primary-content rounded-full w-8 h-8">
                            <span class="text-xs"><%= String.first(user.username) %></span>
                          </div>
                        </div>
                        <span class="font-medium"><%= user.username %></span>
                      </div>
                    </td>
                    <td>
                      <div class="flex items-center gap-2">
                        <.icon name={get_device_icon(user.device_type)} class={"w-4 h-4 #{get_device_color(user.device_type)}"} />
                        <span><%= user.device_type %></span>
                      </div>
                    </td>
                    <td>
                      <div class="flex items-center gap-2">
                        <.icon name="hero-map-pin" class="w-4 h-4 text-gray-500" />
                        <span><%= user.region %></span>
                      </div>
                    </td>
                    <td>
                      <span class="font-mono text-sm"><%= format_duration(user.online_duration) %></span>
                    </td>
                    <td>
                      <%= if user.current_game do %>
                        <span class="badge badge-success badge-sm">
                          <.icon name="hero-puzzle-piece" class="w-3 h-3 mr-1" />
                          <%= user.current_game %>
                        </span>
                      <% else %>
                        <span class="badge badge-ghost badge-sm">
                          <.icon name="hero-home" class="w-3 h-3 mr-1" />
                          大厅
                        </span>
                      <% end %>
                    </td>
                    <td>
                      <span class="text-xs text-gray-500">
                        <%= format_time(user.last_activity) %>
                      </span>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            <% end %>
          </tbody>
        </table>
      </div>
      
      <%= if length(@online_users) > 50 do %>
        <div class="text-center mt-4">
          <div class="text-sm text-gray-500">
            显示前50个用户，共 <%= length(@online_users) %> 个在线用户
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
