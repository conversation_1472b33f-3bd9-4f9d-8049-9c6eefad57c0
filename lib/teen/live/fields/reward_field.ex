defmodule Teen.Live.Fields.RewardField do
  @config_schema []

  @moduledoc """
  奖励字段，使用固定的配置
  """
  use Backpex.Field, config_schema: @config_schema
  require Backpex

  # 固定的子字段配置
  @fixed_child_fields [
    type: %{
      module: Backpex.Fields.Select,
      label: "奖励类型",
      options: [
        {"金币", :coins},
        # {"道具", :items},
        # {"VIP经验", :vip_experience}
      ],
      required: true
    },
    amount: %{
      module: Backpex.Fields.Number,
      label: "奖励数量",
      required: true
    },
    description: %{
      module: Backpex.Fields.Text,
      label: "描述",
      placeholder: "奖励描述（可选）"
    }
  ]

  @impl Phoenix.LiveComponent
  def update(assigns, socket) do
    # 固定配置
    field_options = Map.merge(assigns.field_options, %{
      type: :embed,
      child_fields: @fixed_child_fields
    })

    assigns = Map.put(assigns, :field_options, field_options)

    socket
    |> assign(assigns)
    |> assign(:child_fields, @fixed_child_fields)
    |> ok()
  end

  @impl Backpex.Field
  def render_value(assigns) do
    ~H"""
    <div class="ring-base-content/10 rounded-box overflow-x-auto ring-1">
      <table class="table">
        <thead class="bg-base-200/50 text-base-content uppercase">
          <tr>
            <th :for={{_name, %{label: label}} <- @child_fields} class="font-medium">
              {label}
            </th>
          </tr>
        </thead>
        <tbody class="text-base-content/75">
          <tr :for={row <- @value} class="border-base-content/10 border-b last:border-b-0">
            <td :for={{name, _field_options} <- @child_fields}>
              {HTML.pretty_value(Map.get(row, name))}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    """
  end

  @impl Backpex.Field
  def render_form(assigns) do
    assigns =
      assigns
      |> assign(:child_fields, assigns.field_options.child_fields)

    ~H"""
    <div>
      <Layout.field_container>
        <:label align={Backpex.Field.align_label(@field_options, assigns, :top)}>
          <Layout.input_label text={@field_options[:label]} />
        </:label>

        <div class="flex flex-col">
          <.inputs_for :let={f_nested} field={@form[@name]}>
            <div class="mb-3 flex items-start gap-x-4">
              <div
                :for={{child_field_name, child_field_options} <- @child_fields}
                class={child_field_class(child_field_options, assigns)}
              >
                <p :if={f_nested.index == 0} class="mb-1 text-xs">
                  {child_field_options.label}
                </p>

                <%= if child_field_options.module == Backpex.Fields.Select do %>
                  <BackpexForm.input
                    type="select"
                    field={f_nested[child_field_name]}
                    options={child_field_options[:options] || []}
                    prompt={child_field_options[:prompt]}
                    translate_error_fun={Backpex.Field.translate_error_fun(child_field_options, assigns)}
                    phx-debounce={Backpex.Field.debounce(child_field_options, assigns)}
                    phx-throttle={Backpex.Field.throttle(child_field_options, assigns)}
                  />
                <% else %>
                  <BackpexForm.input
                    type={input_type(child_field_options) |> Atom.to_string()}
                    field={f_nested[child_field_name]}
                    translate_error_fun={Backpex.Field.translate_error_fun(child_field_options, assigns)}
                    phx-debounce={Backpex.Field.debounce(child_field_options, assigns)}
                    phx-throttle={Backpex.Field.throttle(child_field_options, assigns)}
                  />
                <% end %>
              </div>

              <div class={if f_nested.index == 0, do: "mt-5", else: nil}>
                <label for={"#{@name}-checkbox-delete-#{f_nested.index}"}>
                  <input
                    id={"#{@name}-checkbox-delete-#{f_nested.index}"}
                    type="checkbox"
                    name={"#{@form.name}[_drop_#{@name}][]"}
                    value={f_nested.index}
                    class="hidden"
                  />

                  <div class="btn btn-outline btn-error" aria-label={Backpex.__("Delete", @live_resource)}>
                    <Backpex.HTML.CoreComponents.icon name="hero-trash" class="h-5 w-5" />
                  </div>
                </label>
              </div>
            </div>
          </.inputs_for>
        </div>

        <input
          name={"#{@form.name}[_add_#{@name}]"}
          type="checkbox"
          value="end"
          aria-label={Backpex.__("Add entry", @live_resource)}
          class="btn btn-outline btn-sm btn-primary"
        />

        <%= if help_text = Backpex.Field.help_text(@field_options, assigns) do %>
          <Backpex.HTML.Form.help_text class="mt-1">{help_text}</Backpex.HTML.Form.help_text>
        <% end %>
      </Layout.field_container>
    </div>
    """
  end

  @impl Backpex.Field
  def association?({_name, %{type: :assoc}} = _field), do: true
  def association?({_name, %{type: :embed}} = _field), do: false
  def association?(_field), do: false

  @impl Backpex.Field
  def schema({name, _field_options}, schema) do
    schema.__schema__(:association, name)
    |> Map.get(:queryable)
  end

  # 添加缺失的辅助函数
  defp input_type(%{module: Backpex.Fields.Number}), do: :number
  defp input_type(%{module: Backpex.Fields.Text}), do: :text
  defp input_type(%{module: Backpex.Fields.Textarea}), do: :textarea
  defp input_type(%{module: Backpex.Fields.Select}), do: :select
  defp input_type(_), do: :text

  defp child_field_class(_child_field_options, _assigns) do
    "flex-1"
  end

end
