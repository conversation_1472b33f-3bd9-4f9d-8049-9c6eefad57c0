defmodule Teen.Live.Fields.RewardDistributionField do
  @config_schema []
  
  @moduledoc """
  奖励分发方式字段组件
  
  支持两种模式：
  - direct: 直接领取模式 (SlotCat风格)
  - flip_card: 翻牌领取模式 (<PERSON> Patti风格)
  """
  
  use Backpex.Field, config_schema: @config_schema
  use Phoenix.LiveComponent
  require Backpex
  alias Phoenix.HTML.Form
  
  @impl Backpex.Field
  def render_value(assigns) do
    ~H"""
    <div class="flex items-center gap-2">
      <%= case @value do %>
        <% %{"type" => "direct"} -> %>
          <div class="badge badge-success badge-sm gap-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            直接领取
          </div>
          <span class="text-xs text-base-content/60">
            <%= @value["config"]["amount"] || 0 %>金币
          </span>
        <% %{"type" => "flip_card"} -> %>
          <div class="badge badge-info badge-sm gap-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            翻牌领取
          </div>
          <span class="text-xs text-base-content/60">
            <%= @value["card_count"] || 5 %>张卡片
          </span>
        <% _ -> %>
          <div class="badge badge-neutral badge-sm">
            未配置
          </div>
      <% end %>
    </div>
    """
  end
  
  @impl Backpex.Field
  def render_form(assigns) do
    # 获取当前表单值，如果为空则使用默认值
    current_value = 
      case Form.input_value(assigns.form, assigns.name) do
        nil -> %{"type" => "direct"}
        value when is_map(value) -> value
        value when is_binary(value) -> 
          case Jason.decode(value) do
            {:ok, decoded} -> decoded
            _ -> %{"type" => "direct"}
          end
        _ -> %{"type" => "direct"}
      end
    
    assigns = assign(assigns, :current_value, current_value)
    
    ~H"""
    <div class="form-control w-full">
      <label class="label">
        <span class="label-text font-semibold flex items-center gap-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
          奖励分发方式
        </span>
        <span class="label-text-alt">选择玩家如何领取奖励</span>
      </label>
      
      <!-- 简化的表单组件 -->
      <.live_component 
        module={__MODULE__.FormComponent}
        id={"#{@name}_component"}
        name={@name}
        form={@form}
        value={@current_value} />
    </div>
    """
  end
  
  defmodule FormComponent do
    use Phoenix.LiveComponent
    alias Phoenix.HTML.Form
    
    @impl Phoenix.LiveComponent
    def render(assigns) do
      ~H"""
      <div>
        <!-- 隐藏的JSON输入字段，这是实际提交的值 -->
        <input type="hidden" 
               name={Form.input_name(@form, @name)} 
               id={"#{@name}_hidden_input"}
               value={Jason.encode!(@value)} />
        
        <!-- 模式选择 -->
        <div class="tabs tabs-boxed mb-4">
          <button type="button" 
                  class={["tab", @value["type"] == "direct" && "tab-active"]}
                  phx-click="select_distribution_type" 
                  phx-value-type="direct"
                  phx-target={@myself}>
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            直接领取
          </button>
          <button type="button" 
                  class={["tab", @value["type"] == "flip_card" && "tab-active"]}
                  phx-click="select_distribution_type" 
                  phx-value-type="flip_card"
                  phx-target={@myself}>
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            翻牌领取
          </button>
        </div>

        <!-- 直接领取说明 -->
        <%= if @value["type"] == "direct" do %>
          <div class="alert alert-success">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <div>
              <div class="font-bold">直接领取模式</div>
              <div class="text-sm">玩家完成任务后，将直接获得在"奖励配置"中设置的奖励</div>
            </div>
          </div>
        <% end %>

        <!-- 翻牌领取配置 -->
        <%= if @value["type"] == "flip_card" do %>
          <div class="card bg-info/10 border border-info/20">
            <div class="card-body p-4">
              <h4 class="card-title text-info text-sm flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                翻牌领取配置
              </h4>
              
              <div class="form-control">
                <label class="label">
                  <span class="label-text">卡片数量</span>
                  <span class="label-text-alt">设置翻牌的卡片张数（3-10张）</span>
                </label>
                <input type="number" 
                       class="input input-bordered input-sm"
                       value={@value["card_count"] || 5}
                       min="3"
                       max="10"
                       phx-blur="update_card_count" 
                       phx-target={@myself}
                       placeholder="请输入卡片数量" />
              </div>
              
              <div class="alert alert-warning mt-3">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L5.08 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div class="text-sm">
                  <div class="font-semibold">业务说明</div>
                  <div>
                    奖励配置中设置的奖励将在用户选择卡片时随机分配到各张卡片，无需预设权重或奖励方案
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
        
        <!-- 预览区域 -->
        <div class="alert alert-info mt-4">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <div class="font-bold">配置预览</div>
            <div class="text-sm">
              <%= case @value["type"] do %>
                <% "direct" -> %>
                  玩家完成任务后，将直接获得奖励配置中设置的奖励
                <% "flip_card" -> %>
                  玩家需要从 <strong><%= @value["card_count"] || 5 %> 张卡片</strong> 中选择1张来获得奖励
                <% _ -> %>
                  请选择奖励分发方式
              <% end %>
            </div>
          </div>
        </div>
      </div>
      """
    end
    
    @impl Phoenix.LiveComponent
    def handle_event("select_distribution_type", %{"type" => type}, socket) do
      new_value = case type do
        "direct" -> %{"type" => "direct"}
        "flip_card" -> %{"type" => "flip_card", "card_count" => 5}
        _ -> %{"type" => "direct"}
      end
      
      # 直接更新隐藏输入字段的值
      {:noreply, 
       socket
       |> assign(value: new_value)
       |> push_event("js-exec", %{
         to: "##{socket.assigns.name}_hidden_input",
         attr: "value",
         value: Jason.encode!(new_value)
       })}
    end
    
    @impl Phoenix.LiveComponent
    def handle_event("update_card_count", %{"value" => count}, socket) do
      {count_int, _} = Integer.parse(count)
      count_int = max(3, min(10, count_int))
      
      new_value = Map.put(socket.assigns.value, "card_count", count_int)
      
      # 直接更新隐藏输入字段的值
      {:noreply, 
       socket
       |> assign(value: new_value)
       |> push_event("js-exec", %{
         to: "##{socket.assigns.name}_hidden_input",
         attr: "value",
         value: Jason.encode!(new_value)
       })}
    end
  end
end