defmodule Teen.Live.GameManagement.GameRecordLive do
  @moduledoc """
  游戏记录管理页面

  提供游戏记录的查看、搜索和统计功能
  """

  use AshBackpex.LiveResource
  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.GameManagement.GameRecord
    layout({Teen.Layouts, :admin})
    load [:user]

    @impl Backpex.LiveResource
    def singular_name, do: "游戏记录"

    @impl Backpex.LiveResource
    def plural_name, do: "游戏记录"

    fields do
      field :id do
        module Backpex.Fields.Text
        label("记录ID")
        readonly(true)
        only([:show])
      end

      field :user do
        module Backpex.Fields.BelongsTo
        display_field(:username)
        live_resource(Teen.Live.UserLive)
        searchable(true)
        label("用户")
        help_text("游戏玩家")
      end

      field :game_id do
        module Backpex.Fields.Text
        label("游戏ID")
        searchable(true)
        help_text("游戏回合标识")
      end

      field :game_type do
        module Backpex.Fields.Text
        label("游戏类型")
        searchable(true)
        help_text("游戏种类")
      end

      field :bet_amount do
        module Backpex.Fields.Number
        label("下注金额")
        help_text("玩家下注金额（分）")

        render(fn assigns ->
          amount = assigns.value || 0
          yuan_amount = Decimal.div(amount, 100) |> Decimal.to_string()
          assigns = assign(assigns, :yuan_amount, yuan_amount)

          ~H"""
          <span class="font-mono text-blue-600">¥{@yuan_amount}</span>
          """
        end)
      end

      field :win_amount do
        module Backpex.Fields.Number
        label("获胜金额")
        help_text("玩家获胜金额（分）")

        render(fn assigns ->
          amount = assigns.value || 0
          yuan_amount = Decimal.div(amount, 100) |> Decimal.to_string()
          assigns = assign(assigns, :yuan_amount, yuan_amount)

          ~H"""
          <span class="font-mono text-green-600">¥{@yuan_amount}</span>
          """
        end)
      end

      field :net_amount do
        module Backpex.Fields.Number
        label("净输赢")
        help_text("净输赢金额（正数为盈利，负数为亏损）")

        render(fn assigns ->
          amount = assigns.value || 0
          yuan_amount = Decimal.div(amount, 100) |> Decimal.to_string()
          color_class = if Decimal.compare(amount, 0) == :gt, do: "text-green-600", else: "text-red-600"
          assigns = assign(assigns, :yuan_amount, yuan_amount) |> assign(:color_class, color_class)

          ~H"""
          <span class={"font-mono #{@color_class}"}>¥{@yuan_amount}</span>
          """
        end)
      end

      field :result_status do
        label("游戏结果")

        render(fn assigns ->
          status_map = %{
            win: {"胜利", "badge-success"},
            lose: {"失败", "badge-error"},
            draw: {"平局", "badge-warning"},
            cancelled: {"取消", "badge-ghost"},
            in_progress: {"进行中", "badge-info"}
          }

          {text, class} = Map.get(status_map, assigns.value, {"未知", "badge-ghost"})
          assigns = assign(assigns, :text, text) |> assign(:class, class)

          ~H"""
          <span class={"badge #{@class}"}>{@text}</span>
          """
        end)
      end

      field :duration do
        module Backpex.Fields.Number
        label("游戏时长")
        help_text("游戏时长（秒）")

        render(fn assigns ->
          duration = assigns.value || 0
          minutes = div(duration, 60)
          seconds = rem(duration, 60)
          time_text = if minutes > 0, do: "#{minutes}分#{seconds}秒", else: "#{seconds}秒"
          assigns = assign(assigns, :time_text, time_text)

          ~H"""
          <span class="font-mono">{@time_text}</span>
          """
        end)
      end

      field :started_at do
        module Backpex.Fields.DateTime
        label("开始时间")
        readonly(true)
      end

      field :completed_at do
        module Backpex.Fields.DateTime
        label("完成时间")
        readonly(true)
      end

      field :game_data do
        module Backpex.Fields.Textarea
        label("游戏数据")
        help_text("游戏详细数据（JSON格式）")
        only([:show])

        render fn assigns ->
          data = assigns.value || %{}
          json_data = Jason.encode!(data, pretty: true)
          assigns = assign(assigns, :json_data, json_data)

          ~H"""
          <pre class="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">{@json_data}</pre>
          """
        end

        render_form fn assigns ->
          data = assigns.value || %{}
          json_data = Jason.encode!(data, pretty: true)
          assigns = assign(assigns, :value, json_data)

          ~H"""
          <textarea
            id={assigns.id}
            name={Phoenix.HTML.Form.input_name(assigns.form, assigns.name)}
            class="textarea textarea-bordered w-full h-32"
            placeholder="请输入JSON格式的游戏数据"
          ><%= @json_data %></textarea>
          """
        end
      end

      # field :inserted_at do
      #   module Backpex.Fields.DateTime
      #   label("创建时间")
      #   readonly(true)
      # end

      # field :updated_at do
      #   module Backpex.Fields.DateTime
      #   label("更新时间")
      #   readonly(true)
      #   only([:show])
      # end
    end

    # filters do
    #   filter :game_type do
    #     module Backpex.Filters.Select
    #     label("游戏类型")

    #     options([
    #       {"所有游戏", ""},
    #       {"德州扑克", "texas_holdem"},
    #       {"百家乐", "baccarat"},
    #       {"龙虎斗", "dragon_tiger"},
    #       {"牛牛", "niuniu"}
    #     ])
    #   end

    #   filter :result_status do
    #     module Backpex.Filters.Select
    #     label("游戏结果")

    #     options([
    #       {"所有结果", ""},
    #       {"胜利", "win"},
    #       {"失败", "lose"},
    #       {"平局", "draw"},
    #       {"取消", "cancelled"},
    #       {"进行中", "in_progress"}
    #     ])
    #   end

    #   filter :date_range do
    #     module Backpex.Filters.DateRange
    #     label("时间范围")
    #     field(:started_at)
    #   end
    # end
  end

  @impl Backpex.LiveResource
  def can?(_assigns, action, _item) do
    case action do
      :index -> true
      :show -> true
      :new -> false  # 不允许手动创建游戏记录
      :edit -> false # 不允许编辑游戏记录
      :delete -> true # 允许删除（管理员清理数据）
      _ -> false
    end
  end
end
