defmodule Teen.Live.WithdrawalRecordLive do
  @moduledoc """
  提现记录管理页面

  提供提现记录的查看、审核和管理功能
  """

  use AshBackpex.LiveResource
  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.PaymentSystem.WithdrawalRecord
    layout({Teen.Layouts, :admin})

    fields do
      field :order_id do
        searchable(true)
        readonly(true)
        help_text("系统生成的唯一提现订单号")
      end

      field :user do
        display_field(:username)
        live_resource(Teen.Live.UserLive)
        searchable(true)
        help_text("申请提现的用户")
      end

      field :withdrawal_amount do
        help_text("申请提现的金额（分）")
        render(fn assigns ->
          amount = assigns.value || 0
          assigns = Phoenix.LiveView.assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono text-lg font-semibold">¥{@yuan_amount}</span>
          """
        end)
      end

      field :payment_method do
        module Backpex.Fields.Select
        options([
          {"银行卡", "bank_card"},
          {"支付宝", "alipay"},
          {"UPI", "upi"}
        ])
        render(fn assigns ->
          case assigns.value do
            "bank_card" -> ~H"<span class=\"badge badge-primary\">银行卡</span>"
            "alipay" -> ~H"<span class=\"badge badge-info\">支付宝</span>"
            "upi" -> ~H"<span class=\"badge badge-secondary\">UPI</span>"
            _ -> ~H"<span class=\"badge badge-ghost\">未知</span>"
          end
        end)
      end

      field :fee_amount do
        help_text("手续费金额（分）")
        render(fn assigns ->
          amount = assigns.value || 0
          assigns = Phoenix.LiveView.assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono text-orange-600">¥{@yuan_amount}</span>
          """
        end)
      end

      field :actual_amount do
        help_text("扣除手续费后的实际到账金额（分）")
        render(fn assigns ->
          amount = assigns.value || 0
          assigns = Phoenix.LiveView.assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono text-green-600 font-semibold">¥{@yuan_amount}</span>
          """
        end)
      end

      field :audit_status do
        module Backpex.Fields.Select
        options([
          {"待审核", 0},
          {"审核通过", 1},
          {"审核拒绝", 2}
        ])
        render(fn assigns ->
          case assigns.value do
            0 -> ~H"<span class=\"badge badge-warning\">待审核</span>"
            1 -> ~H"<span class=\"badge badge-success\">审核通过</span>"
            2 -> ~H"<span class=\"badge badge-error\">审核拒绝</span>"
            _ -> ~H"<span class=\"badge badge-ghost\">未知</span>"
          end
        end)
      end

      field :progress_status do
        module Backpex.Fields.Select
        options([
          {"排队中", 0},
          {"处理中", 1},
          {"支付成功", 2},
          {"支付失败", 3},
          {"人工处理", 4},
          {"已取消", 5}
        ])
        render(fn assigns ->
          case assigns.value do
            0 -> ~H"<span class=\"badge badge-neutral\">排队中</span>"
            1 -> ~H"<span class=\"badge badge-info\">处理中</span>"
            2 -> ~H"<span class=\"badge badge-success\">支付成功</span>"
            3 -> ~H"<span class=\"badge badge-error\">支付失败</span>"
            4 -> ~H"<span class=\"badge badge-warning\">人工处理</span>"
            5 -> ~H"<span class=\"badge badge-ghost\">已取消</span>"
            _ -> ~H"<span class=\"badge badge-ghost\">未知</span>"
          end
        end)
      end

      field :required_turnover do
        module Backpex.Fields.Number
        help_text("用户需要完成的流水金额（分）")
        render(fn assigns ->
          amount = assigns.value || 0
          assigns = Phoenix.LiveView.assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono">¥{@yuan_amount}</span>
          """
        end)
      end

      field :completed_turnover do
        module Backpex.Fields.Number
        help_text("用户已完成的流水金额（分）")
        render(fn assigns ->
          amount = assigns.value || 0
          assigns = Phoenix.LiveView.assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono">¥{@yuan_amount}</span>
          """
        end)
      end

      field :bank_info do
        module(Backpex.Fields.Textarea)
        help_text("用户提供的银行卡信息")
        render(fn assigns ->
          case assigns.value do
            nil ->
              ~H"<span class=\"text-gray-500\">无</span>"

            info ->
              case Jason.decode(info) do
                {:ok, data} ->
                  assigns = Phoenix.LiveView.assign(assigns, :bank_data, data)

                  ~H"""
                  <div class="text-sm">
                    <div><strong>银行:</strong> {Map.get(@bank_data, "bank_name", "未知")}</div>
                    <div>
                      <strong>卡号:</strong>
                      **** **** **** {String.slice(Map.get(@bank_data, "card_number", ""), -4, 4)}
                    </div>
                    <div><strong>户名:</strong> {Map.get(@bank_data, "account_name", "未知")}</div>
                  </div>
                  """

                {:error, _} ->
                  ~H"<span class=\"text-red-500\">格式错误</span>"
              end
          end
        end)
      end

      field :feedback do
        module(Backpex.Fields.Textarea)
        help_text("审核人员的反馈信息")
      end

      field :audit_time do
        module(Backpex.Fields.DateTime)
        help_text("审核完成的时间")
      end

      field :ip_address do
        help_text("用户申请提现时的IP地址")
      end

      field :created_at do
        module(Backpex.Fields.DateTime)
        readonly(true)
      end

      field :updated_at do
        module(Backpex.Fields.DateTime)
        readonly(true)
      end
    end

    filters do
      filter :audit_status do
        module Teen.Live.Filters.AuditStatusSelect
      end

      filter :progress_status do
        module Teen.Live.Filters.ProgressStatusSelect
      end

      filter :payment_method do
        module Teen.Live.Filters.PaymentMethodSelect
      end
    end

    item_actions do
      strip_default([:new, :delete])

      action :batch_approve, Teen.ItemActions.BatchApproveWithdrawals
      action :batch_reject, Teen.ItemActions.BatchRejectWithdrawals
      action :export_records, Teen.ItemActions.ExportWithdrawalRecords
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "提现记录"

  @impl Backpex.LiveResource
  def plural_name, do: "提现记录"

  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-base-100 p-6 rounded-lg shadow-sm mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-base-content">提现记录管理</h2>
          <p class="text-base-content/70 mt-1">查看和管理所有用户提现申请</p>
        </div>
        <div class="stats shadow">
          <div class="stat">
            <div class="stat-title">总申请数</div>
            <div class="stat-value text-primary">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">待审核</div>
            <div class="stat-value text-warning">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">已完成</div>
            <div class="stat-value text-success">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">今日提现额</div>
            <div class="stat-value text-info">¥0</div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
