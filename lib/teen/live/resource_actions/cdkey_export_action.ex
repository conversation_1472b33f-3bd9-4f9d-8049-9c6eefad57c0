defmodule Teen.Live.Actions.CdkeyExportAction do
  use Backpex.ResourceAction
  import Phoenix.LiveView, only: [put_flash: 3]

  @impl Backpex.ResourceAction
  def label(), do: "导出"

  @impl Backpex.ResourceAction
  def title(), do: "导出CDKEY数据"

  @impl Backpex.ResourceAction
  def icon(_assigns), do: "hero-arrow-down-tray"

  @impl Backpex.ResourceAction
  def can?(_assigns), do: true

  @impl Backpex.ResourceAction
  def fields do
    []
  end

  @impl Backpex.ResourceAction
  def handle(socket, _params) do
    # 这里实现导出逻辑
    {:noreply, socket |> put_flash(:info, "导出CDKEY功能开发中")}
  end
end
