defmodule Teen.Live.Actions.CdkeyClaimRecordActions do
  @moduledoc """
  CDKEY领取记录相关操作
  """

  defmodule ExportAction do
    use Backpex.ItemAction
    import Phoenix.Component

    @impl Backpex.ItemAction
    def label(_assigns, _item), do: "导出记录"

    @impl Backpex.ItemAction
    def icon(assigns, _item) do
      ~H"""
      <Backpex.HTML.CoreComponents.icon
        name="hero-arrow-down-tray"
        class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
      />
      """
    end

    @impl Backpex.ItemAction
    def confirm(_assigns), do: "确认导出选中的记录吗？"

    @impl Backpex.ItemAction
    def fields, do: []

    def can?(_assigns, _item) do
      true
    end

    @impl Backpex.ItemAction
    def handle(socket, records, _params) do
      count = length(records)
      {:noreply, socket |> Phoenix.LiveView.put_flash(:info, "导出 #{count} 条领取记录")}
    end
  end

  defmodule StatisticsAction do
    use Backpex.ItemAction
    import Phoenix.Component

    @impl Backpex.ItemAction
    def label(_assigns, _item), do: "统计分析"

    @impl Backpex.ItemAction
    def icon(assigns, _item) do
      ~H"""
      <Backpex.HTML.CoreComponents.icon
        name="hero-chart-bar"
        class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-green-600"
      />
      """
    end

    @impl Backpex.ItemAction
    def confirm(_assigns), do: "确认对选中记录进行统计分析吗？"

    @impl Backpex.ItemAction
    def fields, do: []

    def can?(_assigns, _item) do
      true
    end

    @impl Backpex.ItemAction
    def handle(socket, records, _params) do
      # 统计分析逻辑
      total_records = length(records)
      unique_users = records |> Enum.map(& &1.user_id) |> Enum.uniq() |> length()
      unique_cdkeys = records |> Enum.map(& &1.cdkey_id) |> Enum.uniq() |> length()

      message = "统计结果: 总记录#{total_records}条, 涉及#{unique_users}个用户, #{unique_cdkeys}个CDKEY"
      {:noreply, socket |> Phoenix.LiveView.put_flash(:info, message)}
    end
  end
end