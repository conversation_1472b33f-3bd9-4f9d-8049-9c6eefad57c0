defmodule Teen.ResourceActions.DailySettlementAction do
  @moduledoc """
  每日结算资源动作
  """

  use Backpex.ResourceAction
  import Phoenix.LiveView

  @impl Backpex.ResourceAction
  def title, do: "执行每日结算"

  @impl Backpex.ResourceAction
  def label, do: "执行每日结算"

  @impl Backpex.ResourceAction
  def icon, do: "hero-calculator"

  @impl Backpex.ResourceAction
  def fields, do: []

  @impl Backpex.ResourceAction
  def changeset(changeset, _attrs, _metadata \\ []) do
    # 处理 tuple 情况，返回空的 changeset
    Ecto.Changeset.change(changeset)
  end

  @impl Backpex.ResourceAction
  def handle(socket, _params) do
    case Teen.ActivitySystem.LossRebateJar.daily_settlement(%{}) do
      {:ok, result} ->
        message = "每日结算完成！成功处理 #{result.processed_users} 个用户"

        # 从完整URL中提取路径
        return_path = extract_path_from_url(socket.assigns.current_url)

        {:ok,
         socket
         |> put_flash(:info, message)}

      {:error, reason} ->
        return_path = extract_path_from_url(socket.assigns.current_url)

        {:ok,
         socket
         |> put_flash(:error, "结算失败：#{inspect(reason)}")}
    end
  end

  # 从完整URL中提取路径部分
  defp extract_path_from_url(url) when is_binary(url) do
    case URI.parse(url) do
      %URI{path: path, query: query} when is_binary(path) ->
        if query, do: "#{path}?#{query}", else: path
      _ ->
        "/admin/user-loss-rebates"
    end
  end

  defp extract_path_from_url(_), do: "/admin/user-loss-rebates"
end
