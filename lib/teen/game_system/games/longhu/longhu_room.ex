defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.LongHu.LongHuRoom do
  @moduledoc """
  龙虎斗游戏房间实现

  ## 游戏流程
  1. **初始化阶段** - 房间创建和配置
  2. **玩家管理** - 玩家加入、离开、重连
  3. **下注阶段** - 玩家下注、续押、庄家管理
  4. **发牌阶段** - 发牌动画和准备
  5. **亮牌阶段** - 显示牌面结果
  6. **结算阶段** - 计算输赢、分发奖励
  7. **等待阶段** - 准备下一轮游戏

  ## 游戏规则
  - 龙虎斗是一种简单的纸牌比大小游戏
  - 分为龙、虎、和三个下注区域
  - 每局发两张牌，比较大小决定胜负
  - 支持庄家系统和机器人
  """

  use Cypridina.Teen.GameSystem.RoomBase, game_type: :longhu

  alias Cypridina.Teen.GameSystem.Games.LongHu.{LongHuGame, LongHuLog<PERSON>, LongHuAI}
  alias Cypridina.Teen.GameSystem.Games.LongHu.{LongHuMessageBuilder}
  alias Teen.Inventory.HundredPlayerControlCalculator
  require Logger

  @main_id 5
  # ==================== 工具宏定义 ====================

  defmacro log_info(tag, message, context \\ []) do
    quote do
      Logger.info(
        "🐉 [#{unquote(tag)}] #{unquote(message)}" <>
          case unquote(context) do
            [] -> ""
            ctx -> " - #{inspect(ctx)}"
          end
      )
    end
  end

  defmacro log_error(tag, message, context \\ []) do
    quote do
      Logger.error(
        "❌ [#{unquote(tag)}] #{unquote(message)}" <>
          case unquote(context) do
            [] -> ""
            ctx -> " - #{inspect(ctx)}"
          end
      )
    end
  end

  # ==================== RoomBehaviour 回调函数实现 ====================

  @doc """
  获取最小玩家数 - 龙虎斗是百人场游戏，最小1人即可开始
  """
  @impl true
  def min_players, do: 1

  @doc """
  获取最大玩家数 - 龙虎斗是百人场游戏，支持大量玩家
  """
  @impl true
  def max_players, do: 1000

  @doc """
  判断游戏是否结束 - 龙虎斗是持续运行的百人场游戏，不会结束
  """
  @impl true
  def game_over?(_state), do: false

  # ==================== 1. 初始化阶段 ====================

  @doc """
  初始化游戏逻辑
  """
  @impl true
  def init_game_logic(state) do
    unified_config = LongHuGame.full_config(state.config)

    state
    |> build_initial_game_state(unified_config)
    |> setup_robot_management(unified_config)
    |> start_game_loop()
  end

  @doc """
  开始游戏循环
  """
  @impl true
  def on_game_start(state) do
    log_info("GAME_START", "开始新一轮游戏", room: state.id, round: state.game_data.round + 1)

    # 重置游戏数据
    game_data = %{
      state.game_data
      | phase: :betting,
        round: state.game_data.round + 1,
        cards: %{long: nil, hu: nil},
        bets: %{},
        total_bets: %{long: 0, hu: 0, he: 0}
    }

    # 启动游戏tick
    schedule_game_tick()

    %{state | game_data: game_data}
    # 启动下注阶段计时器
    |> start_betting_phase()
  end

  # 构建初始游戏状态
  defp build_initial_game_state(state, config) do
    game_config = extract_game_config(config)
    game_data = build_game_data(config, game_config)

    %{state | game_data: game_data}
  end

  # 提取游戏配置
  defp extract_game_config(config) do
    %{
      bet_time: config.bet_time,
      deal_time: config.deal_time,
      reveal_time: config.reveal_time,
      settle_time: config.settle_time,
      min_bet: config.min_bet,
      max_bet: config.max_bet,
      area_bet_limits: config.area_bet_limits,
      enable_robots: config.enable_robots,
      robot_count: config.robot_count,
      banker: config.banker,
      vip_benefits: config.vip_benefits
    }
  end

  # 构建游戏数据
  defp build_game_data(config, game_config) do
    %{
      phase: LongHuGame.game_phases().betting,
      round: 1,
      cards: %{long: nil, hu: nil},
      bets: %{},
      total_bets: %{long: 0, hu: 0, he: 0},
      banker: build_system_banker(config),
      banker_queue: [],
      banker_off_requests: [],
      last_round_bets: %{},
      statistics: build_initial_statistics(),
      player_stats: %{},
      chat_history: [],
      vip_benefits: config.vip_benefits,
      history: [],
      phase_timer: nil,
      config: game_config,
      odds: LongHuGame.odds()
    }
  end

  # 构建系统庄家
  defp build_system_banker(config) do
    %{
      numeric_id: nil,
      name: "系统庄家",
      points: config.banker.system_banker_money,
      turns_played: 0,
      max_turns: config.banker.max_turns,
      min_turns: config.banker.min_turns,
      is_system: true
    }
  end

  # 构建初始统计数据
  defp build_initial_statistics do
    %{
      total_rounds: 0,
      total_bets_amount: 0,
      total_players: 0,
      win_rates: %{long: 0.0, hu: 0.0, he: 0.0},
      recent_results: [],
      daily_stats: %{
        date: Date.utc_today(),
        rounds: 0,
        total_bets: 0,
        unique_players: MapSet.new()
      }
    }
  end

  # 设置机器人管理
  defp setup_robot_management(state, config) do
    if state.game_data.config.enable_robots do
      robot_check_interval = config.performance_config.robot_check_interval
      Process.send_after(self(), :manage_robots, robot_check_interval)
    end

    state
  end

  defp check_game_start(state) do
    player_count = map_size(state.players)

    if state.room_state == @room_states.waiting and player_count >= state.min_players do
      Logger.info("🏠 [ROOM_BASE] 房间满足开始条件: #{state.id}, 玩家数: #{player_count}")
      start_game(state)
    else
      state
    end
  end

  # ==================== 2. 玩家管理 ====================

  @doc """
  玩家加入房间
  """
  @impl true
  def on_player_joined(state, player) do
    log_info("PLAYER_JOIN", "玩家加入百人场", player_id: player.numeric_id)
    # longhu_room需要使用numeric_id作为键来管理玩家
    # 这与room_base的user_id键不同，所以需要特殊处理

    state
    |> send_room_info_to_player(player)
    |> add_player_to_state(player)
    |> send_initial_data_to_player(player)
    |> maybe_add_robots()
    |> broadcast_player_count_change()
    # 检查是否可以开始游戏
    |> check_game_start()
  end

  @doc """
  玩家重连
  """
  @impl true
  def on_player_rejoined(state, player) do
    log_info("PLAYER_REJOIN", "玩家重连加入百人场", player_id: player.numeric_id)

    # 发送断线重连房间信息 (XC_ROOM_INFO_P)
    state
    |> send_reconnect_room_info(player)
    # 发送初始数据（包含历史记录）
    |> send_initial_data_to_player(player)
  end

  @doc """
  玩家离开房间
  """
  @impl true
  def on_player_left(state, player) do
    log_info("PLAYER_LEFT", "玩家离开房间", player_id: player.numeric_id)

    state
    |> remove_player_from_state(player.numeric_id)
    |> remove_player_bets(player.numeric_id)
    |> broadcast_player_count_change()
  end

  @doc """
  处理游戏消息
  """
  @impl true
  def handle_game_message(state, player, %{"subId" => sub_id} = message) do
    log_info("GAME_MESSAGE", "收到游戏消息",
      room: state.id,
      user: player.numeric_id,
      message: inspect(message)
    )

    case sub_id do
      # 下注请求
      3904 ->
        handle_client_bet(message, state, player)

      # 申请上庄
      3906 ->
        numeric_id = player.numeric_id
        Logger.info("🐉 [CLIENT_BANKER] 处理客户端申请上庄请求 - 用户: #{numeric_id}")
        handle_apply_banker(state, player)

      # 取消申请上庄
      3907 ->
        handle_client_cancel_apply_banker(message, state, player)

      # 申请下庄
      3919 ->
        handle_client_apply_off_banker(message, state, player)

      # 请求上庄列表
      3924 ->
        handle_client_request_banker_list(message, state, player)

      # 请求玩家列表
      3920 ->
        handle_client_request_player_list(message, state, player)

      # 续押请求
      3917 ->
        handle_client_follow_bet(message, state, player)

      _ ->
        log_error("UNKNOWN_PROTOCOL", "未知的客户端协议", sub_id: sub_id, user: player.numeric_id)
        state
    end
  end

  # longhu_room需要维护自己的玩家映射，使用numeric_id作为键
  # 这与room_base的user_id键不同，是为了兼容现有的longhu游戏逻辑
  defp add_player_to_state(state, player) do
    # 检查是否已经存在
    if Map.has_key?(state.players, player.numeric_id) do
      state
    else
      # 在longhu_room的players map中添加玩家，使用numeric_id作为键
      %{state | players: Map.put(state.players, player.numeric_id, player)}
    end
  end

  # 从longhu_room的玩家映射中移除玩家
  defp remove_player_from_state(state, numeric_id) do
    %{state | players: Map.delete(state.players, numeric_id)}
  end

  # 发送初始数据给玩家
  defp send_initial_data_to_player(state, player) do
    state
    |> send_game_config(player)
    |> send_game_state_to_user(player)
    |> send_banker_info_to_user(player)
    |> send_banker_list_to_user(player)
    |> send_player_list_to_user(player, 0)
    # 发送历史记录给新加入的玩家
    |> send_history_to_user(player, 0, 20)
  end

  # 可能添加机器人
  defp maybe_add_robots(state) do
    if state.game_data.config.enable_robots do
      LongHuAI.add_robots_if_needed(state)
    else
      state
    end
  end

  # 移除玩家下注
  defp remove_player_bets(state, numeric_id) do
    new_bets = Map.delete(state.game_data.bets, numeric_id)
    new_total_bets = calculate_total_bets(new_bets)

    game_data = %{state.game_data | bets: new_bets, total_bets: new_total_bets}
    %{state | game_data: game_data}
  end

  # 广播玩家数量变化通知
  defp broadcast_player_count_change(state) do
    total_players = map_size(state.players)
    log_info("BROADCAST_PLAYER_COUNT", "广播玩家数量变化", total: total_players)

    message = LongHuMessageBuilder.build_player_count_change(total_players)
    broadcast_to_room(state, message)
  end

  @doc """
  开始下注阶段
  """
  defp start_betting_phase(state) do
    log_info("BETTING_START", "开始下注阶段", room: state.id, round: state.game_data.round)

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 启动新计时器
    timer = Process.send_after(self(), :phase_timeout, state.game_data.config.bet_time * 1000)

    game_data = %{state.game_data | phase: :betting, phase_timer: timer}
    new_state = %{state | game_data: game_data}

    # 广播下注开始
    broadcast_betting_start(new_state)

    # 让机器人开始下注
    if state.game_data.config.enable_robots do
      LongHuAI.schedule_robot_bets(new_state)
    end

    new_state
  end

  @doc """
  处理下注请求
  """
  defp handle_bet(state, player, area_str, amount) do
    log_info("BET_VALIDATION", "开始验证下注",
      user: player.numeric_id,
      area: area_str,
      amount: amount,
      phase: state.game_data.phase,
      round: state.game_data.round
    )

    with {:ok, area} <- parse_bet_area(area_str, player.numeric_id),
         :ok <- validate_game_phase(state, player),
         :ok <- validate_bet_amount(state, player, amount),
         :ok <- validate_player_funds(state, player, amount),
         :ok <- validate_bet_limits(state, player, area, amount) do
      execute_bet(state, player, area, amount)
    else
      {:error, error_code} ->
        send_error(state, player, error_code)
    end
  end

  # 验证下注限制（从validate_and_process_bet提升上来）
  defp validate_bet_limits(state, player, area, amount) do
    numeric_id = player.numeric_id
    current_banker = state.game_data.banker

    # 首先检查单区域下注限制
    case validate_single_area_bet_limit(state, player, area, amount) do
      :ok ->
        # 如果是玩家坐庄，还需要检查庄家赔付能力
        if current_banker.is_system do
          :ok
        else
          validate_banker_payout_ability(state, player, area, amount)
        end

      error -> error
    end
  end

  # 验证单区域下注限制（DRY原则：统一处理单区域限制）
  defp validate_single_area_bet_limit(state, player, area, amount) do
    numeric_id = player.numeric_id
    current_banker = state.game_data.banker

    # 获取单区域下注限制
    area_limit = get_single_area_bet_limit(state)

    # 获取玩家当前在该区域的下注
    user_bets = Map.get(state.game_data.bets, numeric_id, %{})
    current_bet = Map.get(user_bets, area, 0)
    new_bet = current_bet + amount

    if new_bet > area_limit do
      banker_type = if current_banker.is_system, do: "系统坐庄", else: "玩家坐庄"
      Logger.warning(
        "❌ [BET_ERROR] #{banker_type}单区域下注超限 - 用户: #{numeric_id}, 区域: #{area}, 当前下注: #{new_bet}, 限制: #{area_limit}"
      )

      # 发送金币更新协议 [4][8] 恢复玩家金币（因为前端已经扣除了）
      current_money = get_player_points(state, numeric_id)
      send_player_money_update(state, player, current_money)

      # GAME_ERROR_BET_TOOMORE (下注超出限额)
      {:error, 13}
    else
      :ok
    end
  end

  # 验证庄家赔付能力
  defp validate_banker_payout_ability(state, player, area, amount) do
    numeric_id = player.numeric_id
    current_banker = state.game_data.banker
    banker_balance = get_player_points(state, current_banker.numeric_id)
    expected_payout = calculate_expected_payout_with_new_bet(state, area, amount)

    # 计算加入新下注后的总下注金额
    current_total_bets = state.game_data.total_bets
    current_total_amount = Map.get(current_total_bets, :long, 0) +
                          Map.get(current_total_bets, :hu, 0) +
                          Map.get(current_total_bets, :he, 0) + amount

    # 庄家的总可用资金 = 初始余额 + 所有下注金额
    banker_total_funds = banker_balance + current_total_amount

    if expected_payout > banker_total_funds do
      Logger.warning(
        "❌ [BET_ERROR] 玩家坐庄期望赔付超过庄家总资金 - 庄家: #{current_banker.numeric_id}, 总资金: #{banker_total_funds}, 期望赔付: #{expected_payout}"
      )

      # 发送金币更新协议 [4][8] 恢复玩家金币（因为前端已经扣除了）
      current_money = get_player_points(state, numeric_id)
      send_player_money_update(state, player, current_money)

      # GAME_ERROR_BUY_LIMIT (庄家资金不足)
      {:error, 13}
    else
      :ok
    end
  end

  # 获取单区域下注限制（DRY原则：统一获取逻辑）
  defp get_single_area_bet_limit(state) do
    config = state.game_data.config
    current_banker = state.game_data.banker

    if current_banker.is_system do
      Map.get(config, :system_banker_area_limit, 2_000_000)
    else
      Map.get(config, :player_banker_area_limit, 2_000_000)
    end
  end

  @doc """
  计算加入新下注后的最大期望赔付

  当玩家坐庄时，需要确保庄家有足够的余额来支付最坏情况下的赔付。
  最坏情况是所有区域的下注都赢，庄家需要支付所有的赔付。

  ## 参数
  - state: 游戏状态
  - area: 新下注的区域
  - amount: 新下注的金额

  ## 返回
  最大可能的赔付金额
  """
  defp calculate_expected_payout_with_new_bet(state, area, amount) do
    # 获取当前各区域的总下注
    current_total_bets = state.game_data.total_bets

    # 计算加入新下注后的各区域总下注
    updated_total_bets = Map.update(current_total_bets, area, amount, &(&1 + amount))

    # 计算庄家在各种获胜情况下的最大净支出
    max_payout = calculate_banker_max_payout(updated_total_bets, state.game_data.odds)

    Logger.debug("🎰 [EXPECTED_PAYOUT] 计算期望赔付",
      area: area,
      amount: amount,
      updated_bets: updated_total_bets,
      max_payout: max_payout
    )

    max_payout
  end

  # 辅助函数：计算庄家在各种获胜情况下的最大赔付金额
  defp calculate_banker_max_payout(total_bets, odds) do
    long_bet = Map.get(total_bets, :long, 0)
    hu_bet = Map.get(total_bets, :hu, 0)
    he_bet = Map.get(total_bets, :he, 0)

    long_odds = Map.get(odds, :long, 2)
    hu_odds = Map.get(odds, :hu, 2)
    he_odds = Map.get(odds, :he, 9)

    # 龙获胜：庄家需要赔付龙区域
    long_win_payout = long_bet * long_odds

    # 虎获胜：庄家需要赔付虎区域
    hu_win_payout = hu_bet * hu_odds

    # 和获胜：庄家需要赔付和区域 + 返还龙虎区域下注的一半
    he_win_payout = he_bet * he_odds + div(long_bet + hu_bet, 2)

    # 返回庄家最大赔付金额（庄家需要支付的最大金额）
    max(long_win_payout, max(hu_win_payout, he_win_payout))
  end

  # 执行下注（原validate_and_process_bet的执行部分）
  defp execute_bet(state, player, area, amount) do
    numeric_id = player.numeric_id

    Logger.info("✅ [BET_VALIDATION] 下注验证通过 - 用户: #{numeric_id}, 区域: #{area}")

    user_bets = Map.get(state.game_data.bets, numeric_id, %{})
    current_bet = Map.get(user_bets, area, 0)
    new_bet = current_bet + amount

    state
    |> subtract_player_points(numeric_id, amount)
    |> process_successful_bet(player, area, amount, user_bets, current_bet, new_bet)
  end

  # 下注验证辅助函数
  defp parse_bet_area(area_str, numeric_id) do
    case area_str do
      area when area in ["long", :long] ->
        {:ok, :long}

      area when area in ["hu", :hu] ->
        {:ok, :hu}

      area when area in ["he", :he] ->
        {:ok, :he}

      _ ->
        log_error("BET_ERROR", "无效的下注区域", user: numeric_id, area: area_str)
        # GAME_ERROR_BUY_POS_ERROR
        {:error, 15}
    end
  end

  defp validate_game_phase(state, player) do
    if state.game_data.phase == :betting do
      :ok
    else
      log_error("BET_ERROR", "游戏阶段错误",
        user: player.numeric_id,
        current: state.game_data.phase,
        expected: :betting
      )

      # GAME_ERROR_STATE_ERROR
      {:error, 14}
    end
  end

  defp validate_bet_amount(state, player, amount) do
    config = state.game_data.config

    cond do
      amount < config.min_bet ->
        log_error("BET_ERROR", "下注金额过小",
          user: player.numeric_id,
          amount: amount,
          min: config.min_bet
        )

        # GAME_ERROR_NOT_MONEY_TO_BET
        {:error, 8}

      amount > config.max_bet ->
        log_error("BET_ERROR", "单次下注金额过大",
          user: player.numeric_id,
          amount: amount,
          max: config.max_bet
        )

        # GAME_ERROR_BET_TOOMORE
        {:error, 13}

      true ->
        :ok
    end
  end

  defp validate_player_funds(state, player, amount) do
    numeric_id = player.numeric_id
    current_points = get_player_points(state, numeric_id)

    if current_points >= amount do
      :ok
    else
      player_type = if is_robot?(numeric_id), do: "机器人", else: "玩家"
      log_error("BET_ERROR", "#{player_type}积分不足",
        user: numeric_id,
        current: current_points,
        needed: amount
      )
      # GAME_ERROR_NOT_MONEY_TO_BET
      {:error, 8}
    end
  end

  # 工具函数
  defp is_robot?(numeric_id), do: is_integer(numeric_id) and numeric_id < 0

  defp broadcast_betting_start(state) do
    game_state_data = format_game_state_for_client(state)
    message = LongHuMessageBuilder.build_game_state(state, game_state_data)
    broadcast_to_room(state, message)
  end

  # ==================== 4. 发牌阶段 ====================

  @doc """
  转换到发牌阶段
  """
  defp transition_to_dealing(state) do
    log_info("DEALING_START", "转换到发牌阶段", room: state.id, round: state.game_data.round)

    # 获取控制决策
    {control_mode, target_result} = get_control_decision(state)

    # 发牌（应用控制决策）
    {long_card, hu_card} =
      LongHuLogic.deal_cards(
        control_mode: control_mode,
        target_result: target_result,
        game_id: state.id
      )

    game_data = %{state.game_data | phase: :dealing, cards: %{long: long_card, hu: hu_card}}
    new_state = %{state | game_data: game_data}

    # 广播发牌
    broadcast_dealing(new_state)

    # 启动发牌计时器
    timer = Process.send_after(self(), :phase_timeout, state.game_data.config.deal_time * 1000)
    game_data = %{new_state.game_data | phase_timer: timer}

    %{new_state | game_data: game_data}
  end

  defp broadcast_dealing(state) do
    message = LongHuMessageBuilder.build_dealing(state)
    broadcast_to_room(state, message)
  end

  # ==================== 5. 亮牌阶段 ====================

  @doc """
  转换到亮牌阶段
  """
  defp transition_to_revealing(state) do
    log_info("REVEALING_START", "转换到亮牌阶段", room: state.id, round: state.game_data.round)

    game_data = %{state.game_data | phase: :revealing}
    new_state = %{state | game_data: game_data}

    # 广播亮牌
    broadcast_revealing(new_state)

    # 广播游戏状态
    broadcast_game_state(new_state)

    # 启动亮牌计时器
    timer = Process.send_after(self(), :phase_timeout, state.game_data.config.reveal_time * 1000)
    game_data = %{new_state.game_data | phase_timer: timer}

    %{new_state | game_data: game_data}
  end

  # ==================== 6. 结算阶段 ====================

  @doc """
  转换到结算阶段
  """
  defp transition_to_settling(state) do
    log_info("SETTLING_START", "开始转换到结算阶段", room: state.id, round: state.game_data.round)

    # 计算游戏结果
    result = LongHuLogic.calculate_result(state.game_data.cards.long, state.game_data.cards.hu)
    log_info("SETTLING_RESULT", "游戏结果计算完成", result: result)

    # 计算玩家输赢
    settlements = calculate_settlements(state, result)
    log_info("SETTLING_CALCULATION", "玩家结算计算完成", settlements: inspect(settlements))

    # 更新游戏状态
    updated_state = update_game_state_after_settlement(state, result, settlements)

    # 发布游戏完成事件（用于记录和活动系统）
    # 构造符合RoomBase期望的结算结果格式
    settlement_result = %{
      player_changes: calculate_player_changes(state, settlements),
      # 龙虎斗没有单一赢家
      winner: nil
    }

    publish_game_completion_events(updated_state, settlement_result)

    # 广播结算结果
    broadcast_settlement(updated_state, result, settlements)

    # 清理机器人
    final_state = cleanup_robots_after_settlement(updated_state)

    # 通知控制系统游戏结果（用于更新库存状态）
    notify_control_system_result(final_state, result, settlements)

    # 启动结算计时器
    timer = Process.send_after(self(), :phase_timeout, state.game_data.config.settle_time * 1000)
    game_data = %{final_state.game_data | phase_timer: timer}

    log_info("SETTLING_COMPLETE", "结算阶段转换完成", settle_time: state.game_data.config.settle_time)

    %{final_state | game_data: game_data}
  end

  # 更新结算后的游戏状态
  defp update_game_state_after_settlement(state, result, settlements) do
    # 更新玩家积分信息
    updated_state = update_players_with_latest_points(state, settlements)

    # 更新玩家统计数据
    updated_state_with_stats = update_player_statistics(updated_state, result, settlements)

    # 更新庄家积分信息
    {updated_state_with_stats, updated_banker_points, banker_change} =
      calculate_banker_settlement(updated_state_with_stats, result, settlements)

    updated_banker = %{updated_state_with_stats.game_data.banker | points: updated_banker_points}

    # 更新历史记录
    history_item = %{
      round: updated_state_with_stats.game_data.round,
      cards: updated_state_with_stats.game_data.cards,
      result: result,
      total_bets: updated_state_with_stats.game_data.total_bets,
      banker_change: banker_change,
      timestamp: DateTime.utc_now()
    }

    new_history = [history_item | updated_state_with_stats.game_data.history] |> Enum.take(50)

    # 更新游戏数据，包含更新后的庄家信息
    game_data = %{
      updated_state_with_stats.game_data
      | phase: :settling,
        history: new_history,
        banker: updated_banker
    }

    final_state = %{updated_state_with_stats | game_data: game_data}

    log_info("BANKER_STATE_UPDATE", "庄家积分已更新到state",
      banker_id: updated_banker.numeric_id,
      old_points: updated_state_with_stats.game_data.banker.points,
      new_points: updated_banker.points,
      change: banker_change
    )

    final_state
  end

  # 结算后清理机器人
  defp cleanup_robots_after_settlement(state) do
    if state.game_data.config.enable_robots do
      log_info("ROBOT_CLEANUP", "结算后清理积分不足的机器人")
      LongHuAI.cleanup_broke_robots(state, 500)
    else
      state
    end
  end

  # ==================== 7. 等待阶段 ====================

  @doc """
  转换到等待阶段
  """
  defp transition_to_waiting(state) do
    log_info("WAITING_START", "结算完成，准备开始新一轮", room: state.id)

    # 保存本轮下注记录用于续押
    last_round_bets = state.game_data.bets

    # 处理庄家轮换
    new_state = handle_banker_rotation(state)

    game_data = %{
      new_state.game_data
      | phase: :waiting,
        phase_timer: nil,
        last_round_bets: last_round_bets,
        bets: %{},
        total_bets: %{long: 0, hu: 0, he: 0}
    }

    # 百人场自动循环，2秒后开始新一轮
    Process.send_after(self(), :start_new_round, 2000)

    %{new_state | game_data: game_data}
    # 广播等待阶段
    |> broadcast_waiting()
    # 广播上庄列表更新（因为玩家积分可能已经发生变化）
    |> broadcast_banker_list()
  end

  defp broadcast_waiting(state) do
    message = %{
      "mainId" => 5,
      # SC_LHD_GAMESTATE_P
      "subId" => 3901,
      "data" => %{
        # LHD_GameState_End
        "state" => 3,
        "round" => state.game_data.round,
        "next_round_delay" => 2000
      }
    }

    broadcast_to_room(state, message)
  end

  # ==================== 8. 游戏循环控制 ====================

  @doc """
  启动游戏循环
  """
  defp start_game_loop(state) do
    log_info("GAME_LOOP_START", "启动百人场游戏循环", room: state.id)

    # 添加机器人烘托气氛
    new_state = maybe_add_robots(state)
    # 立即开始第一轮游戏
    new_state = start_betting_phase(new_state)

    # 启动游戏tick
    schedule_game_tick()

    new_state
  end

  # ==================== 9. 辅助计算函数 ====================

  defp calculate_total_bets(bets) do
    Enum.reduce(bets, %{long: 0, hu: 0, he: 0}, fn {_numeric_id, user_bets}, acc ->
      Enum.reduce(user_bets, acc, fn {area, amount}, area_acc ->
        Map.update(area_acc, area, amount, &(&1 + amount))
      end)
    end)
  end

  defp calculate_settlements(state, result) do
    # 获取游戏控制配置以获取明税比例
    # 龙虎游戏使用固定的游戏ID 22（不是房间号）
    game_control_config = get_game_control_config(22)

    # 计算有下注的玩家的结算，并实际处理积分变化
    state.game_data.bets
    |> Enum.filter(fn {numeric_id, user_bets} ->
      # 过滤掉没有下注的玩家
      numeric_id != nil and map_size(user_bets) > 0
    end)
    |> Enum.reduce(%{}, fn {numeric_id, user_bets}, acc ->
      # numeric_id 已经是整数类型，无需转换

      user_settlement =
        Enum.reduce(user_bets, 0, fn {area, amount}, total ->
          cond do
            # 赢了，计算净赢利
            area == result ->
              total_payout = amount * Map.get(state.game_data.odds, area)
              # 因为之前下注时已经扣掉了投注，所以这里赢多少都是净赢利
              gross_profit = total_payout

              # 检查是否为真人玩家并应用明税
              net_profit =
                apply_winner_tax_if_real_player(
                  state,
                  numeric_id,
                  gross_profit,
                  game_control_config
                )

              total + net_profit

            # 和局特殊处理：龙区域和虎区域返还50%
            result == :he and area in [:long, :hu] ->
              # 返还50%的下注金额
              refund_amount = div(amount, 2)

              Logger.info(
                "🎲 [TIE_REFUND] 和局返还 - 用户: #{numeric_id}, 区域: #{area}, 原下注: #{amount}, 返还: #{refund_amount}"
              )

              total + refund_amount

            # 其他情况：输了，损失下注金额（已在下注时扣除）
            true ->
              total
          end
        end)

      # 记录结算数据
      Map.put(acc, numeric_id, user_settlement)
    end)
  end

  @doc """
  计算庄家在本轮的输赢

  正确的计算逻辑：
  庄家赢的钱 = 桌上的筹码总额 - 对玩家的赔付总额
  """
  defp calculate_banker_settlement(state, result, settlements) do
    current_banker = state.game_data.banker

    # 计算桌上的筹码总额（所有玩家的下注总和）
    total_chips_on_table =
      state.game_data.total_bets
      |> Enum.reduce(0, fn {_area, amount}, acc -> acc + amount end)

    # 计算对玩家的总赔付（只计算赔付，不包括输掉的下注）
    total_player_payouts =
      settlements
      |> Enum.reduce(0, fn {_numeric_id, win_amount}, acc ->
        if win_amount > 0 do
          # 玩家赢钱时，这是庄家需要赔付的金额
          acc + win_amount
        else
          # 玩家输钱时，庄家不需要赔付（玩家的下注已经在下注时被扣除）
          acc
        end
      end)

    # 庄家的净收益 = 桌上筹码总额 - 对玩家的赔付总额
    banker_change = total_chips_on_table - total_player_payouts

    log_info("BANKER_SETTLEMENT", "计算庄家输赢",
      banker_id: current_banker.numeric_id,
      total_chips_on_table: total_chips_on_table,
      total_player_payouts: total_player_payouts,
      banker_change: banker_change,
      result: result
    )

    # 获取庄家当前真实积分（如果是玩家庄家）
    current_banker_points =
      if current_banker.is_system do
        current_banker.points
      else
        get_player_points(state, current_banker.numeric_id)
      end

    # 更新庄家积分
    updated_banker_points = current_banker_points + banker_change

    # 如果是真实玩家庄家，需要更新数据库积分

    state =
      if current_banker.numeric_id != nil do
        case Map.get(state.players, current_banker.numeric_id) do
          nil ->
            log_error("BANKER_SETTLEMENT", "找不到庄家玩家信息", banker_id: current_banker.numeric_id)
            state

          banker_player ->
            if banker_change > 0 do
              add_player_points(state, banker_player.numeric_id, banker_change)
            else
              subtract_player_points(state, banker_player.numeric_id, abs(banker_change))
            end
        end
      else
        state
      end

    {state, updated_banker_points, banker_change}
  end

  @doc """
  构建庄家结算数据
  """
  defp build_banker_settlement_data(current_banker, updated_banker_points, banker_change) do
    # 获取庄家的真实信息
    # 系统庄家使用-1
    banker_id = current_banker.numeric_id || -1
    banker_name = current_banker.name || "系统庄家"

    log_info("BANKER_DATA", "构建庄家结算数据",
      banker_id: banker_id,
      banker_name: banker_name,
      current_points: current_banker.points,
      updated_points: updated_banker_points,
      change: banker_change
    )

    %{
      # 庄家ID（系统庄家为-1，玩家庄家为真实ID）
      "playerid" => banker_id,
      # 庄家剩余积分（更新后的真实积分）
      "playercoin" => updated_banker_points,
      # 庄家本轮输赢金额
      "nChange" => banker_change,
      # 庄家名称（可选，用于调试）
      "name" => banker_name,
      # 是否为系统庄家
      "is_system" => current_banker.numeric_id == nil
    }
  end

  @doc """
  构建结算时的 other 数据，符合客户端期望格式

  客户端格式参考：
  if (msg.other) {
    let count = 0;
    for (let key in msg.other) {
      let item = msg.other[key];
      let tmp = item.split(",");
      let userData: any = {};
      userData.playercoin = parseInt(tmp[0]);
      userData.nChange = parseInt(tmp[1]);
      userData.playerid = key;
      count += 1;
      resultMsg.others.betrank[count.toString()] = userData;
    }
  }
  """
  defp build_settlement_other_data(state, settlements) do
    settlements
    |> Enum.filter(fn {numeric_id, win_amount} ->
      # 只包含有效的玩家数据：用户ID不为空且输赢金额不为nil
      numeric_id != nil and win_amount != nil
    end)
    |> Enum.into(%{}, fn {numeric_id, win_amount} ->
      # 获取玩家结算后的真实积分
      final_points = get_player_points(state, numeric_id)

      log_info("SETTLEMENT_OTHER", "构建玩家结算数据",
        player_id: numeric_id,
        win_amount: win_amount,
        final_points: final_points,
        is_robot: is_robot?(numeric_id)
      )

      # 客户端期望格式: "playerid" => "剩余积分,输赢积分"
      # 其中 playerid 必须是字符串格式
      player_id_str = to_string(numeric_id)
      settlement_str = "#{final_points},#{win_amount}"

      {player_id_str, settlement_str}
    end)
  end

  # 广播和发送函数
  defp send_game_config(state, player) do
    log_info("SEND_CONFIG", "发送游戏配置",
      user: player.numeric_id,
      room: state.id,
      min_bet: state.game_data.config.min_bet,
      max_bet: state.game_data.config.max_bet
    )

    log_info("SEND_CONFIG", "游戏配置已发送")

    message = LongHuMessageBuilder.build_game_config(state)
    send_to_player(state, player, message)
  end

  defp send_error(state, player, error_code) do
    log_error("SEND_ERROR", "发送错误消息", user: player.numeric_id, error_code: error_code)

    message = LongHuMessageBuilder.build_error_message(error_code)
    send_to_player(state, player, message)
  end

  defp broadcast_revealing(state) do
    cards = state.game_data.cards
    long_card_data = format_card_for_client(cards.long)
    hu_card_data = format_card_for_client(cards.hu)

    log_info("BROADCAST_REVEALING", "发送亮牌数据",
      long_card: inspect(cards.long),
      hu_card: inspect(cards.hu)
    )

    message = LongHuMessageBuilder.build_revealing(state, long_card_data, hu_card_data)
    broadcast_to_room(state, message)
  end

  defp broadcast_settlement(state, result, settlements) do
    # SC_LHD_SETTLEMENT_P_NEW
    settlement_protocol = 3928

    Logger.info(
      "📡 [BROADCAST_SETTLEMENT] 开始广播结算消息 - 协议: #{settlement_protocol}, 回合: #{state.game_data.round}"
    )

    # 使用统一配置的协议格式: MainProto.XC (5) + SC_LHD_SETTLEMENT_P_NEW
    # 客户端期望的结算消息格式 (参考 onResult 函数)
    cards = state.game_data.cards

    # 构建真实的 other 字段数据
    other_data = build_settlement_other_data(state, settlements)

    log_info("SETTLEMENT", "结算数据构建完成", data: inspect(other_data))

    # 使用已经更新的庄家信息（在 update_game_state_after_settlement 中已计算）
    current_banker = state.game_data.banker

    # 从历史记录中获取庄家变化（最新的记录）
    banker_change =
      case state.game_data.history do
        [latest_history | _] -> latest_history.banker_change
        [] -> 0
      end

    zhuang_data =
      build_banker_settlement_data(current_banker, current_banker.points, banker_change)

    # 生成唯一的消息ID用于追踪
    message_id = "settlement_#{state.game_data.round}_#{System.system_time(:millisecond)}"

    # 格式化牌值数据 - 使用完整的客户端格式
    long_card_data = format_card_for_client(cards.long)
    hu_card_data = format_card_for_client(cards.hu)

    log_info("SETTLEMENT_CARDS", "结算牌值数据",
      long_card: inspect(cards.long),
      hu_card: inspect(cards.hu),
      long_formatted: inspect(long_card_data),
      hu_formatted: inspect(hu_card_data)
    )

    message = %{
      # MainProto.XC
      "mainId" => 5,
      # SC_LHD_SETTLEMENT_P_NEW - 3928
      "subId" => 3928,
      "data" => %{
        # 客户端期望 roundid
        "roundid" => state.game_data.round,
        # 客户端期望 resultPos (1=龙, 2=虎, 3=和)
        "resultPos" => result_to_direction(result),
        # 获胜区域的总下注金额
        "win_value" => calculate_win_value(state, result),
        # 龙牌数据
        "long_card" => long_card_data,
        # 虎牌数据
        "hu_card" => hu_card_data,
        # 玩家结算数据，格式: "playerid" => "剩余金币,输赢金币"
        "other" => other_data,
        # 当前玩家ID (可选)
        "_playerid" => nil,
        # 下一轮时间 (可选)
        "nextat" => nil,
        # 庄家数据 - 客户端期望的格式
        "zhuang" => zhuang_data,
        # 唯一消息ID，用于追踪重复
        "message_id" => message_id
      }
    }

    Logger.info("📡 [BROADCAST_SETTLEMENT] 结算消息ID: #{message_id}")
    Logger.info("📡 [BROADCAST_SETTLEMENT] 结算消息内容: #{inspect(message)}")
    Logger.info("📡 [BROADCAST_SETTLEMENT] 开始广播给房间内所有玩家 - 玩家数量: #{map_size(state.players)}")

    Logger.info("📡 [BROADCAST_SETTLEMENT] 结算消息广播完成 - 协议: 5/3928, 消息ID: #{message_id}")

    broadcast_to_room(state, message)
  end

  # 处理成功的下注
  defp process_successful_bet(state, player, area, amount, user_bets, current_bet, new_bet) do
    numeric_id = player.numeric_id
    # 更新玩家下注

    new_user_bets = Map.put(user_bets, area, new_bet)
    new_bets = Map.put(state.game_data.bets, numeric_id, new_user_bets)

    # 更新总下注
    old_total_bets = state.game_data.total_bets
    new_total_bets = calculate_total_bets(new_bets)

    Logger.info(
      "📊 [BET_TOTALS] 总下注更新 - 旧: #{inspect(old_total_bets)}, 新: #{inspect(new_total_bets)}"
    )

    game_data = %{state.game_data | bets: new_bets, total_bets: new_total_bets}

    Logger.info(
      "🎉 [BET_SUCCESS] 下注处理完成 - 用户: #{numeric_id}, 区域: #{area}, 金额: #{amount}, 总计: #{new_bet}"
    )

    %{state | game_data: game_data}
    # 发送下注成功确认给玩家 (SC_LHD_BET_SUCCESS 3926) - 发送给下注玩家
    |> send_bet_success_confirmation(player, area, amount)
    # 广播下注同步 (SC_LHD_BET_SYNC 3927) - 广播给所有玩家
    |> broadcast_bet_sync(player, area, amount)
  end

  # 更新所有真实玩家的积分信息（结算后同步最新数据）
  defp update_players_with_latest_points(state, settlements) do
    # 只更新有结算记录且赢钱的真实玩家
    Enum.reduce(settlements, state, fn {numeric_id, win_amount}, acc_state ->
      if win_amount > 0 do
        acc_state
        |> add_player_points(numeric_id, win_amount)
      else
        acc_state
      end
    end)
  end

  # 发送游戏状态给指定用户
  defp send_game_state_to_user(state, player) do
    game_state_data = format_game_state_for_client(state)
    message = LongHuMessageBuilder.build_game_state(state, game_state_data)

    log_info("SEND_GAME_STATE", "发送游戏状态给用户",
      user: player.numeric_id,
      phase: state.game_data.phase
    )

    send_to_player(state, player, message)
  end

  # 广播游戏状态给所有玩家 (SC_LHD_GAMESTATE_P)
  defp broadcast_game_state(state) do
    # 使用统一配置的协议格式: MainProto.XC (5) + SC_LHD_GAMESTATE_P
    game_state_data = format_game_state_for_client(state)

    message = %{
      # MainProto.XC
      "mainId" => 5,
      # SC_LHD_GAMESTATE_P - 3901
      "subId" => 3901,
      "data" => game_state_data
    }

    Logger.info("📤 [BROADCAST_GAME_STATE] 广播游戏状态 - 状态: #{state.game_data.phase}")
    broadcast_to_room(state, message)
  end

  # 格式化游戏状态为客户端期望的格式
  defp format_game_state_for_client(state) do
    # 计算剩余时间
    remaining_time = calculate_remaining_time(state)

    # 计算剩余下注限制
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    # 根据当前游戏阶段返回对应的状态数据
    case state.game_data.phase do
      :betting ->
        %{
          # LHD_GameState_BuyHorse (下注阶段)
          "state" => 1,
          "round" => state.game_data.round,
          "bet_time" => state.game_data.config.bet_time,
          "remaining_time" => remaining_time,
          "total_bets" => state.game_data.total_bets,
          "leftbet" => remaining_bet_limits
        }

      :dealing ->
        %{
          # LHD_GameState_Combine (亮牌阶段)
          "state" => 2,
          "round" => state.game_data.round,
          "deal_time" => state.game_data.config.deal_time,
          "remaining_time" => remaining_time
        }

      :revealing ->
        %{
          # LHD_GameState_Combine (亮牌阶段)
          "state" => 2,
          "round" => state.game_data.round,
          "reveal_time" => state.game_data.config.reveal_time,
          "remaining_time" => remaining_time
        }

      :settling ->
        %{
          # LHD_GameState_End (结算阶段)
          "state" => 3,
          "round" => state.game_data.round,
          "settle_time" => state.game_data.config.settle_time,
          "remaining_time" => remaining_time
        }

      :waiting ->
        %{
          # LHD_GameState_End (游戏休息,等下一局开始)
          "state" => 3,
          "round" => state.game_data.round,
          "next_round_delay" => 2000,
          "remaining_time" => remaining_time
        }

      _ ->
        %{
          # LHD_GameState_None (无状态)
          "state" => 0,
          "round" => state.game_data.round,
          "remaining_time" => 0
        }
    end
  end

  # 辅助函数：获取玩家在各区域的下注总额
  defp get_player_bets(state, numeric_id) do
    player_bets = Map.get(state.game_data.bets, numeric_id, %{})

    %{
      long: Map.get(player_bets, :long, 0),
      hu: Map.get(player_bets, :hu, 0),
      he: Map.get(player_bets, :he, 0)
    }
  end

  # 辅助函数：计算剩余下注限制 (1=龙, 2=虎, 3=和) - 兼容旧版本
  # 注意：这个函数返回的是全局剩余限制，不考虑单个玩家的单区域限制
  defp calculate_remaining_bet_limits(state) do
    current_banker = state.game_data.banker

    if current_banker.is_system do
      # 系统坐庄：使用配置的区域限制
      area_limits = state.game_data.config.area_bet_limits
      total_bets = state.game_data.total_bets

      %{
        # 龙区域剩余
        "1" => max(0, Map.get(area_limits, :long, 100_000) - Map.get(total_bets, :long, 0)),
        # 虎区域剩余
        "2" => max(0, Map.get(area_limits, :hu, 100_000) - Map.get(total_bets, :hu, 0)),
        # 和区域剩余
        "3" => max(0, Map.get(area_limits, :he, 50000) - Map.get(total_bets, :he, 0))
      }
    else
      # 玩家坐庄：根据庄家余额计算各区域最大可下注金额
      banker_balance = get_player_points(state, current_banker.numeric_id)

      # 计算各区域的最大可下注金额
      %{
        "1" => calculate_max_bet_for_area(state, :long, banker_balance),
        "2" => calculate_max_bet_for_area(state, :hu, banker_balance),
        "3" => calculate_max_bet_for_area(state, :he, banker_balance)
      }
    end
  end

  # 计算特定玩家的剩余下注限制（考虑单区域限制）
  defp calculate_player_remaining_bet_limits(state, player_id) do
    # 获取全局剩余限制
    global_limits = calculate_remaining_bet_limits(state)

    # 获取单区域下注限制
    area_limit = get_single_area_bet_limit(state)

    # 获取玩家当前在各区域的下注
    player_bets = Map.get(state.game_data.bets, player_id, %{})

    # 计算玩家在各区域的剩余下注额度（取全局限制和单区域限制的最小值）
    %{
      # 龙区域剩余 = min(全局剩余, 单区域剩余)
      "1" => min(
        Map.get(global_limits, "1", 0),
        max(0, area_limit - Map.get(player_bets, :long, 0))
      ),
      # 虎区域剩余 = min(全局剩余, 单区域剩余)
      "2" => min(
        Map.get(global_limits, "2", 0),
        max(0, area_limit - Map.get(player_bets, :hu, 0))
      ),
      # 和区域剩余 = min(全局剩余, 单区域剩余)
      "3" => min(
        Map.get(global_limits, "3", 0),
        max(0, area_limit - Map.get(player_bets, :he, 0))
      )
    }
  end

  # 辅助函数：计算在庄家赔付能力范围内，某区域的最大可下注金额
  defp calculate_max_bet_for_area(state, area, banker_balance) do
    # 使用迭代方法来找到最大可下注金额，避免二分查找的复杂性
    # 设置一个合理的搜索上限（比如庄家余额的10倍，但通常不会达到）
    max_search_limit = min(banker_balance * 10, 100_000_000)

    find_max_bet_iterative(state, area, banker_balance, 0, max_search_limit, 1000)
  end

  # 迭代查找最大可下注金额
  defp find_max_bet_iterative(state, area, banker_balance, current_max, search_limit, step_size) do
    test_amount = current_max + step_size

    # 如果超过搜索限制，返回当前最大值
    if test_amount > search_limit do
      current_max
    else
      current_total_bets = state.game_data.total_bets
      test_total_bets = Map.update(current_total_bets, area, test_amount, &(&1 + test_amount))

      # 计算庄家总可用资金 = 初始余额 + 所有下注总额
      total_bet_amount = Map.get(test_total_bets, :long, 0) +
                        Map.get(test_total_bets, :hu, 0) +
                        Map.get(test_total_bets, :he, 0)
      banker_total_funds = banker_balance + total_bet_amount

      max_payout = calculate_banker_max_payout(test_total_bets, state.game_data.odds)

      if max_payout <= banker_total_funds do
        # 可以承受，继续尝试更大的金额
        find_max_bet_iterative(state, area, banker_balance, test_amount, search_limit, step_size)
      else
        # 不能承受，返回当前最大值
        # 如果步长大于1，尝试用更小的步长精确查找
        if step_size > 1 do
          new_step = max(1, div(step_size, 10))
          find_max_bet_iterative(state, area, banker_balance, current_max, current_max + step_size, new_step)
        else
          current_max
        end
      end
    end
  end

  # 辅助函数：将区域转换为客户端期望的direction - 使用统一配置
  defp area_to_direction(area) do
    bet_areas = LongHuGame.bet_areas()

    case area do
      "long" -> bet_areas.long
      "hu" -> bet_areas.hu
      "he" -> bet_areas.he
      :long -> bet_areas.long
      :hu -> bet_areas.hu
      :he -> bet_areas.he
      # 默认为龙
      _ -> bet_areas.long
    end
  end

  # 辅助函数：计算剩余时间
  defp calculate_remaining_time(state) do
    case state.game_data.phase_timer do
      nil ->
        0

      timer_ref ->
        case Process.read_timer(timer_ref) do
          # 计时器已过期
          false -> 0
          # 转换为秒
          time_left -> div(time_left, 1000)
        end
    end
  end

  # 广播筹码增量信息 (SC_LHD_BET_SYNC)
  defp broadcast_bet_sync(state, player, area, amount) do
    numeric_id = player.numeric_id
    # 使用统一配置的协议格式: MainProto.XC (5) + SC_LHD_BET_SYNC
    # 客户端期望的筹码增量信息，使用位运算编码

    # 获取玩家在各区域的下注总额
    # player_bets = get_player_bets(state, numeric_id)

    # 计算更新后的区域下注限制 (剩余可下注额度)
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    # 根据客户端 onBetSYNC 函数分析，需要编码筹码信息
    # 客户端使用位运算解码：tabArr: [0, 4, 8, 12, 16, 20]
    # 每个筹码面值对应4位，最多支持6种筹码面值
    encoded_bets =
      encode_bet_for_sync(amount, area)
      |> Map.put("chouma", get_player_points(state, player.numeric_id))

    Logger.info("🎰 [BROADCAST_BET_SYNC] 构建筹码增量广播 - 用户: #{numeric_id}, 区域: #{area}, 金额: #{amount}")

    Logger.info("🎰 [BROADCAST_BET_SYNC] 编码后的下注数据: #{inspect(encoded_bets)}")

    message = %{
      # MainProto.XC
      "mainId" => 5,
      # SC_LHD_BET_SYNC - 3927
      "subId" => 3927,
      "data" => %{
        "roundid" => state.game_data.round,
        # 当前总下注
        "total" => state.game_data.total_bets,
        # 剩余下注限制 (全局)
        "leftbet" => remaining_bet_limits,
        # 玩家筹码增量信息 - 使用 numeric_id 作为键
        numeric_id => encoded_bets
      }
    }

    Logger.info("📤 [BROADCAST_BET_SYNC] 发送筹码增量广播 - 协议: 5/3927, 消息: #{inspect(message)}")
    Logger.info("🚫 [BROADCAST_BET_SYNC] 排除下注玩家: #{numeric_id}")
    # 排除下注玩家
    Logger.info("✅ [BROADCAST_BET_SYNC] 筹码增量广播已发送 (已排除下注玩家)")
    broadcast_to_room(state, message)
  end

  # 辅助函数：编码下注信息为客户端期望的位运算格式
  defp encode_bet_for_sync(amount, area) do
    # 使用统一配置的筹码面值
    chip_values = LongHuGame.chip_values()

    # 计算每种筹码的数量
    {encoded_long, encoded_hu, encoded_he} =
      case area do
        :long -> {encode_chips(amount, chip_values), 0, 0}
        "long" -> {encode_chips(amount, chip_values), 0, 0}
        :hu -> {0, encode_chips(amount, chip_values), 0}
        "hu" -> {0, encode_chips(amount, chip_values), 0}
        :he -> {0, 0, encode_chips(amount, chip_values)}
        "he" -> {0, 0, encode_chips(amount, chip_values)}
        _ -> {encode_chips(amount, chip_values), 0, 0}
      end

    %{
      "long" => encoded_long,
      "hu" => encoded_hu,
      "he" => encoded_he,
      "dirctionall" =>
        case area do
          :long -> amount
          "long" -> amount
          :hu -> amount
          "hu" -> amount
          :he -> amount
          "he" -> amount
          _ -> amount
        end
    }
  end

  # 辅助函数：将金额编码为筹码位运算格式
  defp encode_chips(amount, chip_values) do
    # 贪心算法：从大到小使用筹码，所以需要反转筹码列表
    {encoded, _remaining} =
      chip_values
      |> Enum.reverse()
      |> Enum.reduce({0, amount}, fn chip_value, {acc_encoded, remaining} ->
        if remaining >= chip_value do
          # 每种筹码最多15个 (4位)
          chip_count = min(div(remaining, chip_value), 15)
          used_amount = chip_count * chip_value
          new_remaining = remaining - used_amount

          # 使用统一配置的筹码位移
          shift_bits = LongHuGame.chip_bit_shift(chip_value)

          new_encoded = Bitwise.bor(acc_encoded, Bitwise.bsl(chip_count, shift_bits))
          {new_encoded, new_remaining}
        else
          {acc_encoded, remaining}
        end
      end)

    encoded
  end

  # 发送下注成功确认
  defp send_bet_success_confirmation(state, player, area, amount) do
    numeric_id = player.numeric_id
    player_bets = get_player_bets(state, numeric_id)
    direction = area_to_direction(area)
    remaining_bet_limits = calculate_remaining_bet_limits(state)
    current_money = get_player_points(state, player.numeric_id)

    log_info("SEND_BET_SUCCESS", "构建下注成功确认",
      user: numeric_id,
      area: "#{area}(#{direction})",
      amount: amount,
      bets: "龙:#{player_bets.long} 虎:#{player_bets.hu} 和:#{player_bets.he}"
    )

    message =
      LongHuMessageBuilder.build_bet_success(
        player_bets,
        direction,
        amount,
        state.game_data.total_bets,
        current_money,
        remaining_bet_limits
      )

    send_to_player(state, player, message)
    log_info("SEND_BET_SUCCESS", "下注成功确认已发送")

    state
  end

  # ==================== 庄家系统处理函数 ====================

  # 处理申请上庄
  defp handle_apply_banker(state, player) do
    numeric_id = player.numeric_id
    Logger.info("🏦 [APPLY_BANKER] 处理申请上庄 - 用户: #{numeric_id}")

    # 检查用户是否已在队列中
    already_in_queue =
      Enum.any?(state.game_data.banker_queue, fn banker -> banker.numeric_id == numeric_id end)

    # 获取玩家真实积分
    player_points = get_player_points(state, numeric_id)
    min_banker_money = state.game_data.config.banker.min_banker_money

    cond do
      already_in_queue ->
        Logger.warning("❌ [APPLY_BANKER] 用户已在上庄队列中 - 用户: #{numeric_id}")
        # GAME_ERROR_APPLYZHUANG_OK (已申请)
        send_banker_error(state, player, 7)
        state

      player_points < min_banker_money ->
        Logger.warning(
          "❌ [APPLY_BANKER] 用户积分不足 - 用户: #{numeric_id}, 当前积分: #{player_points}, 需要: #{min_banker_money}"
        )

        # GAME_ERROR_NOT_MONEY_TO_BET (积分不足)
        send_banker_error(state, player, 8)
        state

      # 检查是否为机器人（机器人不能申请上庄）
      Cypridina.Teen.GameSystem.PlayerData.is_robot?(player) ->
        Logger.warning("❌ [APPLY_BANKER] 机器人不能申请上庄 - 用户: #{numeric_id}")
        # GAME_ERROR_STATE_ERROR (状态错误)
        send_banker_error(state, player, 14)
        state

      true ->
        # 获取玩家真实信息
        player_name = Cypridina.Teen.GameSystem.PlayerData.get_display_name(player)

        # 添加到上庄队列
        new_banker = %{
          numeric_id: numeric_id,
          name: player_name,
          points: player_points,
          apply_time: System.system_time(:millisecond)
        }

        new_queue = state.game_data.banker_queue ++ [new_banker]
        game_data = %{state.game_data | banker_queue: new_queue}
        new_state = %{state | game_data: game_data}

        Logger.info(
          "✅ [APPLY_BANKER] 申请上庄成功 - 用户: #{numeric_id}, 姓名: #{player_name}, 积分: #{player_points}, 队列长度: #{length(new_queue)}"
        )

        # 广播上庄列表更新
        broadcast_banker_list(new_state)

        # 发送申请成功消息
        send_banker_success(new_state, player, "申请上庄成功")

        new_state
    end
  end

  # 处理取消申请上庄
  defp handle_cancel_apply_banker(state, player) do
    numeric_id = player.numeric_id
    Logger.info("🏦 [CANCEL_APPLY_BANKER] 处理取消申请上庄 - 用户: #{numeric_id}")

    # 从队列中移除用户
    new_queue =
      Enum.reject(state.game_data.banker_queue, fn banker -> banker.numeric_id == numeric_id end)

    if length(new_queue) == length(state.game_data.banker_queue) do
      Logger.warning("❌ [CANCEL_APPLY_BANKER] 用户不在上庄队列中 - 用户: #{numeric_id}")
      # 通用错误
      send_banker_error(state, player, 1)
      state
    else
      game_data = %{state.game_data | banker_queue: new_queue}
      new_state = %{state | game_data: game_data}

      Logger.info("✅ [CANCEL_APPLY_BANKER] 取消申请上庄成功 - 用户: #{numeric_id}")

      # 广播上庄列表更新
      broadcast_banker_list(new_state)

      # 发送取消成功消息
      send_banker_success(new_state, player, "取消申请上庄成功")

      new_state
    end
  end

  # 处理申请下庄
  defp handle_apply_off_banker(state, player) do
    numeric_id = player.numeric_id
    Logger.info("🏦 [APPLY_OFF_BANKER] 处理申请下庄 - 用户: #{numeric_id}")

    current_banker = state.game_data.banker

    cond do
      current_banker.numeric_id != numeric_id ->
        Logger.warning("❌ [APPLY_OFF_BANKER] 用户不是当前庄家 - 用户: #{numeric_id}")
        # GAME_ERROR_OZ_STATE
        send_banker_error(state, player, 3)
        state

      current_banker.turns_played < current_banker.min_turns ->
        Logger.warning(
          "❌ [APPLY_OFF_BANKER] 坐庄局数不足 - 用户: #{numeric_id}, 已坐: #{current_banker.turns_played}, 最少: #{current_banker.min_turns}"
        )

        # GAME_ERROR_NOT_ROUND
        send_banker_error(state, player, 2)
        # 发送还需坐庄局数提示
        remaining_rounds = current_banker.min_turns - current_banker.turns_played
        send_banker_notice(state, player, remaining_rounds)
        state

      true ->
        # 添加到下庄申请列表
        if numeric_id not in state.game_data.banker_off_requests do
          new_off_requests = [numeric_id | state.game_data.banker_off_requests]
          game_data = %{state.game_data | banker_off_requests: new_off_requests}
          new_state = %{state | game_data: game_data}

          Logger.info("✅ [APPLY_OFF_BANKER] 申请下庄成功 - 用户: #{numeric_id}")

          new_state
          # 发送申请下庄成功消息
          |> send_banker_success(player, "申请下庄成功，将在下一轮下庄")
        else
          Logger.info("ℹ️ [APPLY_OFF_BANKER] 用户已申请下庄 - 用户: #{numeric_id}")

          state
          |> send_banker_success(player, "已申请下庄")
        end
    end
  end

  # 处理请求上庄列表
  defp handle_request_banker_list(state, player) do
    numeric_id = player.numeric_id
    Logger.info("🏦 [REQUEST_BANKER_LIST] 处理请求上庄列表 - 用户: #{numeric_id}")

    # 直接发送当前上庄列表给请求用户
    send_banker_list_to_user(state, player)
  end

  # 处理请求玩家列表
  defp handle_request_player_list(state, player, page) do
    numeric_id = player.numeric_id
    Logger.info("👥 [REQUEST_PLAYER_LIST] 处理请求玩家列表 - 用户: #{numeric_id}, 页码: #{page}")

    # 发送玩家列表给请求用户
    send_player_list_to_user(state, player, page)
  end

  # 处理续押请求
  defp handle_follow_bet(state, player) do
    numeric_id = player.numeric_id
    Logger.info("🔄 [FOLLOW_BET] 处理续押请求 - 用户: #{numeric_id}")

    # 检查是否在下注阶段
    if state.game_data.phase != :betting do
      Logger.warning("❌ [FOLLOW_BET] 不在下注阶段 - 用户: #{numeric_id}")
      send_follow_bet_error(state, player, "当前不在下注阶段")
    else
      # 获取上一轮的下注记录
      last_bets = Map.get(state.game_data.last_round_bets, numeric_id, %{})

      if map_size(last_bets) == 0 do
        Logger.warning("❌ [FOLLOW_BET] 上一轮无下注记录 - 用户: #{numeric_id}")
        send_follow_bet_error(state, player, "上一轮无下注记录")
      else
        Logger.info("🔄 [FOLLOW_BET] 执行续押 - 用户: #{numeric_id}, 下注: #{inspect(last_bets)}")

        # 逐个执行上一轮的下注
        Enum.reduce(last_bets, state, fn {area, amount}, acc_state ->
          # 使用内部下注处理函数
          handle_bet(acc_state, player, Atom.to_string(area), amount)
        end)
        # 发送续押成功消息
        |> send_follow_bet_success(player, "续押成功")
      end
    end
  end

  # 辅助函数：格式化单张牌数据为客户端期望的格式
  defp format_card_for_client(card) do
    %{
      # 客户端期望的是color字段
      "color" => suit_to_color(card.suit),
      # 客户端期望的是number字段
      "number" => card.rank,
      "suit" => card.suit,
      "rank" => card.rank,
      "value" => card.value
    }
  end

  # 辅助函数：将花色转换为客户端期望的颜色值
  # 根据客户端资源文件 card.plist 的实际花色编码
  defp suit_to_color(suit) do
    case suit do
      # 红桃 (poker_3xx.png)
      :hearts -> 3
      # 方块 (poker_4xx.png)
      :diamonds -> 4
      # 梅花 (poker_5xx.png)
      :clubs -> 5
      # 黑桃 (poker_6xx.png)
      :spades -> 6
      # 默认红桃
      _ -> 3
    end
  end

  # 辅助函数：将结果转换为客户端期望的方向值 (1=龙, 2=虎, 3=和)
  defp result_to_direction(result) do
    case result do
      # 龙
      :long -> 1
      # 虎
      :hu -> 2
      # 和
      :tie -> 3
      # 和 (别名)
      :he -> 3
      # 默认为龙
      _ -> 1
    end
  end

  # 计算获胜区域的总下注金额
  defp calculate_win_value(state, result) do
    case result do
      # 龙区域的总下注
      :long -> state.game_data.total_bets.long
      # 虎区域的总下注
      :hu -> state.game_data.total_bets.hu
      # 和区域的总下注
      :tie -> state.game_data.total_bets.he
      _ -> 0
    end
  end

  # 游戏循环和机器人逻辑

  defp schedule_game_tick() do
    # 每秒检查一次游戏状态
    Process.send_after(self(), :game_tick, 1000)
  end

  # ==================== 庄家广播函数 ====================

  # 广播上庄列表更新
  defp broadcast_banker_list(state) do
    banker_list_data = format_banker_list_for_client(state)

    log_info("BROADCAST_BANKER_LIST", "广播上庄列表更新",
      queue_length: length(state.game_data.banker_queue)
    )

    broadcast_to_room(state, %{
      "mainId" => 5,
      "subId" => 3908,
      "data" => banker_list_data
    })
  end

  # 发送上庄列表给指定用户
  @doc """
  计算玩家的净利润变化（用于RoomBase事件系统）
  player_changes = 结算金额 - 下注总额
  """
  defp calculate_player_changes(state, settlements) do
    settlements
    |> Enum.into(%{}, fn {numeric_id, settlement_amount} ->
      # 计算该玩家的下注总额
      user_bets = Map.get(state.game_data.bets, numeric_id, %{})
      total_bet = Enum.reduce(user_bets, 0, fn {_area, amount}, acc -> acc + amount end)

      # 净利润 = 结算金额 - 下注总额
      net_profit = settlement_amount - total_bet

      {numeric_id, net_profit}
    end)
  end

  defp send_banker_list_to_user(state, player) do
    banker_list_data = format_banker_list_for_client(state)

    log_info("SEND_BANKER_LIST", "发送上庄列表给用户", user: player.numeric_id)

    send_to_player(state, player, %{
      "mainId" => 5,
      "subId" => 3908,
      "data" => banker_list_data
    })
  end

  # 广播庄家信息更新
  defp broadcast_banker_info(state) do
    banker_info_data = format_banker_info_for_client(state)

    log_info("BROADCAST_BANKER_INFO", "广播庄家信息更新", banker: state.game_data.banker.numeric_id)

    message = LongHuMessageBuilder.build_banker_info(banker_info_data)
    broadcast_to_room(state, message)
  end

  # 发送庄家信息给指定用户
  defp send_banker_info_to_user(state, player) do
    banker_info_data = format_banker_info_for_client(state)

    log_info("SEND_BANKER_INFO", "发送庄家信息给用户", user: player.numeric_id)

    message = LongHuMessageBuilder.build_banker_info(banker_info_data)
    send_to_player(state, player, message)
  end

  # 发送庄家操作成功消息
  defp send_banker_success(state, player, message) do
    log_info("SEND_BANKER_SUCCESS", "发送庄家操作成功", user: player.numeric_id, message: message)

    response = LongHuMessageBuilder.build_banker_response(1, message)
    send_to_player(state, player, response)
  end

  # 发送庄家操作错误消息
  defp send_banker_error(state, player, error_code) do
    log_info("SEND_BANKER_ERROR", "发送庄家操作错误", user: player.numeric_id, error_code: error_code)

    response = LongHuMessageBuilder.build_banker_response(error_code)
    send_to_player(state, player, response)
  end

  # 发送庄家下庄通知
  defp send_banker_notice(state, player, remaining_rounds) do
    log_info("SEND_BANKER_NOTICE", "发送庄家下庄通知",
      user: player.numeric_id,
      remaining_rounds: remaining_rounds
    )

    response = LongHuMessageBuilder.build_banker_notice(remaining_rounds)
    send_to_player(state, player, response)
  end

  # 处理机器人管理消息
  def handle_info(:manage_robots, state) do
    if state.game_data.config.enable_robots do
      # 检查当前游戏阶段，在下注阶段不清理机器人以避免下注数据不一致
      if state.game_data.phase == :betting do
        Logger.info("🤖 [MANAGE_ROBOTS] 跳过管理 - 当前处于下注阶段，避免数据不一致")
        # 缩短检查间隔，在下注结束后尽快处理
        Process.send_after(self(), :manage_robots, 5_000)
        {:noreply, state}
      else
        Logger.info("🤖 [MANAGE_ROBOTS] 开始动态管理机器人 - 房间: #{state.id}")

        # 执行动态机器人管理
        new_state = LongHuAI.manage_robots_dynamically(state)

        # 广播玩家数量变化
        broadcast_player_count_change(new_state)

        # 安排下次检查（30秒后）
        Process.send_after(self(), :manage_robots, 30_000)

        {:noreply, new_state}
      end
    else
      {:noreply, state}
    end
  end

  # 处理机器人下注消息
  def handle_info({:robot_bet, robot_id}, state) do
    if state.game_data.phase == :betting do
      # 获取机器人信息
      robot_player = Map.get(state.players, robot_id)

      if robot_player && robot_player.is_robot do
        # 检查机器人积分是否足够
        robot_points = get_player_points(state, robot_id)

        if robot_points < 1000 do
          # 积分不足，移除机器人
          Logger.info("🤖 [ROBOT_BET] 机器人积分不足，移除: #{robot_id} (积分: #{robot_points})")
          new_state = LongHuAI.remove_robot_from_room(state, robot_id)
          {:noreply, new_state}
        else
          # 生成智能机器人下注决策（避免单边下注）
          {area, amount} = LongHuAI.generate_smart_robot_bet(robot_player, state)

          # 执行机器人下注
          new_state = execute_robot_single_bet(state, robot_player, area, amount)

          {:noreply, new_state}
        end
      else
        Logger.warning("🤖 [ROBOT_BET] 机器人不存在或无效: #{robot_id}")
        {:noreply, state}
      end
    else
      Logger.debug("🤖 [ROBOT_BET] 游戏阶段不是下注阶段: #{state.game_data.phase}")
      {:noreply, state}
    end
  end

  # 执行机器人下注
  defp execute_robot_single_bet(state, robot_player, area, amount) do
    robot_id = robot_player.numeric_id

    # 执行机器人下注
    new_state = handle_bet(state, robot_player, Atom.to_string(area), amount)
    new_robot_points = get_player_points(new_state, robot_id)

    # 获取机器人当前的下注分布
    current_bets = Map.get(new_state.game_data.bets, robot_id, %{})
    bet_areas = Map.keys(current_bets)

    bet_style =
      Cypridina.Teen.GameSystem.PlayerDataBuilder.get_robot_ai_attribute(
        robot_player,
        :bet_style
      ) || :moderate

    Logger.info(
      "🤖 [ROBOT_BET] 机器人智能下注: #{robot_id} -> #{area}: #{amount} (风格: #{bet_style}, 已下注区域: #{inspect(bet_areas)}, 剩余积分: #{new_robot_points})"
    )

    new_state
  end

  # 处理机器人跟风下注消息
  def handle_info({:robot_follow_bet, robot_id}, state) do
    if state.game_data.phase == :betting do
      # 获取机器人信息
      robot_player = Map.get(state.players, robot_id)

      if robot_player && robot_player.is_robot do
        # 检查机器人积分是否足够
        robot_points = get_player_points(state, robot_id)

        if robot_points < 1000 do
          # 积分不足，移除机器人
          Logger.info("🤖 [ROBOT_FOLLOW_BET] 机器人积分不足，移除: #{robot_id} (积分: #{robot_points})")
          new_state = LongHuAI.remove_robot_from_room(state, robot_id)
          {:noreply, new_state}
        else
          # 生成跟风下注决策（倾向于选择当前最热门的区域）
          {area, amount} = generate_follow_bet_decision(robot_player, state)

          # 执行机器人下注
          new_state = handle_bet(state, robot_player, Atom.to_string(area), amount)
          new_robot_points = get_player_points(new_state, robot_id)

          Logger.info(
            "🤖 [ROBOT_FOLLOW_BET] 机器人跟风下注: #{robot_id} -> #{area}: #{amount} (剩余积分: #{new_robot_points})"
          )

          {:noreply, new_state}
        end
      else
        Logger.warning("🤖 [ROBOT_FOLLOW_BET] 机器人不存在或无效: #{robot_id}")
        {:noreply, state}
      end
    else
      Logger.debug("🤖 [ROBOT_FOLLOW_BET] 游戏阶段不是下注阶段: #{state.game_data.phase}")
      {:noreply, state}
    end
  end

  # 生成跟风下注决策
  defp generate_follow_bet_decision(robot_player, state) do
    total_bets = state.game_data.total_bets

    # 找到当前最热门的区域
    popular_area =
      if map_size(total_bets) > 0 do
        total_bets
        |> Enum.max_by(fn {_area, amount} -> amount end)
        |> elem(0)
      else
        # 如果没有下注，随机选择
        Enum.random([:long, :hu, :he])
      end

    # 80%概率跟风热门区域，20%概率选择其他区域
    area =
      if :rand.uniform() < 0.8 do
        popular_area
      else
        [:long, :hu, :he]
        |> Enum.reject(&(&1 == popular_area))
        |> Enum.random()
      end

    # 跟风下注通常金额较小
    ai_data = Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_data(robot_player)
    aggression = Map.get(ai_data, :aggression, 0.5)

    # 使用较小的下注金额
    chip_values = LongHuGame.chip_values()
    # 只使用前3种较小的筹码
    small_chips = Enum.take(chip_values, 3)
    selected_chip = Enum.random(small_chips)

    # 跟风下注数量较少
    # 1-3个筹码
    quantity = 1 + :rand.uniform(2)
    amount = selected_chip * quantity

    # 确保在游戏限制范围内
    config = state.game_data.config
    final_amount = max(config.min_bet, min(amount, config.max_bet))

    {area, final_amount}
  end

  # 处理阶段超时
  def handle_info(:phase_timeout, state) do
    Logger.info(
      "🐉 [PHASE_TIMEOUT] 阶段超时触发 - 当前阶段: #{state.game_data.phase}, 回合: #{state.game_data.round}"
    )

    new_state =
      case state.game_data.phase do
        :betting ->
          Logger.info("🐉 [PHASE_TIMEOUT] 下注阶段超时，转换到发牌阶段")
          transition_to_dealing(state)

        :dealing ->
          Logger.info("🐉 [PHASE_TIMEOUT] 发牌阶段超时，转换到亮牌阶段")
          transition_to_revealing(state)

        :revealing ->
          Logger.info("🐉 [PHASE_TIMEOUT] 亮牌阶段超时，转换到结算阶段")
          transition_to_settling(state)

        :settling ->
          Logger.info("🐉 [PHASE_TIMEOUT] 结算阶段超时，转换到等待阶段")
          transition_to_waiting(state)

        _ ->
          Logger.warning("🐉 [PHASE_TIMEOUT] 未知阶段超时: #{state.game_data.phase}")
          state
      end

    {:noreply, new_state}
  end

  # 处理开始新一轮消息
  def handle_info(:start_new_round, state) do
    Logger.info("🐉 [LONGHU] 开始新一轮游戏")

    new_state = on_game_start(state)
    {:noreply, new_state}
  end

  @impl true
  def handle_game_tick(state) do
    # 继续调度下一次tick
    if state.game_data.phase != :waiting do
      schedule_game_tick()
    end

    state
  end

  # ==================== 数据格式化函数 ====================

  # 格式化上庄列表为客户端期望的格式
  defp format_banker_list_for_client(state) do
    # 客户端期望的格式参考 onUpdateBankerList 函数
    banker_list =
      state.game_data.banker_queue
      |> Enum.with_index(1)
      |> Enum.into(%{}, fn {banker, index} ->
        # 获取玩家当前真实积分（如果玩家还在房间中）
        current_points =
          case Map.get(state.players, banker.numeric_id) do
            nil ->
              # 玩家已离开房间，使用申请时的积分
              banker.points

            player ->
              # 玩家还在房间，获取当前真实积分
              get_player_points(state, banker.numeric_id)
          end

        {index,
         %{
           "playerid" => banker.numeric_id,
          #  "headid" => get_player_head_id(banker),
           "nickname" => banker.name,
           # ✅ 使用当前真实积分
           "coin" => current_points,
           "sort" => index
         }}
      end)

    %{
      "list" => banker_list,
      "isempty" => state.game_data.banker_queue |> Enum.empty?()
    }
  end

  # 格式化庄家信息为客户端期望的格式
  defp format_banker_info_for_client(state) do
    banker = state.game_data.banker
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    # 客户端期望的格式参考 onBankerInfo 函数
    %{
      # 庄家头像ID (系统庄家用-1)
      "zhuangheadid" => banker.numeric_id || -1,
      # 庄家ID (系统庄家用-1)
      "zhuangid" => banker.numeric_id || -1,
      # 已坐庄局数
      "zhuangturn" => banker.turns_played,
      # 庄家积分
      "chouma" => banker.points,
      # 庄家名称
      "zhuangname" => banker.name,
      # 是否系统庄家
      "issystem" => if(banker.is_system, do: 1, else: 0),
      # 剩余下注限制
      "leftbet" => remaining_bet_limits,
      # 上庄列表 (可选)
      "list" => format_banker_list_for_client(state)
    }
  end

  # ==================== 庄家轮换逻辑 ====================

  # 处理庄家轮换
  defp handle_banker_rotation(state) do
    current_banker = state.game_data.banker

    Logger.info(
      "🏦 [BANKER_ROTATION] 检查庄家轮换 - 当前庄家: #{current_banker.numeric_id}, 已坐庄: #{current_banker.turns_played}"
    )

    # 更新庄家的真实积分（如果是玩家庄家）
    updated_banker =
      if current_banker.is_system do
        current_banker
      else
        case Map.get(state.players, current_banker.numeric_id) do
          # 庄家已离开，保持原积分
          nil ->
            current_banker

          player ->
            current_points = get_player_points(state, current_banker.numeric_id)
            %{current_banker | points: current_points}
        end
      end

    # 增加庄家坐庄局数
    updated_banker = %{updated_banker | turns_played: updated_banker.turns_played + 1}

    # 检查是否需要下庄
    should_rotate =
      should_banker_rotate?(
        updated_banker,
        state.game_data.banker_off_requests,
        state.game_data.banker_queue,
        state
      )

    if should_rotate do
      Logger.info(
        "🏦 [BANKER_ROTATION] 庄家需要下庄 - 原因: #{get_rotation_reason(updated_banker, state.game_data.banker_off_requests)}"
      )

      rotate_banker(state, updated_banker)
    else
      # 更新庄家局数和积分
      game_data = %{state.game_data | banker: updated_banker}
      new_state = %{state | game_data: game_data}

      # 广播庄家信息更新
      broadcast_banker_info(new_state)
    end
  end

  # 判断庄家是否应该下庄
  defp should_banker_rotate?(banker, off_requests, banker_queue, state) do
    cond do
      # 系统庄家：有玩家申请上庄时让位
      banker.is_system and length(banker_queue) > 0 ->
        true

      # 系统庄家：没有玩家申请时继续坐庄
      banker.is_system ->
        false

      # 玩家庄家：达到最大坐庄局数（强制下庄，无论是否申请）
      not banker.is_system and banker.turns_played >= banker.max_turns ->
        Logger.info(
          "🏦 [BANKER_ROTATION] 玩家庄家达到最大坐庄局数: #{banker.turns_played}/#{banker.max_turns}"
        )

        true

      # 庄家申请下庄且已达到最小局数
      banker.numeric_id in off_requests and banker.turns_played >= banker.min_turns ->
        true

      # 庄家积分不足（检查真实积分）
      not banker.is_system ->
        case Map.get(state.players, banker.numeric_id) do
          nil ->
            # 庄家已离开房间，需要下庄
            Logger.warning("🏦 [BANKER_CHECK] 庄家已离开房间: #{banker.numeric_id}")
            true

          player ->
            current_points = get_player_points(state, banker.numeric_id)
            min_banker_money = state.game_data.config.banker.min_banker_money

            if current_points < min_banker_money do
              Logger.warning(
                "🏦 [BANKER_CHECK] 庄家积分不足: #{banker.numeric_id}, 当前: #{current_points}, 需要: #{min_banker_money}"
              )

              true
            else
              false
            end
        end

      true ->
        false
    end
  end

  # 获取下庄原因
  defp get_rotation_reason(banker, off_requests) do
    cond do
      not banker.is_system and banker.turns_played >= banker.max_turns ->
        "玩家坐庄达到最大局数限制(#{banker.max_turns}局)"

      banker.numeric_id in off_requests ->
        "庄家申请下庄"

      banker.is_system ->
        "系统庄家让位给玩家"

      true ->
        "其他原因"
    end
  end

  # 执行庄家轮换
  defp rotate_banker(state, current_banker) do
    Logger.info("🏦 [ROTATE_BANKER] 执行庄家轮换")

    # 清除当前庄家的下庄申请
    new_off_requests =
      List.delete(state.game_data.banker_off_requests, current_banker.numeric_id)

    # 选择下一个庄家
    {new_banker, remaining_queue} = select_next_banker(state.game_data.banker_queue, state)

    # 更新游戏数据
    game_data = %{
      state.game_data
      | banker: new_banker,
        banker_queue: remaining_queue,
        banker_off_requests: new_off_requests
    }

    Logger.info("🏦 [ROTATE_BANKER] 庄家轮换完成 - 新庄家: #{new_banker.numeric_id}")

    %{state | game_data: game_data}
    # 广播庄家变更
    |> broadcast_banker_change(current_banker, new_banker)
  end

  # 选择下一个庄家
  defp select_next_banker(banker_queue, state) do
    case banker_queue do
      [] ->
        # 没有排队的玩家，继续使用系统庄家
        Logger.info("🏦 [SELECT_BANKER] 无排队玩家，继续使用系统庄家")

        system_banker = %{
          numeric_id: nil,
          name: "系统庄家",
          points: 1_000_000,
          turns_played: 0,
          max_turns: 999,
          min_turns: 0,
          is_system: true
        }

        {system_banker, []}

      [next_banker | remaining_queue] ->
        # 使用队列中的下一个玩家
        Logger.info("🏦 [SELECT_BANKER] 选择队列中的下一个庄家: #{next_banker.numeric_id}")

        # 获取玩家当前真实积分（如果有state的话）
        current_points =
          if state do
            case Map.get(state.players, next_banker.numeric_id) do
              # 玩家已离开，使用申请时的积分
              nil -> next_banker.points
              player -> get_player_points(state, next_banker.numeric_id)
            end
          else
            # 没有state，使用申请时的积分
            next_banker.points
          end

        # 获取配置中的庄家限制
        banker_config =
          if state, do: state.game_data.config.banker, else: LongHuGame.config().banker

        player_banker = %{
          numeric_id: next_banker.numeric_id,
          name: next_banker.name,
          points: current_points,
          turns_played: 0,
          max_turns: 3,
          min_turns: 1,
          is_system: false
        }

        Logger.info(
          "🏦 [SELECT_BANKER] 新庄家信息 - ID: #{next_banker.numeric_id}, 姓名: #{next_banker.name}, 积分: #{current_points} 配置：#{inspect(banker_config)}"
        )

        {player_banker, remaining_queue}
    end
  end

  # 广播庄家变更
  defp broadcast_banker_change(state, old_banker, new_banker) do
    state
    # 广播新庄家信息
    |> broadcast_banker_info()
    # 广播上庄列表更新
    |> broadcast_banker_list()
  end

  # 发送玩家列表给指定用户
  defp send_player_list_to_user(state, player, page) do
    player_list_data = format_player_list_for_client(state, page)

    log_info("SEND_PLAYER_LIST", "发送玩家列表给用户",
      user: player.numeric_id,
      page: page,
      total_players: map_size(state.players),
      robots: count_robots_in_state(state),
      real_players: map_size(state.players) - count_robots_in_state(state)
    )

    send_to_player(state, player, %{
      "mainId" => @main_id,
      "subId" => 3921,
      "data" => player_list_data
    })
  end

  @doc """
  发送断线重连房间信息 (XC_ROOM_INFO_P - MainProto.XC=5, subId=0)
  """
  defp send_reconnect_room_info(state, player) do
    numeric_id = player.numeric_id
    log_info("SEND_RECONNECT", "发送断线重连房间信息", user: numeric_id)

    # 获取玩家当前筹码
    player_money = get_player_points(state, numeric_id)

    # 构建配置信息
    config_data = %{
      "Zhuang" => %{
        "Need" => state.game_data.config.banker.min_banker_money,
        "MinTurn" => state.game_data.config.banker.min_turns,
        "MaxTurn" => state.game_data.config.banker.max_turns
      }
    }

    # 构建赔率信息
    odds_data = %{
      "1" => LongHuGame.odds().long - 1,
      "2" => LongHuGame.odds().hu - 1,
      "3" => LongHuGame.odds().he - 1
    }

    # 构建下注限制信息
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    message = %{
      # MainProto.XC
      "mainId" => 5,
      # XC_ROOM_INFO_P
      "subId" => 0,
      "data" => %{
        # 玩家当前筹码
        "chouma" => player_money,
        # 下注限制
        "canbet" => remaining_bet_limits,
        # 配置信息
        "config" => config_data,
        # 赔率信息
        "Odds" => odds_data,
        # 当前游戏状态
        "state" => format_game_phase_for_client(state.game_data.phase),
        # 当前回合
        "round" => state.game_data.round,
        # 庄家信息
        "zhuangdata" => format_banker_info_for_client(state),
        # 如果在下注阶段，包含剩余时间
        "remaining_time" => calculate_remaining_time(state)
      }
    }

    log_info("SEND_RECONNECT", "断线重连信息已发送")
    send_to_player(state, player, message)
  end

  # 统计房间中的机器人数量
  defp count_robots_in_state(state) do
    state.players
    |> Enum.count(fn {_user_id, player} -> player.is_robot end)
  end

  @doc """
  格式化游戏阶段为客户端期望的格式
  """
  defp format_game_phase_for_client(phase) do
    case phase do
      # LHD_GameState_None
      :waiting -> 0
      # LHD_GameState_BuyHorse
      :betting -> 1
      # LHD_GameState_Combine
      :dealing -> 2
      # LHD_GameState_Combine
      :revealing -> 2
      # LHD_GameState_End
      :settling -> 3
      _ -> 0
    end
  end

  # 获取玩家统计数据
  defp get_player_statistics(state, numeric_id) do
    player_stats = Map.get(state.game_data.player_stats, numeric_id, %{})

    %{
      win_count: Map.get(player_stats, :win_count, 0),
      win_score: Map.get(player_stats, :win_score, 0),
      last_win_position: Map.get(player_stats, :last_win_position, 0),
      last_win_time: Map.get(player_stats, :last_win_time, DateTime.utc_now())
    }
  end

  # 更新玩家统计数据
  defp update_player_statistics(state, result, settlements) do
    # 确保 player_stats 字段存在
    current_stats = Map.get(state.game_data, :player_stats, %{})

    # 更新有下注的玩家统计（包括输赢的玩家）
    updated_stats =
      Enum.reduce(state.game_data.bets, current_stats, fn {numeric_id, user_bets}, acc_stats ->
        # 跳过没有下注的玩家
        if map_size(user_bets) == 0 do
          acc_stats
        else
          current_player_stats =
            Map.get(acc_stats, numeric_id, %{
              win_count: 0,
              win_score: 0,
              last_win_position: 0,
              last_win_time: DateTime.utc_now()
            })

          # 计算玩家在本轮的净赢利（赢得的钱减去投注的钱）
          {net_profit, total_bet} =
            calculate_player_net_profit(user_bets, result, state.game_data.odds)

          # 如果玩家赢钱（净赢利 > 0），更新统计
          if net_profit > 0 do
            updated_player_stats = %{
              win_count: current_player_stats.win_count + 1,
              win_score: current_player_stats.win_score + net_profit,
              last_win_position: result_to_direction(result),
              last_win_time: DateTime.utc_now()
            }

            Map.put(acc_stats, numeric_id, updated_player_stats)
          else
            # 即使输钱也要记录时间
            updated_player_stats = %{current_player_stats | last_win_time: DateTime.utc_now()}
            Map.put(acc_stats, numeric_id, updated_player_stats)
          end
        end
      end)

    # 更新游戏状态
    game_data = Map.put(state.game_data, :player_stats, updated_stats)
    %{state | game_data: game_data}
  end

  # 计算玩家净赢利（赢得的钱减去投注的钱）
  defp calculate_player_net_profit(user_bets, result, odds) do
    total_bet = Enum.reduce(user_bets, 0, fn {_area, amount}, acc -> acc + amount end)

    total_winnings =
      Enum.reduce(user_bets, 0, fn {area, amount}, acc ->
        if area == result do
          # 赢了这个区域，计算总赔付
          total_payout = amount * Map.get(odds, area, 1)
          acc + total_payout
        else
          # 输了这个区域，没有赔付
          acc
        end
      end)

    # 净赢利 = 总赔付 - 总投注
    net_profit = total_winnings - total_bet

    {net_profit, total_bet}
  end

  @doc """
  1) CS_LHD_ALLLIST_P //请求玩家列表
  参数1："page" //查询页, 0-查询所有排行榜数据与玩家列表。  1-n查询指定页的玩家信息
  2) SC_LHD_ALLLIST_P //返回玩家列表
      参数1："totalplayernum" //房间内玩家总数
      参数2："roundid" //回合ID
      参数3："betrank" //下注排行榜(只在page=0时返回)
              参数1："playerid" //玩家ID
              参数2："name" //玩家昵称
              参数3："headid" //玩家系统头像id
              参数4："wxheadurl" //自定义头像url
              参数5："coin" //玩家的金币
              参数6："bet" //玩家下注额
              参数7："winnum" //玩家赢的局数
              参数8："winscore" //玩家赢的金币
      参数4："winscorerank" //赢分排行榜(只在page=0时返回)
              参数1："playerid" //玩家ID
              参数2："name" //玩家昵称
              参数3："headid" //玩家系统头像id
              参数4："wxheadurl" //自定义头像url
              参数5："winpos" //赢的位置: 1-龙 2-虎 3-和
              参数6："bet" //玩家下注额
              参数7："winscore" //玩家赢的金币
              参数8："time" //当局时间
      参数5："playerlist" //房间内的玩家列表(通过page指定查询, 每页14条数据)
              参数1："playerid" //玩家ID
              参数2："name" //玩家昵称
              参数3："headid" //玩家系统头像id
              参数4："wxheadurl" //自定义头像url
              参数5："coin" //玩家的金币
  """
  # 格式化玩家列表为客户端期望的格式
  defp format_player_list_for_client(state, page) do
    # 每页显示的玩家数量 (文档要求每页14条数据)
    page_size = 14
    start_index = page * page_size

    # 获取所有玩家（包括机器人，按下注金额排序）
    all_players =
      state.players
      |> Enum.to_list()
      |> Enum.map(fn {numeric_id, player} ->
        # 计算玩家本轮总下注
        user_bets = Map.get(state.game_data.bets, numeric_id, %{})
        total_bet = Enum.reduce(user_bets, 0, fn {_area, amount}, acc -> acc + amount end)

        # 获取玩家真实积分
        player_points = get_player_points(state, player.numeric_id)

        # 获取玩家统计数据
        player_stats = get_player_statistics(state, numeric_id)

        log_info("PLAYER_LIST_DEBUG", "玩家数据",
          player_id: numeric_id,
          is_robot: player.is_robot,
          nickname: Map.get(player.user, :nickname, "未知"),
          points: player_points,
          total_bet: total_bet,
          win_count: player_stats.win_count,
          win_score: player_stats.win_score
        )

        {numeric_id, player, total_bet, player_points, player_stats}
      end)
      # 按下注金额降序排列
      |> Enum.sort_by(fn {_numeric_id, _player, total_bet, _points, _stats} -> -total_bet end)

    total_count = length(all_players)

    log_info("PLAYER_LIST_SUMMARY", "玩家列表统计",
      total_players: total_count,
      robots:
        Enum.count(all_players, fn {_id, player, _bet, _points, _stats} -> player.is_robot end),
      real_players:
        Enum.count(all_players, fn {_id, player, _bet, _points, _stats} -> not player.is_robot end)
    )

    # 如果是第0页，返回排行榜数据和玩家列表
    if page == 0 do
      # 下注排行榜 - 按下注金额排序
      betrank_players =
        all_players
        |> Enum.take(50)
        |> Enum.with_index(1)
        |> Enum.into(%{}, fn {{numeric_id, player, total_bet, player_points, player_stats}, index} ->
          {index,
           %{
             "playerid" => player.numeric_id,
             "name" => get_player_nickname(player),
             "headid" => Map.get(player.user, :avatar_id, 1),
             "wxheadurl" => Map.get(player.user, :avatar_url, ""),
             "coin" => player_points,
             "bet" => total_bet,
             "winnum" => player_stats.win_count,
             "winscore" => player_stats.win_score
           }}
        end)

      # 赢分排行榜 - 按赢分金额排序
      winscorerank_players =
        all_players
        |> Enum.sort_by(fn {_numeric_id, _player, _total_bet, _points, player_stats} ->
          -player_stats.win_score
        end)
        |> Enum.take(50)
        |> Enum.with_index(1)
        |> Enum.into(%{}, fn {{numeric_id, player, total_bet, player_points, player_stats}, index} ->
          {index,
           %{
             "playerid" => player.numeric_id,
             "name" => get_player_nickname(player),
             "headid" => Map.get(player.user, :avatar_id, 1),
             "wxheadurl" => Map.get(player.user, :avatar_url, ""),
             "winpos" => player_stats.last_win_position,
             "bet" => total_bet,
             "winscore" => player_stats.win_score,
             "time" => DateTime.to_unix(player_stats.last_win_time, :millisecond)
           }}
        end)

      # 第一页玩家列表 (page=0 时也返回第一页的 playerlist)
      first_page_players =
        all_players
        |> Enum.take(page_size)
        |> Enum.with_index(1)
        |> Enum.into(%{}, fn {{numeric_id, player, total_bet, player_points, _player_stats},
                              index} ->
          {index,
           %{
             "playerid" => player.numeric_id,
             "name" => get_player_nickname(player),
             "headid" => Map.get(player.user, :avatar_id, 1),
             "wxheadurl" => Map.get(player.user, :avatar_url, ""),
             "coin" => player_points
           }}
        end)

      # 客户端期望的格式 (参考文档) - page=0 时返回排行榜和第一页玩家列表
      %{
        "totalplayernum" => total_count,
        "roundid" => state.game_data.round,
        "betrank" => betrank_players,
        "winscorerank" => winscorerank_players,
        "playerlist" => first_page_players
      }
    else
      # 分页获取玩家列表
      page_players =
        all_players
        |> Enum.drop(start_index)
        |> Enum.take(page_size)
        |> Enum.with_index(start_index + 1)
        |> Enum.into(%{}, fn {{numeric_id, player, total_bet, player_points, _player_stats},
                              index} ->
          {index,
           %{
             "playerid" => player.numeric_id,
             "name" => get_player_nickname(player),
             "headid" => Map.get(player.user, :avatar_id, 1),
             "wxheadurl" => Map.get(player.user, :avatar_url, ""),
             "coin" => player_points
           }}
        end)

      # 分页数据格式
      %{
        "totalplayernum" => total_count,
        "roundid" => state.game_data.round,
        "playerlist" => page_players,
        "page" => page
      }
    end
  end

  # ==================== 续押、奖池、时间配置发送函数 ====================

  # 发送续押成功消息
  defp send_follow_bet_success(state, player, message) do
    numeric_id = player.numeric_id
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_FOLLOW_BUY_P
    success_data = %{
      "code" => 1,
      "msg" => message
    }

    response = %{
      # MainProto.XC
      "mainId" => 5,
      # SC_LHD_BET_SYNC (续押成功) - 3927
      "subId" => 3927,
      "data" => success_data
    }

    Logger.info("📤 [SEND_FOLLOW_BET_SUCCESS] 发送续押成功 - 用户: #{numeric_id}, 消息: #{message}")
    send_to_player(state, player, response)
  end

  # 发送续押错误消息
  defp send_follow_bet_error(state, player, message) do
    numeric_id = player.numeric_id
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_FOLLOW_BUY_P
    error_data = %{
      "code" => 1,
      "msg" => message
    }

    response = %{
      # MainProto.XC
      "mainId" => 5,
      # SC_LHD_BET_SYNC (续押失败) - 3927
      "subId" => 3927,
      "data" => error_data
    }

    Logger.info("📤 [SEND_FOLLOW_BET_ERROR] 发送续押错误 - 用户: #{numeric_id}, 消息: #{message}")
    send_to_player(state, player, response)
  end

  # ==================== 协议处理功能 ====================
  # 直接处理客户端协议，无需协议号转换

  # 使用统一配置的协议号 - 不再重复定义

  @doc """
  处理客户端下注请求 - 直接处理，无需协议转换
  """
  def handle_client_bet(%{"mainId" => 5, "subId" => 3904} = message, state, player) do
    Logger.info("🐉 [CLIENT_BET] 处理客户端下注请求: #{inspect(message["data"])}")

    # 解析客户端数据格式
    data = message["data"] || %{}
    # 客户端发送的方向 (1=龙, 2=虎, 3=和)
    direction = data["direction"]
    # 客户端发送的下注金额
    odds = data["odds"]

    # 转换方向为内部格式 - 使用统一配置
    bet_areas = LongHuGame.bet_areas()

    area =
      case direction do
        # 龙
        dir when dir == bet_areas.long -> :long
        # 虎
        dir when dir == bet_areas.hu -> :hu
        # 和
        dir when dir == bet_areas.he -> :he
        # 默认为龙
        _ -> :long
      end

    amount = odds

    # 验证下注参数
    cond do
      amount <= 0 ->
        # GAME_ERROR_NOT_MONEY_TO_BET
        send_error(state, player, 8)

      true ->
        # 直接调用内部下注处理
        handle_bet(state, player, area, amount)
    end
  end

  @doc """
  处理客户端取消申请上庄请求
  """
  def handle_client_cancel_apply_banker(
        %{"mainId" => 5, "subId" => 3907} = _message,
        state,
        player
      ) do
    numeric_id = player.numeric_id
    Logger.info("🐉 [CLIENT_CANCEL_BANKER] 处理客户端取消申请上庄请求 - 用户: #{numeric_id}")
    handle_cancel_apply_banker(state, player)
  end

  @doc """
  处理客户端申请下庄请求
  """
  def handle_client_apply_off_banker(%{"mainId" => 5, "subId" => 3919} = _message, state, player) do
    numeric_id = player.numeric_id
    Logger.info("🐉 [CLIENT_OFF_BANKER] 处理客户端申请下庄请求 - 用户: #{numeric_id}")
    handle_apply_off_banker(state, player)
  end

  @doc """
  处理客户端请求上庄列表请求
  """
  def handle_client_request_banker_list(
        %{"mainId" => 5, "subId" => 3924} = _message,
        state,
        player
      ) do
    numeric_id = player.numeric_id
    Logger.info("🐉 [CLIENT_BANKER_LIST] 处理客户端请求上庄列表请求 - 用户: #{numeric_id}")
    handle_request_banker_list(state, player)
  end

  @doc """
  处理客户端请求玩家列表请求
  """
  def handle_client_request_player_list(
        %{"mainId" => 5, "subId" => 3920} = message,
        state,
        player
      ) do
    numeric_id = player.numeric_id
    Logger.info("🐉 [CLIENT_PLAYER_LIST] 处理客户端请求玩家列表请求 - 用户: #{numeric_id}")

    data = message["data"] || %{}
    page = Map.get(data, "page", 0)

    handle_request_player_list(state, player, page)
  end

  @doc """
  处理客户端续押请求
  """
  def handle_client_follow_bet(%{"mainId" => 5, "subId" => 3917} = _message, state, player) do
    numeric_id = player.numeric_id
    Logger.info("🐉 [CLIENT_FOLLOW_BET] 处理客户端续押请求 - 用户: #{numeric_id}")
    handle_follow_bet(state, player)
  end

  # 发送历史记录给客户端
  defp send_history_to_user(state, player, page, count) do
    log_info("SEND_HISTORY", "发送历史记录", user: player.numeric_id, page: page, count: count)

    history = state.game_data.history
    total_count = length(history)

    {page_history, actual_count} = get_page_history(history, page, count, total_count)

    # 格式化为客户端期望的格式
    formatted_data = format_history_for_client_trend(page_history, total_count)

    message = LongHuMessageBuilder.build_history_trend(formatted_data)

    log_info("SEND_HISTORY", "历史记录已发送", user: player.numeric_id, sent_count: actual_count)
    send_to_player(state, player, message)
  end

  # 获取分页历史记录
  defp get_page_history(history, page, count, total_count) do
    start_index = page * count

    if start_index < total_count do
      page_history = history |> Enum.slice(start_index, count)
      {page_history, length(page_history)}
    else
      {[], 0}
    end
  end

  # 格式化历史记录为客户端走势图期望的格式
  defp format_history_for_client_trend(history, _total_count) do
    # 计算各种结果的统计
    {long_count, hu_count, he_count} = calculate_result_statistics(history)

    # 构建客户端期望的数据格式
    data = %{
      "longwinnum" => long_count,
      "huwinnum" => hu_count,
      "hewinnum" => he_count
    }

    # 添加历史记录数据，使用索引作为键
    history_with_index =
      history
      # 最新的在前
      |> Enum.reverse()
      |> Enum.with_index(1)
      |> Enum.reduce(data, fn {item, index}, acc ->
        result_value = format_result_for_client(item.result)

        Map.put(acc, to_string(index), %{
          "win" => result_value,
          "round" => item.round,
          "timestamp" => DateTime.to_unix(item.timestamp, :millisecond)
        })
      end)

    %{data: history_with_index}
  end

  # 计算结果统计
  defp calculate_result_statistics(history) do
    Enum.reduce(history, {0, 0, 0}, fn item, {long, hu, he} ->
      case item.result do
        :long -> {long + 1, hu, he}
        :hu -> {long, hu + 1, he}
        :he -> {long, hu, he + 1}
        _ -> {long, hu, he}
      end
    end)
  end

  # 格式化游戏结果为客户端格式 - 使用统一配置
  defp format_result_for_client(result) do
    bet_areas = LongHuGame.bet_areas()

    case result do
      # 龙赢
      :long -> bet_areas.long
      # 虎赢
      :hu -> bet_areas.hu
      # 和
      :he -> bet_areas.he
      # 未知
      _ -> 0
    end
  end

  # ==================== 重写 RoomBase 函数以支持损失返水 ====================

  @doc """
  获取玩家下注金额 - 重写以支持龙虎斗的下注数据结构
  """
  defp get_player_bet_amount(state, player_id) do
    # 龙虎斗将下注存储在 state.game_data.bets 中
    bets = Map.get(state.game_data, :bets, %{})
    player_bets = Map.get(bets, player_id, %{})

    # 计算玩家在所有区域的总下注
    Enum.reduce(player_bets, 0, fn {_area, amount}, acc ->
      acc + amount
    end)
  end

  # ==================== 赢家抽水机制 ====================
  @doc """
  获取游戏控制配置
  """
  defp get_game_control_config(game_id) do
    case Teen.Resources.Inventory.GameControlConfig.get_by_game_id_and_type(game_id, 3) do
      {:ok, config} ->
        config

      {:error, _} ->
        # 如果没有配置，返回默认配置
        %{winner_tax_rate: Decimal.new("0.05"), dark_tax_rate: -50}
    end
  end

  @doc """
  检查是否为真人玩家并应用明税
  """
  defp apply_winner_tax_if_real_player(state, numeric_id, gross_profit, game_control_config) do
    player = Map.get(state.players, numeric_id)

    # 只对真人玩家收税，机器人不收税
    if player && !player.is_robot && gross_profit > 0 do
      apply_winner_tax(state, player, gross_profit, game_control_config)
    else
      gross_profit
    end
  end

  @doc """
  应用明税机制

  税收流程：
  1. 计算明税：gross_profit * winner_tax_rate
  2. 计算暗税：明税 * (dark_tax_rate / 1000)
  3. 平台收入：明税 - 暗税
  4. 中心线调整：+暗税
  5. 玩家实得：gross_profit - 明税
  """
  defp apply_winner_tax(state, player, gross_profit, game_control_config) do
    # 获取配置参数
    winner_tax_rate = game_control_config.winner_tax_rate || Decimal.new("0.05")
    dark_tax_rate = game_control_config.dark_tax_rate || -50

    # 转换为Decimal确保精确计算
    gross_profit_decimal =
      if is_struct(gross_profit, Decimal), do: gross_profit, else: Decimal.new("#{gross_profit}")

    winner_tax_rate_decimal =
      if is_struct(winner_tax_rate, Decimal),
        do: winner_tax_rate,
        else: Decimal.new("#{winner_tax_rate}")

    # 1. 计算明税
    ming_tax = Decimal.mult(gross_profit_decimal, winner_tax_rate_decimal)

    # 2. 计算暗税（从明税中抽取）
    dark_tax_ratio = dark_tax_rate / 1000.0
    an_tax = Decimal.mult(ming_tax, Decimal.new("#{dark_tax_ratio}"))

    # 3. 计算平台实际收入（明税 - 暗税）
    platform_income = Decimal.sub(ming_tax, an_tax)

    # 4. 计算玩家实得（总赢利 - 明税）
    net_profit = Decimal.sub(gross_profit_decimal, ming_tax)

    log_info("WINNER_TAX", "明税计算完成",
      player_id: player.numeric_id,
      gross_profit: gross_profit,
      ming_tax: Decimal.to_float(ming_tax),
      an_tax: Decimal.to_float(an_tax),
      platform_income: Decimal.to_float(platform_income),
      net_profit: Decimal.to_float(net_profit),
      tax_rate: Decimal.to_float(winner_tax_rate_decimal),
      dark_tax_rate: dark_tax_rate
    )

    # 5. 执行税收转账
    execute_tax_transfer(state, player, platform_income, an_tax)

    # 6. 返回玩家实际应得金额（转换为整数避免Money.new!错误）
    Decimal.to_integer(net_profit)
  end

  @doc """
  执行税收转账
  """
  defp execute_tax_transfer(state, player, platform_income, an_tax) do
    try do
      # 平台收入转账（明税 - 暗税部分）
      if Decimal.compare(platform_income, Decimal.new("0")) == :gt do
        case Cypridina.Ledger.game_rake(
               state.game_id,
               player.user_id,
               Decimal.to_float(platform_income),
               reason: "龙虎斗明税收入",
               game_round: state.game_data.round
             ) do
          {:ok, _} ->
            log_info("TAX_TRANSFER", "平台税收转账成功",
              player_id: player.numeric_id,
              amount: Decimal.to_float(platform_income)
            )

          {:error, reason} ->
            log_error("TAX_TRANSFER", "平台税收转账失败",
              player_id: player.numeric_id,
              amount: Decimal.to_float(platform_income),
              reason: reason
            )
        end
      end

      # 暗税已在主结算通知中处理，无需重复调用
      if Decimal.compare(an_tax, Decimal.new("0")) != :eq do
        log_info("TAX_CALCULATION", "暗税计算完成",
          dark_tax_amount: Decimal.to_float(an_tax),
          note: "暗税已在主结算中传递到控制系统"
        )
      end
    rescue
      error ->
        log_error("TAX_TRANSFER", "税收转账过程出错",
          player_id: player.numeric_id,
          error: inspect(error)
        )
    end
  end

  # ==================== 控制决策集成 ====================

  @doc """
  收集并格式化下注数据用于控制决策

  ## 返回格式
  %{
    long: [{user_id, amount, is_robot}, ...],
    hu: [{user_id, amount, is_robot}, ...],
    he: [{user_id, amount, is_robot}, ...]
  }
  """
  defp collect_bet_data(state) do
    bets = Map.get(state.game_data, :bets, %{})
    players = state.players

    # 初始化各区域的下注数据
    bet_data = %{
      long: [],
      hu: [],
      he: []
    }

    # 遍历所有玩家的下注
    Enum.reduce(bets, bet_data, fn {user_id, user_bets}, acc ->
      # 获取玩家信息
      player = Map.get(players, user_id, %{})
      # 统一机器人判断逻辑：优先使用is_robot字段，没有则用numeric_id判断
      is_robot =
        if Map.has_key?(player, :is_robot) do
          player.is_robot
        else
          # 机器人的numeric_id为负数
          Map.get(player, :numeric_id, 0) < 0
        end

      # 处理每个区域的下注
      Enum.reduce(user_bets, acc, fn {area, amount}, area_acc ->
        # 只处理有效的下注区域和金额
        if area in [:long, :hu, :he] and amount > 0 do
          current_bets = Map.get(area_acc, area, [])
          new_bets = [{user_id, amount, is_robot} | current_bets]
          Map.put(area_acc, area, new_bets)
        else
          area_acc
        end
      end)
    end)
  end

  @doc """
  获取控制决策

  调用百人场控制计算器获取当前游戏的控制决策
  """
  defp get_control_decision(state) do
    # 龙虎游戏使用固定的游戏ID 22（不是房间号）
    game_id = 22

    # 收集下注数据
    bet_data = collect_bet_data(state)

    log_info("CONTROL_DECISION", "收集下注数据完成",
      game_id: state.id,
      bet_data: bet_data
    )

    case HundredPlayerControlCalculator.get_control_decision(game_id, :longhu, bet_data) do
      {:ok, %{control_mode: control_mode, target_result: target_result}} ->
        log_info("CONTROL_DECISION", "获取控制决策成功",
          game_id: state.id,
          control_mode: control_mode,
          target_result: target_result
        )

        {control_mode, target_result}

      {:error, reason} ->
        log_info("CONTROL_DECISION", "获取控制决策失败，使用随机模式",
          game_id: state.id,
          reason: inspect(reason)
        )

        {:random, :random}
    end
  end

  @doc """
  通知控制系统游戏结果

  游戏结算完成后，通知百人场控制系统更新库存状态
  """
  defp notify_control_system_result(state, result, settlements) do
    # 计算总的库存变化（平台盈亏）- 只计算真实玩家
    total_profit = calculate_total_platform_profit(state, settlements)

    # 计算总暗税（用于中心线调整）
    total_dark_tax = calculate_total_dark_tax_from_settlements(settlements)

    # 构建游戏数据
    game_data = %{
      round: state.game_data.round,
      cards: state.game_data.cards,
      result: result,
      settlements: settlements,
      total_platform_profit: total_profit,
      total_dark_tax: total_dark_tax,
      timestamp: DateTime.utc_now()
    }

    # 通知控制系统
    # 龙虎游戏使用固定的游戏ID 22（不是房间号）
    game_id = 22

    HundredPlayerControlCalculator.handle_game_result(
      game_id,
      :longhu,
      result,
      game_data
    )

    log_info("CONTROL_SYSTEM_NOTIFICATION", "控制系统结果通知成功",
      game_id: state.id,
      result: result,
      platform_profit: total_profit
    )
  end

  @doc """
  计算平台总盈利

  根据所有玩家的结算结果计算平台的总盈亏
  settlements 格式: %{numeric_id => settlement_amount}
  """
  defp calculate_total_platform_profit(state, settlements) do
    settlements
    |> Enum.filter(fn {player_id, _settlement_amount} ->
      # 只计算真实玩家的盈亏，排除机器人
      case Map.get(state.players, player_id) do
        # 玩家不存在，跳过
        nil ->
          false

        player ->
          # 优先使用is_robot字段，没有则用player_id判断
          if Map.has_key?(player, :is_robot) do
            # 只计算非机器人玩家
            !player.is_robot
          else
            # player_id为正数的是真实玩家
            player_id > 0
          end
      end
    end)
    |> Enum.reduce(Decimal.new("0"), fn {_player_id, settlement_amount}, acc ->
      # 平台盈利 = 玩家亏损（负的净收益）
      # settlement_amount 是玩家的净收益，负值表示平台盈利
      player_net_change =
        if is_struct(settlement_amount, Decimal),
          do: settlement_amount,
          else: Decimal.new("#{settlement_amount}")

      platform_profit = Decimal.negate(player_net_change)
      Decimal.add(acc, platform_profit)
    end)
  end

  @doc """
  计算所有真人玩家的总暗税（用于中心线调整）

  暗税只有真人玩家获胜时才会产生，机器人不产生暗税
  """
  defp calculate_total_dark_tax_from_settlements(settlements) do
    settlements
    |> Enum.reduce(Decimal.new("0"), fn {player_id, settlement_amount}, acc ->
      # 只处理真人玩家的获胜情况
      if is_integer(player_id) and player_id > 0 and settlement_amount > 0 do
        # 获取游戏控制配置
        # 龙虎游戏使用固定的游戏ID 22（不是房间号）
        game_control_config = get_game_control_config(22)

        # 计算暗税（基于获胜金额）
        settlement_decimal =
          if is_struct(settlement_amount, Decimal),
            do: settlement_amount,
            else: Decimal.new("#{settlement_amount}")

        # settlement_amount已经是扣除明税后的净收益，需要反推税前金额来计算暗税
        winner_tax_rate = game_control_config.winner_tax_rate || Decimal.new("0.05")

        # 反推税前金额：净收益 / (1 - 明税率) = 税前金额
        # 然后计算明税：税前金额 * 明税率
        one_minus_tax_rate = Decimal.sub(Decimal.new("1"), winner_tax_rate)
        gross_profit = Decimal.div(settlement_decimal, one_minus_tax_rate)
        ming_tax = Decimal.mult(gross_profit, winner_tax_rate)

        # 计算暗税（基于明税）
        dark_tax_rate = game_control_config.dark_tax_rate || -50
        dark_tax_ratio = dark_tax_rate / 1000.0
        an_tax = Decimal.mult(ming_tax, Decimal.new("#{dark_tax_ratio}"))

        # 汇率换算：将暗税从分转换为元（用于控制系统），与JhandiMunda保持一致
        an_tax_yuan = Decimal.div(an_tax, Decimal.new("100"))

        Decimal.add(acc, an_tax_yuan)
      else
        acc
      end
    end)
  end
end
