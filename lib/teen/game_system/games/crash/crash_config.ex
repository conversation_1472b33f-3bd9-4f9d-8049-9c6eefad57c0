defmodule Cypridina.Teen.GameSystem.Games.Crash.CrashConfig do
  @moduledoc """
  Crash游戏配置管理模块

  管理游戏的各种配置参数，包括：
  - 时间配置
  - 倍数配置
  - 筹码配置
  - 控制参数
  """

  @doc """
  获取默认配置
  """
  def get_default_config do
    %{
      # 时间配置（秒）
      # 等待时间
      free_time: 0,
      # 下注时间
      betting_time: 15,
      # 结算时间
      settling_time: 3,
      # 最大飞行时间（毫秒）
      max_fly_time: 110_460,

      # 筹码配置（分）
      chips: [1000, 5000, 10000, 100_000, 500_000],
      # 最小下注（分）
      min_bet: 1000,
      # 下注必须是此数的倍数
      bet_multiple: 100,

      # 玩家配置
      # 最大玩家数
      max_players: 100,
      # 最小玩家数
      min_players: 1,

      # 税率配置
      # 税率 5% (50/1000)
      revenue_ratio: 50,

      # 机器人配置
      # 最大机器人数
      max_robots: 60,
      # 机器人下注概率
      robot_bet_probability: 0.6,
      # 目标机器人数量范围
      min_target_robots: 20,
      max_target_robots: 60,
      # 没有真实玩家时保留的机器人数量
      idle_robots: 1,

      # 机器人工作时间管理配置
      robot_work_schedules: %{
        # 短时间工作模式：上班5-15分钟，休息100-200分钟
        short: %{
          work_time_range: {1, 2},      # 分钟
          rest_time_range: {100, 200},   # 分钟
          probability: 0.6               # 40%概率选择此模式
        },
        # 中时间工作模式：上班30-60分钟，休息200-300分钟
        medium: %{
          work_time_range: {30, 60},     # 分钟
          rest_time_range: {200, 300},   # 分钟
          probability: 0.3               # 40%概率选择此模式
        },
        # 长时间工作模式：上班60-100分钟，休息200-300分钟
        long: %{
          work_time_range: {60, 100},    # 分钟
          rest_time_range: {200, 300},   # 分钟
          probability: 0.1               # 20%概率选择此模式
        }
      },

      # 机器人余额管理配置
      robot_balance_config: %{
        # 初始余额范围（分）
        initial_balance_range: {50_000, 1_000_000},
        # 余额更新频率（每局游戏后更新）
        update_frequency: :per_game,
        # 最小余额阈值（低于此值机器人会"下班"）
        min_balance_threshold: 10_000,
        # 余额恢复策略
        balance_recovery: %{
          # 是否启用余额恢复
          enabled: true,
          # 恢复到的目标余额范围
          target_range: {100_000, 500_000},
          # 恢复概率（机器人"休息"时）
          recovery_probability: 0.9
        }
      },

      # 倍数配置 - 基于C++配置文件格式
      multiplier_configs: generate_multiplier_configs(),

      # 库存控制配置
      stock_control: %{
        # 初始库存分（分）
        initial_stock: 10_000_000,
        # 安全线（分）- 库存控制的基准线
        safety_line: 10_000_000,
        # 收分阈值（相对于安全线的百分比）
        collect_threshold: 0.5,  # 50% - 低于安全线的50%时收分
        # 放分阈值（相对于安全线的百分比）
        release_threshold: 1.5,  # 150% - 高于安全线的150%时放分
        # 库存更新频率
        update_frequency: :per_game
      },

      # 倍率分布配置 - 简化为两种情况
      multiplier_distribution: %{
        # 无真实玩家时的分布 (总权重1000)
        no_real_players: [
          # {概率权重, 最小倍数, 最大倍数, 描述}
          {1, 100, 100, "1.00x (立即爆炸)"},
          {30, 101, 110, "1.01x-1.10x"},
          {30, 111, 120, "1.11x-1.20x"},
          {50, 121, 130, "1.21x-1.30x"},
          {80, 131, 140, "1.31x-1.40x"},
          {80, 141, 150, "1.41x-1.50x"},
          {80, 151, 200, "1.51x-2.00x"},
          {100, 201, 300, "2.01x-3.00x"},
          {100, 301, 400, "3.01x-4.00x"},
          {100, 401, 500, "4.01x-5.00x"},
          {100, 501, 1000, "5.01x-10.00x"},
          {80, 1001, 2000, "10.01x-20.00x"},
          {50, 2001, 5000, "20.01x-50.00x"},
          {3, 5001, 10000, "50.01x-100.00x"},
          {1, 10001, 20000, "100.01x-200.00x"},
          {1, 20001, 30000, "200.01x-300.00x"}
        ],

        # 有真实玩家时的基础分布 (总权重1000) - 会根据实时盈亏动态调整
        has_real_players: [
          {10, 100, 100, "1.00x (立即爆炸)"},
          {20, 101, 110, "1.01x-1.10x"},
          {100, 111, 120, "1.11x-1.20x"},
          {100, 121, 130, "1.21x-1.30x"},
          {100, 131, 140, "1.31x-1.40x"},
          {100, 141, 150, "1.41x-1.50x"},
          {100, 151, 200, "1.51x-2.00x"},
          {100, 201, 300, "2.01x-3.00x"},
          {100, 301, 400, "3.01x-4.00x"},
          {100, 401, 500, "4.01x-5.00x"},
          {60, 501, 1000, "5.01x-10.00x"},
          {40, 1001, 2000, "10.01x-20.00x"},
          {30, 2001, 3000, "20.01x-30.00x"},
          {20, 3001, 5000, "30.01x-50.00x"},
          {8, 5001, 10000, "50.01x-100.00x"},
          {4, 10001, 20000, "100.01x-200.00x"},
          {3, 20001, 30000, "200.01x-300.00x"}
        ]
      },

      # 实时赔付率控制配置
      realtime_payout_control: %{
        # 高于安全线时的赔付率阈值 - 超过此比例尽快爆炸
        high_stock_payout_threshold: 1.10,  # 110%
        # 低于安全线时的赔付率阈值 - 达到此比例尽快爆炸
        low_stock_payout_threshold: 1.00,   # 100%
        # 动态调整强度 (0.0-1.0)
        adjustment_intensity: 0.3
      }
    }
  end

  @doc """
  获取指定房间的配置
  """
  def get_room_config(_room_id) do
    # TODO: 根据房间ID获取特定配置
    # 目前返回默认配置
    get_default_config()
  end

  @doc """
  获取游戏配置参数
  """
  def get_game_params(config) do
    %{
      free_time: Map.get(config, :free_time, 5),
      betting_time: Map.get(config, :betting_time, 15),
      settling_time: Map.get(config, :settling_time, 5),
      max_fly_time: Map.get(config, :max_fly_time, 110_460),
      min_bet: Map.get(config, :min_bet, 500),
      bet_multiple: Map.get(config, :bet_multiple, 100),
      revenue_ratio: Map.get(config, :revenue_ratio, 50),
      max_players: Map.get(config, :max_players, 100),
      min_players: Map.get(config, :min_players, 1),
      max_robots: Map.get(config, :max_robots, 60),
      robot_bet_probability: Map.get(config, :robot_bet_probability, 0.3),
      min_target_robots: Map.get(config, :min_target_robots, 10),
      max_target_robots: Map.get(config, :max_target_robots, 20),
      idle_robots: Map.get(config, :idle_robots, 0)
    }
  end

  @doc """
  获取合并后的配置
  """
  def get_merged_config(custom_config \\ %{}) do
    Map.merge(get_default_config(), custom_config)
  end

  @doc """
  生成倍数配置
  基于C++代码中的配置文件格式
  """
  def generate_multiplier_configs do
    # 基础配置：前100个点位，每20ms增加1个点
    base_configs =
      1..100
      |> Enum.map(fn id ->
        %{
          id: id,
          # 从1.00x开始
          multiplier: 100 + id - 1,
          # 每20ms一个点
          time: (id - 1) * 20
        }
      end)

    # 扩展配置：更高倍数的配置
    extended_configs = [
      # 1.10x
      %{id: 101, multiplier: 110, time: 2100},
      # 1.20x
      %{id: 102, multiplier: 120, time: 2400},
      # 1.30x
      %{id: 103, multiplier: 130, time: 2800},
      # 1.40x
      %{id: 104, multiplier: 140, time: 3200},
      # 1.50x
      %{id: 105, multiplier: 150, time: 3600},
      # 1.60x
      %{id: 106, multiplier: 160, time: 4000},
      # 1.70x
      %{id: 107, multiplier: 170, time: 4500},
      # 1.80x
      %{id: 108, multiplier: 180, time: 5000},
      # 1.90x
      %{id: 109, multiplier: 190, time: 5500},
      # 2.00x
      %{id: 110, multiplier: 200, time: 6000},
      # 2.20x
      %{id: 111, multiplier: 220, time: 7000},
      # 2.50x
      %{id: 112, multiplier: 250, time: 8000},
      # 3.00x
      %{id: 113, multiplier: 300, time: 10000},
      # 3.50x
      %{id: 114, multiplier: 350, time: 12000},
      # 4.00x
      %{id: 115, multiplier: 400, time: 15000},
      # 5.00x
      %{id: 116, multiplier: 500, time: 20000},
      # 6.00x
      %{id: 117, multiplier: 600, time: 25000},
      # 7.00x
      %{id: 118, multiplier: 700, time: 30000},
      # 8.00x
      %{id: 119, multiplier: 800, time: 35000},
      # 9.00x
      %{id: 120, multiplier: 900, time: 40000},
      # 10.00x
      %{id: 121, multiplier: 1000, time: 45000},
      # 12.00x
      %{id: 122, multiplier: 1200, time: 50000},
      # 15.00x
      %{id: 123, multiplier: 1500, time: 55000},
      # 20.00x
      %{id: 124, multiplier: 2000, time: 60000},
      # 25.00x
      %{id: 125, multiplier: 2500, time: 65000},
      # 30.00x
      %{id: 126, multiplier: 3000, time: 70000},
      # 40.00x
      %{id: 127, multiplier: 4000, time: 75000},
      # 50.00x
      %{id: 128, multiplier: 5000, time: 80000},
      # 75.00x
      %{id: 129, multiplier: 7500, time: 90000},
      # 100.00x
      %{id: 130, multiplier: 10000, time: 100_000},
      # 150.00x
      %{id: 131, multiplier: 15000, time: 105_000},
      # 200.00x
      %{id: 132, multiplier: 20000, time: 108_000},
      # 300.00x (接近最大时间)
      %{id: 133, multiplier: 30000, time: 110_000}
    ]

    base_configs ++ extended_configs
  end

  @doc """
  获取筹码配置
  """
  def get_chip_config(config \\ nil) do
    get_config_value(config, :chips, [1000, 5000, 10000, 100_000, 500_000])
  end

  @doc """
  获取时间配置
  """
  def get_time_config(config \\ nil) do
    %{
      free_time: get_config_value(config, :free_time, 5),
      betting_time: get_config_value(config, :betting_time, 15),
      settling_time: get_config_value(config, :settling_time, 5),
      max_fly_time: get_config_value(config, :max_fly_time, 110_460)
    }
  end

  @doc """
  获取下注配置
  """
  def get_bet_config(config \\ nil) do
    %{
      min_bet: get_config_value(config, :min_bet, 500),
      bet_multiple: get_config_value(config, :bet_multiple, 100),
      revenue_ratio: get_config_value(config, :revenue_ratio, 50)
    }
  end

  @doc """
  获取机器人配置
  """
  def get_robot_config(config \\ nil) do
    %{
      max_robots: get_config_value(config, :max_robots, 60),
      robot_bet_probability: get_config_value(config, :robot_bet_probability, 0.3),
      min_target_robots: get_config_value(config, :min_target_robots, 10),
      max_target_robots: get_config_value(config, :max_target_robots, 20),
      idle_robots: get_config_value(config, :idle_robots, 0)
    }
  end

  @doc """
  获取库存控制配置
  """
  def get_stock_control_config(config \\ nil) do
    stock_config = get_config_value(config, :stock_control, %{})

    %{
      initial_stock: Map.get(stock_config, :initial_stock, 1_000_000),
      safety_line: Map.get(stock_config, :safety_line, 500_000),
      collect_threshold: Map.get(stock_config, :collect_threshold, 0.5),
      release_threshold: Map.get(stock_config, :release_threshold, 1.5),
      update_frequency: Map.get(stock_config, :update_frequency, :per_game)
    }
  end

  @doc """
  获取倍率分布配置 - 简化函数名保持一致性
  """
  def get_distribution_config(config \\ nil) do
    get_config_value(config, :multiplier_distribution, %{})
  end

  # 保持向后兼容的别名
  def get_multiplier_distribution_config(config \\ nil) do
    get_distribution_config(config)
  end

  @doc """
  根据玩家状态获取对应的倍率分布
  """
  def get_distribution_by_player_status(has_real_players, config \\ nil) do
    distribution_config = get_distribution_config(config)

    if has_real_players do
      Map.get(distribution_config, :has_real_players, [])
    else
      Map.get(distribution_config, :no_real_players, [])
    end
  end

  @doc """
  获取实时赔付控制配置 - 统一命名模式
  """
  def get_payout_control_config(config \\ nil) do
    get_config_value(config, :realtime_payout_control, %{})
  end

  # 保持向后兼容的别名
  def get_realtime_payout_control_config(config \\ nil) do
    get_payout_control_config(config)
  end

  @doc """
  根据分布配置生成随机倍率
  """
  def generate_multiplier_from_distribution(distribution) do
    if Enum.empty?(distribution) do
      # 如果没有配置，返回默认倍率
      150  # 1.50x
    else
      # 计算总权重
      total_weight = Enum.reduce(distribution, 0, fn {weight, _, _, _}, acc -> acc + weight end)

      # 生成随机数
      rand = :rand.uniform(total_weight)

      # 找到对应的区间
      {_weight, min_mult, max_mult, _desc} = find_multiplier_range(distribution, rand, 0)

      # 在区间内生成随机倍率
      if min_mult == max_mult do
        min_mult
      else
        min_mult + :rand.uniform(max_mult - min_mult)
      end
    end
  end

  @doc """
  根据实时赔付率状况动态调整倍率分布
  注意：实际的赔付状况计算应该在 crash_logic.ex 中进行
  """
  def adjust_distribution_by_payout_ratio(base_distribution, payout_status, control_config) do
    cond do
      # 需要立即爆炸保护
      payout_status.need_immediate_crash ->
        [{1000, 100, 100, "立即爆炸保护 - #{payout_status.reason}"}]

      # 需要提高低倍数概率
      payout_status.need_adjustment ->
        adjust_distribution_for_risk(base_distribution, payout_status.risk_level, control_config)

      # 正常状况，使用基础分布
      true ->
        base_distribution
    end
  end



  @doc """
  根据风险等级调整分布
  """
  defp adjust_distribution_for_risk(base_distribution, risk_level, control_config) do
    adjustment_intensity = control_config.adjustment_intensity

    # 根据风险等级确定调整强度
    adjustment_factor = case risk_level do
      :critical -> adjustment_intensity * 0.8  # 80%强度
      :high -> adjustment_intensity * 0.6      # 60%强度
      :medium -> adjustment_intensity * 0.4    # 40%强度
      :low -> adjustment_intensity * 0.2       # 20%强度
      _ -> 0.0
    end

    # 提高低倍数权重，降低高倍数权重
    Enum.map(base_distribution, fn {weight, min_mult, max_mult, desc} ->
      adjusted_weight = if max_mult <= 150 do
        # 低倍数：增加权重
        round(weight * (1 + adjustment_factor))
      else
        # 高倍数：减少权重
        round(weight * (1 - adjustment_factor))
      end

      {max(adjusted_weight, 1), min_mult, max_mult, desc}  # 确保权重至少为1
    end)
  end

  # 私有函数：根据随机数找到对应的倍率区间
  defp find_multiplier_range([{weight, min_mult, max_mult, desc} | _rest], rand, acc)
       when rand <= acc + weight do
    {weight, min_mult, max_mult, desc}
  end

  defp find_multiplier_range([{weight, _, _, _} | rest], rand, acc) do
    find_multiplier_range(rest, rand, acc + weight)
  end

  defp find_multiplier_range([], _rand, _acc) do
    # 如果没有找到，返回默认值
    {100, 150, 150, "默认1.50x"}
  end

  @doc """
  验证配置有效性
  """
  def validate_config(config) do
    errors = []

    # 验证时间配置
    errors =
      if Map.get(config, :free_time, 0) < 1 do
        ["free_time must be at least 1 second" | errors]
      else
        errors
      end

    errors =
      if Map.get(config, :betting_time, 0) < 5 do
        ["betting_time must be at least 5 seconds" | errors]
      else
        errors
      end

    # 验证下注配置
    errors =
      if Map.get(config, :min_bet, 0) < 100 do
        ["min_bet must be at least 100" | errors]
      else
        errors
      end

    errors =
      if Map.get(config, :bet_multiple, 0) < 1 do
        ["bet_multiple must be at least 1" | errors]
      else
        errors
      end

    # 验证玩家配置
    errors =
      if Map.get(config, :max_players, 0) < 1 do
        ["max_players must be at least 1" | errors]
      else
        errors
      end

    errors =
      if Map.get(config, :min_players, 0) < 1 do
        ["min_players must be at least 1" | errors]
      else
        errors
      end

    case errors do
      [] -> {:ok, config}
      _ -> {:error, errors}
    end
  end

  @doc """
  根据时间获取倍数点（对应C++的GetRewardPoint）
  """
  def get_multiplier_point_by_time(time_ms, config \\ nil) do
    multiplier_configs = get_config_value(config, :multiplier_configs, [])

    # 找到对应时间的倍数点（每20毫秒一个点）
    point_index =
      Enum.find_index(multiplier_configs, fn point ->
        time_ms <= point.time
      end)

    point_index || max(0, length(multiplier_configs) - 1)
  end

  @doc """
  根据倍数点获取倍数（对应C++的GetMultiByRewardPoint）
  """
  def get_multiplier_by_point(point, config \\ nil) do
    multiplier_configs = get_config_value(config, :multiplier_configs, [])

    case Enum.at(multiplier_configs, point) do
      # 默认1.00倍
      nil -> 100
      multiplier_point -> multiplier_point.multiplier
    end
  end

  @doc """
  根据倍数获取时间（对应C++中倍数到时间的转换）
  """
  def get_time_by_multiplier(multiplier, config \\ nil) do
    multiplier_configs = get_config_value(config, :multiplier_configs, [])

    # 找到对应倍数的时间
    case Enum.find(multiplier_configs, fn point ->
           point.multiplier >= multiplier
         end) do
      nil -> get_config_value(config, :max_fly_time, 110_460)
      point -> point.time
    end
  end

  @doc """
  根据倍数获取对应的点索引（对应C++中的倍数查找逻辑）
  """
  def get_point_by_multiplier(multiplier, config \\ nil) do
    multiplier_configs = get_config_value(config, :multiplier_configs, [])

    # 找到对应倍数的点索引
    point_index =
      Enum.find_index(multiplier_configs, fn point ->
        point.multiplier >= multiplier
      end)

    point_index || max(0, length(multiplier_configs) - 1)
  end

  @doc """
  验证下注金额是否有效
  """
  def valid_bet_amount?(bet_amount, config \\ nil) do
    min_bet = get_config_value(config, :min_bet, 500)
    bet_multiple = get_config_value(config, :bet_multiple, 100)

    bet_amount >= min_bet and rem(bet_amount, bet_multiple) == 0
  end

  @doc """
  从配置文件加载配置（基于C++的ReadConfigInformation）
  """
  def load_from_file(file_path) do
    if File.exists?(file_path) do
      try do
        content = File.read!(file_path)
        parse_config_file(content)
      rescue
        error ->
          {:error, "Failed to load config file: #{inspect(error)}"}
      end
    else
      {:error, "Config file not found: #{file_path}"}
    end
  end

  @doc """
  保存配置到文件
  """
  def save_to_file(config, file_path) do
    try do
      content = format_config_file(config)
      File.write!(file_path, content)
      {:ok, file_path}
    rescue
      error ->
        {:error, "Failed to save config file: #{inspect(error)}"}
    end
  end

  @doc """
  获取环境变量配置
  """
  def get_env_config do
    %{
      free_time: get_env_int("CRASH_FREE_TIME", 5),
      betting_time: get_env_int("CRASH_BETTING_TIME", 15),
      settling_time: get_env_int("CRASH_SETTLING_TIME", 5),
      min_bet: get_env_int("CRASH_MIN_BET", 500),
      max_players: get_env_int("CRASH_MAX_PLAYERS", 100),
      revenue_ratio: get_env_int("CRASH_REVENUE_RATIO", 50)
    }
  end

  # ==================== 私有函数 ====================

  # 统一的配置获取辅助函数 - DRY原则
  defp ensure_config(config), do: config || get_default_config()

  # 统一的配置值获取函数 - 避免重复的Map.get模式
  defp get_config_value(config, key, default) do
    Map.get(ensure_config(config), key, default)
  end

  # 解析配置文件内容（基于C++的INI文件格式）
  defp parse_config_file(content) do
    try do
      config = get_default_config()

      # 解析INI格式的配置文件
      parsed_config =
        content
        |> String.split("\n")
        |> Enum.reduce(config, &parse_config_line/2)

      {:ok, parsed_config}
    rescue
      error ->
        {:error, "Failed to parse config: #{inspect(error)}"}
    end
  end

  # 解析单行配置
  defp parse_config_line(line, config) do
    line = String.trim(line)

    # 跳过空行和注释行
    if line == "" or String.starts_with?(line, "#") or String.starts_with?(line, ";") do
      config
    else
      case String.split(line, "=", parts: 2) do
        [key, value] ->
          key = String.trim(key)
          value = String.trim(value)
          parse_config_value(config, key, value)

        _ ->
          config
      end
    end
  end

  # 解析配置值
  defp parse_config_value(config, key, value) do
    case key do
      "free_time" -> Map.put(config, :free_time, parse_int(value, 5))
      "betting_time" -> Map.put(config, :betting_time, parse_int(value, 15))
      "settling_time" -> Map.put(config, :settling_time, parse_int(value, 5))
      "min_bet" -> Map.put(config, :min_bet, parse_int(value, 500))
      "max_players" -> Map.put(config, :max_players, parse_int(value, 100))
      "max_robots" -> Map.put(config, :max_robots, parse_int(value, 60))
      "revenue_ratio" -> Map.put(config, :revenue_ratio, parse_int(value, 50))
      _ -> config
    end
  end

  # 格式化配置文件内容
  defp format_config_file(config) do
    """
    # Crash游戏配置文件
    # 基于C++项目的配置格式

    [Config]
    free_time=#{Map.get(config, :free_time, 5)}
    betting_time=#{Map.get(config, :betting_time, 15)}
    settling_time=#{Map.get(config, :settling_time, 5)}
    min_bet=#{Map.get(config, :min_bet, 500)}
    max_players=#{Map.get(config, :max_players, 100)}
    max_robots=#{Map.get(config, :max_robots, 60)}
    revenue_ratio=#{Map.get(config, :revenue_ratio, 50)}

    [Chips]
    chip0=500
    chip1=1000
    chip2=5000
    chip3=10000
    chip4=50000
    chip5=500000

    [DrawPoint]
    # 格式：ID=倍数,时间(单位：20毫秒)
    """ <> format_multiplier_configs(Map.get(config, :multiplier_configs, []))
  end

  # 格式化倍数配置
  defp format_multiplier_configs(multiplier_configs) do
    multiplier_configs
    |> Enum.map(fn %{id: id, multiplier: multiplier, time: time} ->
      # 转换为20毫秒单位
      time_units = div(time, 20)
      "#{id}=#{multiplier},#{time_units}"
    end)
    |> Enum.join("\n")
  end

  # 解析整数值
  defp parse_int(value, default) do
    case Integer.parse(value) do
      {int_value, _} -> int_value
      :error -> default
    end
  end

  defp get_env_int(key, default) do
    case System.get_env(key) do
      nil ->
        default

      value ->
        case Integer.parse(value) do
          {int_value, _} -> int_value
          :error -> default
        end
    end
  end
end
