defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Crash.CrashLogic do
  @moduledoc """
  Crash游戏逻辑模块

  处理游戏的核心逻辑，包括：
  - 爆炸点计算
  - 倍数计算
  - 收益计算
  - 游戏状态管理
  """

  alias Cypridina.Teen.GameSystem.Games.Crash.CrashConfig

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Crash.{CrashGame}
  require Logger

  @doc """
  计算爆炸倍数和时间

  基于C++代码的CalculateStayTime算法，考虑以下因素：
  - 是否有真实玩家下注
  - 库存控制状态
  - 玩家控制值
  - 充值提现比例
  - 玩家历史行为模式（新增）
  - 实时盈亏状况（新增）

  返回: {multiplier, crash_time_ms}
  """
  def calculate_crash_point(state, betting_players \\ []) do
    config = state.game_data.config

    # 过滤出真实玩家（机器人不影响爆炸点计算）
    real_betting_players = Enum.filter(betting_players, fn player ->
      not Map.get(player, :is_robot, false)
    end)

    Logger.info("🧠 [CRASH_LOGIC] 下注玩家统计: 总计#{length(betting_players)}人，真实玩家#{length(real_betting_players)}人")

    # 计算爆炸倍数（只基于真实玩家）
    multiplier =
      if Enum.empty?(real_betting_players) do
        # 没有真实玩家下注时的随机算法
        calculate_no_bet_multiplier()
      else
        # 有真实玩家下注时的智能算法（集成实时盈亏计算）
        calculate_intelligent_multiplier(state, real_betting_players)
      end

    # 根据倍数查找对应的时间
    crash_time = get_crash_time_by_multiplier(multiplier, config)

    Logger.info("🎲 [CRASH_LOGIC] 计算爆炸点 - 倍数: #{multiplier / 100}x, 时间: #{crash_time}ms")

    {multiplier, crash_time}
  end

  @doc """
  根据时间获取当前倍数
  """
  def get_current_multiplier(elapsed_time_ms, _config) do
    # 基于C++的倍数计算公式：倍数 = 100 + elapsed_time_ms / 20
    # 每20毫秒增加1个点（0.01倍）
    multiplier = 100 + div(elapsed_time_ms, 20)
    # 最小倍数为1.00x
    max(multiplier, 100)
  end

  @doc """
  根据倍数查找对应的爆炸时间
  """
  def get_crash_time_by_multiplier(multiplier, config) do
    # 查找配置表中对应的时间
    multiplier_configs = Map.get(config, :multiplier_configs, [])
    max_fly_time = Map.get(config, :max_fly_time, 110_460)

    # 找到第一个大于等于目标倍数的配置项
    found_config =
      Enum.find(multiplier_configs, fn config_item ->
        config_item.multiplier >= multiplier
      end)

    crash_time =
      case found_config do
        nil ->
          # 如果没找到，使用最大飞行时间
          max_fly_time

        config_item ->
          config_item.time
      end

    # 确保爆炸时间不超过最大飞行时间
    min(crash_time, max_fly_time)
  end

  @doc """
  计算玩家下车收益
  """
  def calculate_cash_out_payout(bet_amount, cash_out_multiplier, config) do
    revenue_ratio = Map.get(config, :revenue_ratio, 50)
    CrashGame.calculate_payout(bet_amount, cash_out_multiplier, revenue_ratio)
  end

  @doc """
  验证玩家是否可以下注
  """
  def can_place_bet?(state, player, bet_amount) do
    cond do
      state.game_data.phase != :betting ->
        {:error, CrashGame.error_messages().not_betting_phase}

      not CrashGame.valid_bet_amount?(bet_amount, state.game_data.config) ->
        {:error, CrashGame.error_messages().invalid_bet_amount}

      # 检查玩家余额
      not has_sufficient_balance?(state, player, bet_amount) ->
        {:error, CrashGame.error_messages().insufficient_balance}

      true ->
        :ok
    end
  end

  @doc """
  检查玩家是否有足够余额
  """
  defp has_sufficient_balance?(state, player, bet_amount) do
    current_balance = get_player_current_balance(state, player)
    current_balance >= bet_amount
  end

  @doc """
  获取玩家当前余额
  """
  defp get_player_current_balance(state, player) do
    if Map.get(player, :is_robot, false) do
      # 机器人使用本地存储的余额
      Map.get(player, :balance, 1_000_000)
    else
      # 真实玩家从账户系统获取实时余额
      try do
        Cypridina.Accounts.get_user_points(player.user_id)
      rescue
        _ -> 0
      end
    end
  end

  @doc """
  验证玩家是否可以下车
  """
  def can_cash_out?(state, player) do
    cond do
      state.game_data.phase != :flying ->
        {:error, CrashGame.error_messages().not_flying_phase}

      Map.get(player, :bet_amount, 0) <= 0 ->
        {:error, CrashGame.error_messages().no_bet_placed}

      Map.get(player, :cashed_out, false) ->
        {:error, CrashGame.error_messages().already_cashed_out}

      true ->
        :ok
    end
  end

  @doc """
  处理玩家下注
  """
  def place_bet(state, player, bet_amount, auto_cash_out \\ nil) do
    case can_place_bet?(state, player, bet_amount) do
      :ok ->
        # 验证自动下车倍率的有效性
        validated_auto_cash_out = validate_auto_cash_out(auto_cash_out)

        # 计算玩家本轮累计下注金额
        current_bet_amount = Map.get(player, :bet_amount, 0)
        total_bet_amount = current_bet_amount + bet_amount

        updated_player =
          Map.merge(player, %{
            # 累计下注金额
            bet_amount: total_bet_amount,
            bet_time: System.system_time(:millisecond),
            cashed_out: false,
            cash_out_multiplier: nil,
            # 自动下车倍率（以最后一次下注的设置为准）
            auto_cash_out: validated_auto_cash_out
          })

        # 更新游戏数据
        updated_game_data =
          state.game_data
          |> Map.update(:total_bets, bet_amount, &(&1 + bet_amount))
          |> Map.update(:betting_players, [player.numeric_id], fn players ->
            [player.numeric_id | players] |> Enum.uniq()
          end)

        updated_state = %{
          state
          | players: Map.put(state.players, player.numeric_id, updated_player),
            game_data: updated_game_data
        }

        {:ok, updated_state, updated_player}

      error ->
        error
    end
  end

  @doc """
  验证自动下车倍率的有效性
  """
  def validate_auto_cash_out(auto_cash_out) do
    case auto_cash_out do
      nil -> nil
      value when is_integer(value) and value >= 100 -> value
      value when is_number(value) and value >= 100 -> round(value)
      _ -> nil  # 无效值，不启用自动下车
    end
  end

  @doc """
  检查玩家是否应该自动下车
  """
  def should_auto_cash_out?(player, current_multiplier) do
    with auto_cash_out when not is_nil(auto_cash_out) <- Map.get(player, :auto_cash_out),
         false <- Map.get(player, :cashed_out, false),
         true <- current_multiplier >= auto_cash_out do
      true
    else
      _ -> false
    end
  end

  @doc """
  获取所有应该自动下车的玩家
  """
  def get_auto_cash_out_players(state, current_multiplier) do
    state.players
    |> Map.values()
    |> Enum.filter(fn player ->
      # 只检查有下注且未下车的真实玩家
      Map.get(player, :bet_amount, 0) > 0 and
      not Map.get(player, :cashed_out, false) and
      not Map.get(player, :is_robot, false) and
      should_auto_cash_out?(player, current_multiplier)
    end)
  end

  @doc """
  处理玩家下车
  """
  def cash_out(state, player, current_time_ms) do
    case can_cash_out?(state, player) do
      :ok ->
        config = state.game_data.config
        current_multiplier = get_current_multiplier(current_time_ms, config)
        payout_info = calculate_cash_out_payout(player.bet_amount, current_multiplier, config)

        updated_player =
          Map.merge(player, %{
            cashed_out: true,
            cash_out_time: current_time_ms,
            cash_out_multiplier: current_multiplier,
            payout: payout_info.gross_payout,
            profit: payout_info.profit,
            revenue: payout_info.revenue
          })

        updated_state = %{
          state
          | players: Map.put(state.players, player.numeric_id, updated_player)
        }

        {:ok, updated_state, updated_player, payout_info}

      error ->
        error
    end
  end

  @doc """
  检查游戏是否应该爆炸
  参数 elapsed_time_ms 是从飞行开始到现在经过的时间（毫秒）

  检查逻辑：
  1. 预设爆炸时间检查
  2. 最大飞行时间检查
  3. 实时赔付率风险检查（每次game_tick都会执行）
  """
  def should_crash?(state, elapsed_time_ms) do
    crash_time = Map.get(state.game_data, :crash_time, 0)
    max_fly_time = Map.get(state.game_data.config, :max_fly_time, 110_460)

    # 1. 检查是否达到预设的爆炸时间或最大飞行时间
    preset_crash = elapsed_time_ms >= crash_time or elapsed_time_ms >= max_fly_time

    # 2. 实时赔付率风险检查
    realtime_risk_crash = should_crash_for_realtime_risk?(state, elapsed_time_ms)

    # 任一条件满足都应该爆炸
    result = preset_crash or realtime_risk_crash

    if realtime_risk_crash do
      Logger.warn("🚨 [REALTIME_RISK] 实时风险触发爆炸 - 时间: #{elapsed_time_ms}ms, 倍率: #{get_current_multiplier(elapsed_time_ms, state.game_data.config) / 100}x")
    end

    result
  end

  @doc """
  实时赔付率风险检查 - 在每次game_tick时执行
  只有在库存不安全时才进行风控，库存安全时不干预
  """
  defp should_crash_for_realtime_risk?(state, elapsed_time_ms) do
    # 计算当前实时盈亏状况
    profit_status = calculate_realtime_profit(state)

    # 如果没有真实玩家下注，无需风险控制
    if profit_status.total_bet == 0 or profit_status.remaining_players == 0 do
      false
    else
      # 获取库存控制配置
      stock_config = CrashConfig.get_stock_control_config()
      current_stock = Map.get(state, :current_stock, stock_config.initial_stock)
      is_above_safety_line = current_stock >= stock_config.safety_line

      # 🎯 关键修复：库存安全时不进行风控！
      if is_above_safety_line do
        # 库存高于安全线，不进行风控，让游戏正常进行
        Logger.debug("💰 [STOCK_SAFE] 库存#{current_stock}高于安全线#{stock_config.safety_line}，不进行风控")
        false
      else
        # 库存低于安全线，才进行风控检查
        Logger.info("⚠️ [STOCK_RISK] 库存#{current_stock}低于安全线#{stock_config.safety_line}，进行风控检查")

        # 🎯 基于单局盈亏的智能风控分析
        {has_profit_surplus, amount, analysis_msg} = analyze_recent_profit_trend_with_current_game(state, elapsed_time_ms)

        current_multiplier = get_current_multiplier(elapsed_time_ms, state.game_data.config)

        if has_profit_surplus do
          # 有盈余，可以适当"吐出来"给玩家
          Logger.info("💰 [SMART_CONTROL] #{analysis_msg}，允许适当放宽控制")
          false
        else
          # 没有盈余或会亏损，需要严格控制
          Logger.warn("🚨 [SMART_CONTROL] #{analysis_msg}，需要严格控制 - 当前倍率: #{current_multiplier / 100}x")

          control_config = CrashConfig.get_payout_control_config()
          # 计算当前赔付状况
          payout_status = calculate_payout_status(state, profit_status, control_config)

          if payout_status.need_immediate_crash do
            Logger.warn("🚨 [REALTIME_RISK] #{payout_status.reason} - 当前倍率: #{current_multiplier / 100}x")
            true
          else
            false
          end
        end
      end
    end
  end

  @doc """
  基于玩家自动下车设置计算安全爆炸点位
  参考玩家的自动下车倍率，但不完全依赖（因为玩家可能手动下车）
  """
  defp calculate_safe_crash_point_based_on_players(state, profit_status, control_config) do
    # 获取所有未下车的真实玩家
    remaining_players = state.players
    |> Map.values()
    |> Enum.filter(fn player ->
      not Map.get(player, :is_robot, false) and
      not Map.get(player, :cashed_out, false) and
      Map.get(player, :bet_amount, 0) > 0
    end)

    if Enum.empty?(remaining_players) do
      # 没有剩余玩家，返回最小安全倍率
      105  # 1.05x
    else
      # 获取库存控制配置
      stock_config = CrashConfig.get_stock_control_config()
      current_stock = Map.get(state, :current_stock, stock_config.initial_stock)
      is_above_safety_line = current_stock >= stock_config.safety_line

      # 计算当前赔付率
      current_payout_ratio = if profit_status.total_bet > 0 do
        profit_status.total_payout / profit_status.total_bet
      else
        0.0
      end

      # 根据库存状态确定安全阈值
      safety_threshold = if is_above_safety_line do
        control_config.high_stock_payout_threshold  # 110%
      else
        control_config.low_stock_payout_threshold   # 100%
      end

      # 计算允许的剩余赔付空间
      remaining_payout_space = (safety_threshold - current_payout_ratio) * profit_status.total_bet

      if remaining_payout_space <= 0 do
        # 已经没有赔付空间，立即爆炸
        get_current_multiplier(0, state.game_data.config)  # 当前倍率
      else
        # 基于玩家自动下车设置和剩余赔付空间计算安全点位
        calculate_optimal_crash_point_for_players(remaining_players, remaining_payout_space, profit_status.total_bet)
      end
    end
  end

  @doc """
  为玩家计算最优爆炸点位
  考虑自动下车设置，但不完全依赖
  """
  defp calculate_optimal_crash_point_for_players(players, remaining_payout_space, total_bet) do
    # 分析玩家的自动下车设置
    auto_settings = Enum.map(players, fn player ->
      bet_amount = Map.get(player, :bet_amount, 0)
      auto_cash_out = Map.get(player, :auto_cash_out)

      %{
        bet_amount: bet_amount,
        auto_cash_out: auto_cash_out,
        weight: bet_amount / total_bet  # 下注权重
      }
    end)

    # 计算加权平均的参考爆炸点
    reference_crash_point = calculate_weighted_reference_crash_point(auto_settings)

    # 计算基于剩余赔付空间的最大安全倍率
    max_safe_multiplier = calculate_max_safe_multiplier_by_payout_space(players, remaining_payout_space)

    # 取两者中的较小值，确保安全
    safe_crash_point = min(reference_crash_point, max_safe_multiplier)

    # 确保最小倍率不低于1.05x
    max(safe_crash_point, 105)
  end

  @doc """
  计算加权平均的参考爆炸点
  """
  defp calculate_weighted_reference_crash_point(auto_settings) do
    # 分离有自动设置和无自动设置的玩家
    with_auto = Enum.filter(auto_settings, fn setting -> not is_nil(setting.auto_cash_out) end)
    without_auto = Enum.filter(auto_settings, fn setting -> is_nil(setting.auto_cash_out) end)

    # 计算有自动设置玩家的加权平均
    auto_weighted_avg = if Enum.empty?(with_auto) do
      nil
    else
      total_weight = Enum.sum(Enum.map(with_auto, & &1.weight))
      weighted_sum = Enum.reduce(with_auto, 0, fn setting, acc ->
        acc + setting.auto_cash_out * setting.weight
      end)
      round(weighted_sum / total_weight)
    end

    # 对于无自动设置的玩家，使用保守估计
    conservative_estimate = 180  # 1.80x 保守估计

    case {auto_weighted_avg, Enum.empty?(without_auto)} do
      {nil, false} ->
        # 只有无自动设置的玩家
        conservative_estimate
      {avg, true} ->
        # 只有有自动设置的玩家，但要打折扣（因为可能手动下车）
        round(avg * 0.85)  # 85%的折扣
      {avg, false} ->
        # 混合情况，按权重混合
        auto_weight = Enum.sum(Enum.map(with_auto, & &1.weight))
        manual_weight = Enum.sum(Enum.map(without_auto, & &1.weight))

        round((avg * 0.85 * auto_weight + conservative_estimate * manual_weight) / (auto_weight + manual_weight))
    end
  end

  @doc """
  基于剩余赔付空间计算最大安全倍率
  """
  defp calculate_max_safe_multiplier_by_payout_space(players, remaining_payout_space) do
    # 找到下注金额最大的玩家（最大风险）
    max_bet_player = Enum.max_by(players, fn player -> Map.get(player, :bet_amount, 0) end)
    max_bet_amount = Map.get(max_bet_player, :bet_amount, 0)

    if max_bet_amount == 0 do
      500  # 5.00x 默认值
    else
      # 计算最大安全倍率：剩余空间 / 最大下注金额
      max_safe_ratio = remaining_payout_space / max_bet_amount

      # 转换为倍率格式（×100）并添加安全边际
      safe_multiplier = round((1.0 + max_safe_ratio * 0.8) * 100)  # 80%的安全边际

      # 确保在合理范围内
      max(min(safe_multiplier, 1000), 105)  # 1.05x - 10.00x
    end
  end

  @doc """
  处理游戏爆炸，结算未下车的玩家
  """
  def handle_crash(state, crash_time_ms) do
    config = state.game_data.config
    crash_multiplier = get_current_multiplier(crash_time_ms, config)

    # 处理所有未下车的玩家
    updated_players =
      state.players
      |> Enum.map(fn {numeric_id, player} ->
        if Map.get(player, :bet_amount, 0) > 0 and not Map.get(player, :cashed_out, false) do
          # 玩家没有下车，损失下注金额
          updated_player =
            Map.merge(player, %{
              crashed: true,
              crash_time: crash_time_ms,
              crash_multiplier: crash_multiplier,
              payout: 0,
              profit: -player.bet_amount
            })

          {numeric_id, updated_player}
        else
          {numeric_id, player}
        end
      end)
      |> Enum.into(%{})

    # 更新游戏数据
    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :settling,
        crash_time: crash_time_ms,
        crash_multiplier: crash_multiplier,
        crashed: true
      })

    updated_state = %{state | players: updated_players, game_data: updated_game_data}

    {:ok, updated_state}
  end

  # ==================== 私有函数 ====================

  # 安全的随机数生成函数，确保参数总是正整数
  defp safe_uniform(n) when is_integer(n) and n > 0, do: :rand.uniform(n)
  defp safe_uniform(_), do: 1

  # 没有真实玩家下注时的倍数计算 - 使用配置文件中的分布
  defp calculate_no_bet_multiplier() do
    # 从配置获取无真实玩家时的分布
    distribution = CrashConfig.get_distribution_by_player_status(false)

    # 使用配置生成倍率
    multiplier = CrashConfig.generate_multiplier_from_distribution(distribution)

    Logger.info(
      "🎲 [CRASH_LOGIC] 无真实玩家，生成倍数: #{multiplier / 100}x (观看者模式)"
    )

    multiplier
  end

  # 智能倍数计算（基于实时盈亏状况动态调整）
  defp calculate_intelligent_multiplier(state, betting_players) do
    Logger.info("🧠 [CRASH_LOGIC] 开始智能计算爆炸倍数 - 基于实时盈亏率")

    # 🎯 首先进行基于单局盈亏的智能风控分析
    # 在计算爆炸点时，使用0作为elapsed_time_ms（游戏开始时）
    {has_profit_surplus, amount, analysis_msg} = analyze_recent_profit_trend_with_current_game(state, 0)

    Logger.info("🎯 [SMART_ANALYSIS] #{analysis_msg}")

    if has_profit_surplus do
      # 有盈余，可以适当"吐出来"给玩家，使用更高的倍率
      Logger.info("💰 [SMART_CONTROL] 有盈余#{amount}，允许适当放宽控制，使用较高倍率")

      # 获取放宽控制的分布（更多高倍率）
      relaxed_distribution = CrashConfig.get_distribution_by_player_status(false)  # false表示放宽
      base_multiplier = CrashConfig.generate_multiplier_from_distribution(relaxed_distribution)

      Logger.info("🎲 [CRASH_LOGIC] 放宽控制倍数: #{base_multiplier / 100}x")
      base_multiplier
    else
      # 没有盈余或会亏损，需要严格控制
      Logger.warn("🚨 [SMART_CONTROL] 会亏损#{amount}，需要严格控制")

      # 计算当前局的实时盈亏状况
      profit_status = calculate_realtime_profit(state)
      current_stock = Map.get(state, :current_stock, 10_000_000)

      # 计算赔付率
      payout_ratio = if profit_status.total_bet > 0 do
        profit_status.total_payout / profit_status.total_bet
      else
        0.0
      end

      Logger.info("💰 [REALTIME_PAYOUT] 当前状况: 总下注#{profit_status.total_bet}, 已赔付#{profit_status.total_payout}, 赔付率#{Float.round(payout_ratio * 100, 1)}%, 库存#{current_stock}, 剩余玩家#{profit_status.remaining_players}")

      # 获取赔付控制配置
      control_config = CrashConfig.get_payout_control_config()

      # 计算当前赔付状况
      payout_status = calculate_payout_status(state, profit_status, control_config)

      # 获取严格控制的分布（更多低倍率）
      strict_distribution = CrashConfig.get_distribution_by_player_status(true)  # true表示严格

      # 根据实时赔付率进一步调整分布
      adjusted_distribution = CrashConfig.adjust_distribution_by_payout_ratio(strict_distribution, payout_status, control_config)

      # 生成基础倍率
      base_multiplier = CrashConfig.generate_multiplier_from_distribution(adjusted_distribution)

      Logger.info("🎲 [CRASH_LOGIC] 严格控制倍数: #{base_multiplier / 100}x (赔付率调整)")

      # 🎯 关键修复：调用异常玩家检测和调整
      final_multiplier = detect_suspicious_players_and_adjust(state, base_multiplier)

      Logger.info("🎲 [CRASH_LOGIC] 最终倍数: #{final_multiplier / 100}x (异常检测调整)")
      final_multiplier
    end
  end

  # 保留原有的倍数计算作为备用（向后兼容） - 使用配置化分布
  defp calculate_with_bet_multiplier(state, betting_players) do
    Logger.info("🎲 [CRASH_LOGIC] 使用传统算法计算倍数")

    # 计算总下注金额
    total_bet_amount =
      Enum.reduce(betting_players, 0, fn player, acc ->
        acc + Map.get(player, :bet_amount, 0)
      end)

    # 获取库存控制状态（使用新的配置方式）
    stock_control = get_stock_control_state_from_config(state)

    # 使用配置化的分布系统替代硬编码逻辑
    distribution = CrashConfig.get_distribution_by_player_status(true)
    multiplier = CrashConfig.generate_multiplier_from_distribution(distribution)

    Logger.info("🎲 [CRASH_LOGIC] 传统算法生成倍数: #{multiplier / 100}x (库存状态: #{stock_control.status})")

    multiplier
  end


  # ==================== 智能计算核心函数 ====================

  # 分析所有玩家行为（使用state中的历史记录）
  defp analyze_all_players_behavior(players, state) do
    Enum.map(players, fn {player_id, player} ->
      # 获取玩家历史数据（从state中获取）
      history = get_player_history_from_state(player_id, state)

      # 分析行为模式
      behavior_pattern = analyze_player_behavior_pattern(history)

      # 预测下车倍数
      {predicted_cashout, confidence} = predict_cashout_with_confidence(player, behavior_pattern)

      %{
        player_id: player_id,
        bet_amount: Map.get(player, :bet_amount, 0),
        auto_cash_out: Map.get(player, :auto_cash_out),  # 使用现有字段名
        cashed_out: Map.get(player, :cashed_out, false),
        cash_out_multiplier: Map.get(player, :cash_out_multiplier),
        predicted_cashout: predicted_cashout,
        confidence: confidence,
        behavior_pattern: behavior_pattern,
        is_robot: Map.get(player, :is_robot, false)
      }
    end)
  end

  # 从state中获取玩家历史记录
  defp get_player_history_from_state(player_id, state) do
    player_histories = Map.get(state, :player_histories, %{})
    Map.get(player_histories, player_id, %{
      total_profit: 0,
      recent_games: []  # 最近20局的记录
    })
  end

  # 获取库存控制状态（基于安全线计算）
  defp get_stock_control_state_from_config(state) do
    # 从game_data获取库存控制配置
    stock_config = Map.get(state.game_data, :stock_config, %{})

    # 获取配置参数
    safety_line = Map.get(stock_config, :safety_line, 500_000)
    collect_threshold = Map.get(stock_config, :collect_threshold, 0.5)  # 50%
    release_threshold = Map.get(stock_config, :release_threshold, 1.5)  # 150%

    # 从state获取当前库存分
    current_stock = Map.get(state, :current_stock, Map.get(stock_config, :initial_stock, 1_000_000))

    # 计算相对于安全线的比例
    safety_ratio = current_stock / safety_line

    # 判断库存状态（基于安全线）
    status = cond do
      safety_ratio >= release_threshold -> :high  # 高于安全线150%，放分
      safety_ratio <= collect_threshold -> :low   # 低于安全线50%，收分
      true -> :safe                               # 50%-150%之间，安全
    end

    Logger.info("🏦 [STOCK_CONTROL] 当前库存:#{current_stock}, 安全线:#{safety_line}, 比例:#{Float.round(safety_ratio * 100, 1)}%, 状态:#{status}")

    %{
      current_stock: current_stock,
      safety_line: safety_line,
      safety_ratio: safety_ratio,
      status: status,
      collect_threshold: collect_threshold,
      release_threshold: release_threshold
    }
  end

  # 计算当前财务状况
  defp calculate_current_financial_status(state) do
    players = state.players

    # 计算总下注金额
    total_bets = Enum.sum(Enum.map(players, fn {_id, player} ->
      Map.get(player, :bet_amount, 0)
    end))

    # 计算已下车玩家的盈利
    cashed_out_profit = Enum.reduce(players, 0, fn {_id, player}, acc ->
      if Map.get(player, :cashed_out, false) do
        profit = Map.get(player, :profit, 0)
        acc + profit
      else
        acc
      end
    end)

    # 计算未下车玩家的下注金额
    remaining_bets = Enum.reduce(players, 0, fn {_id, player}, acc ->
      if not Map.get(player, :cashed_out, false) and Map.get(player, :bet_amount, 0) > 0 do
        acc + Map.get(player, :bet_amount, 0)
      else
        acc
      end
    end)

    # 当前系统盈亏状况
    current_system_profit = remaining_bets - cashed_out_profit

    %{
      total_bets: total_bets,
      cashed_out_profit: cashed_out_profit,
      remaining_bets: remaining_bets,
      current_system_profit: current_system_profit
    }
  end

  # 预测未来赔付需求
  defp predict_future_payouts(player_analysis) do
    # 只考虑未下车的玩家
    remaining_players = Enum.filter(player_analysis, fn player ->
      not player.cashed_out and player.bet_amount > 0
    end)

    # 按预测下车倍数排序
    sorted_players = Enum.sort_by(remaining_players, &(&1.predicted_cashout))

    # 生成关键爆炸点
    key_points = [100, 110, 120, 150, 200, 300] ++
                 Enum.map(sorted_players, &(&1.predicted_cashout))
    |> Enum.uniq()
    |> Enum.sort()

    Enum.map(key_points, fn explosion_point ->
      # 计算在此点爆炸的赔付情况
      successful_players = Enum.filter(sorted_players, fn player ->
        player.predicted_cashout <= explosion_point
      end)

      failed_players = Enum.filter(sorted_players, fn player ->
        player.predicted_cashout > explosion_point
      end)

      # 计算系统收入（失败玩家的下注）
      system_income = Enum.sum(Enum.map(failed_players, &(&1.bet_amount)))

      # 计算赔付支出（成功玩家的收益）
      payout_cost = Enum.sum(Enum.map(successful_players, fn player ->
        payout = trunc(player.bet_amount * explosion_point / 100)
        payout - player.bet_amount  # 只计算收益部分
      end))

      # 计算净盈利
      net_profit = system_income - payout_cost

      %{
        explosion_point: explosion_point,
        successful_players: length(successful_players),
        failed_players: length(failed_players),
        system_income: system_income,
        payout_cost: payout_cost,
        net_profit: net_profit,
        player_satisfaction: if(length(sorted_players) > 0, do: length(successful_players) / length(sorted_players), else: 0.5)
      }
    end)
  end

  # 决定最优倍数 - 使用新的配置化分布系统
  defp determine_optimal_multiplier(financial_status, future_payouts, stock_control, player_analysis) do
    # 如果没有剩余玩家，使用配置化的分布系统
    if financial_status.remaining_bets == 0 do
      # 使用无真实玩家的分布
      distribution = CrashConfig.get_distribution_by_player_status(false)
      multiplier = CrashConfig.generate_multiplier_from_distribution(distribution)
      Logger.info("🎲 [CRASH_LOGIC] 无剩余玩家，使用观看者分布: #{multiplier / 100}x")
      multiplier
    else
      # 筛选可行的爆炸点（确保系统整体盈利）
      viable_scenarios = Enum.filter(future_payouts, fn scenario ->
        total_system_profit = financial_status.current_system_profit + scenario.net_profit
        total_system_profit >= 0  # 确保系统不亏损
      end)

      if Enum.empty?(viable_scenarios) do
        # 如果没有可行方案，使用保守策略
        Logger.warning("🚨 [CRASH_LOGIC] 无可行方案，使用保守策略")
        conservative_multiplier = if Enum.empty?(player_analysis) do
          150  # 1.50x 默认保守值
        else
          predicted_cashouts = Enum.map(player_analysis, &(&1.predicted_cashout))
          min_predicted = Enum.min(predicted_cashouts)
          max(100, trunc(min_predicted * 0.9))
        end
        conservative_multiplier
      else
        # 选择最优方案
        optimal_scenario = select_optimal_scenario(viable_scenarios, stock_control, financial_status)
        optimal_scenario.explosion_point
      end
    end
  end

  # 选择最优场景
  defp select_optimal_scenario(viable_scenarios, stock_control, financial_status) do
    # 根据库存状态调整评分权重
    {profit_weight, satisfaction_weight, timing_weight} = case stock_control.status do
      :high -> {0.2, 0.6, 0.2}  # 高库存优先玩家体验
      :low -> {0.7, 0.2, 0.1}   # 低库存优先系统盈利
      :safe -> {0.4, 0.4, 0.2}  # 安全库存平衡
    end

    # 评分并选择最优方案
    scored_scenarios = Enum.map(viable_scenarios, fn scenario ->
      # 系统盈利分数 (0-1)
      total_profit = financial_status.current_system_profit + scenario.net_profit
      profit_score = min(1.0, max(0.0, total_profit / max(1, financial_status.total_bets * 0.1)))

      # 玩家体验分数 (0-1)
      satisfaction_score = scenario.player_satisfaction

      # 避免过早爆炸的分数
      timing_score = if scenario.explosion_point <= 105 do  # 1.05x以下
        0.1  # 大幅降低分数
      else
        1.0
      end

      # 综合评分
      total_score = profit_score * profit_weight +
                   satisfaction_score * satisfaction_weight +
                   timing_score * timing_weight

      Map.put(scenario, :score, total_score)
    end)

    # 选择得分最高的方案
    Enum.max_by(scored_scenarios, &(&1.score))
  end

  # ==================== 玩家行为分析辅助函数 ====================

  # 分析玩家行为模式（使用真实历史记录）
  defp analyze_player_behavior_pattern(history) do
    recent_games = Map.get(history, :recent_games, [])

    if Enum.empty?(recent_games) do
      %{avg_cashout: 180, consistency: 0.5, risk_level: :medium, total_profit: 0}
    else
      cashouts = Enum.map(recent_games, &(&1.cash_out_multiplier))
      avg_cashout = Enum.sum(cashouts) / length(cashouts)

      # 计算一致性（标准差的倒数）
      variance = Enum.sum(Enum.map(cashouts, fn x -> :math.pow(x - avg_cashout, 2) end)) / length(cashouts)
      std_dev = :math.sqrt(variance)
      consistency = max(0.1, 1.0 - (std_dev / avg_cashout))

      risk_level = cond do
        avg_cashout < 150 -> :very_conservative
        avg_cashout < 200 -> :conservative
        avg_cashout < 300 -> :moderate
        true -> :aggressive
      end

      %{
        avg_cashout: trunc(avg_cashout),
        consistency: consistency,
        risk_level: risk_level,
        total_profit: Map.get(history, :total_profit, 0),
        game_count: length(recent_games)
      }
    end
  end

  # 预测玩家下车倍数和置信度
  defp predict_cashout_with_confidence(player, behavior_pattern) do
    auto_setting = Map.get(player, :auto_cash_out)

    base_prediction = case auto_setting do
      nil ->
        # 无自动设置，基于历史行为
        behavior_pattern.avg_cashout

      auto_mult ->
        # 有自动设置，结合历史行为
        if behavior_pattern.consistency > 0.7 do
          auto_mult  # 高一致性，信任自动设置
        else
          # 低一致性，历史行为权重更高
          trunc(auto_mult * 0.3 + behavior_pattern.avg_cashout * 0.7)
        end
    end

    # 添加随机性
    variation = trunc(base_prediction * 0.1)
    predicted = base_prediction + :rand.uniform(max(1, variation * 2)) - variation

    # 计算置信度
    confidence = case behavior_pattern.consistency do
      c when c > 0.8 -> 0.9
      c when c > 0.6 -> 0.7
      c when c > 0.4 -> 0.5
      _ -> 0.3
    end

    {max(105, predicted), confidence}
  end

  # ==================== 基于单局盈亏的智能风控 ====================

  @doc """
  分析最近盈利趋势并考虑当前局预计亏损
  从最近系统盈利的那一局开始计算净盈亏，并加上当前局玩家预计盈利
  """
  defp analyze_recent_profit_trend_with_current_game(state, elapsed_time_ms) do
    # 获取最近3-5局的盈利记录
    recent_profits = get_recent_game_profits(state, 5)

    # 使用已有的智能预测逻辑计算当前局预计亏损
    current_expected_loss = calculate_current_game_expected_loss_smart(state, elapsed_time_ms)

    case find_most_recent_profit_game(recent_profits) do
      nil ->
        # 没有找到盈利局，返回累计亏损加上当前局预计亏损
        total_loss = Enum.sum(recent_profits)
        final_balance = total_loss - current_expected_loss  # total_loss是负数
        {false, abs(final_balance), "最近5局无盈利，累计亏损#{abs(total_loss)}，下个倍率点预计亏损#{current_expected_loss}，最终亏损#{abs(final_balance)}"}

      profit_amount ->
        # 找到盈利局，计算盈利后的净亏损，再减去当前局预计亏损
        profit_index = Enum.find_index(recent_profits, &(&1 == profit_amount))
        losses_after_profit = Enum.drop(recent_profits, profit_index + 1)
        total_losses_after = Enum.sum(losses_after_profit)
        net_profit_before_current = profit_amount + total_losses_after  # total_losses_after是负数
        final_balance = net_profit_before_current - current_expected_loss

        if final_balance > 0 do
          {true, final_balance, "从盈利局#{profit_amount}到现在净盈利#{net_profit_before_current}，下个倍率点预计亏损#{current_expected_loss}，最终盈余#{final_balance}"}
        else
          {false, abs(final_balance), "从盈利局#{profit_amount}到现在净盈利#{net_profit_before_current}，下个倍率点预计亏损#{current_expected_loss}，最终亏损#{abs(final_balance)}"}
        end
    end
  end

  @doc """
  获取最近几局的盈利记录
  """
  defp get_recent_game_profits(state, limit) do
    system_recent_games = Map.get(state, :system_recent_games, [])

    # system_recent_games 已经是数字列表，直接取最近的几局记录
    system_recent_games
    |> Enum.take(limit)
  end

  @doc """
  找到最近的盈利局
  """
  defp find_most_recent_profit_game(recent_profits) do
    Enum.find(recent_profits, fn profit -> profit > 0 end)
  end

  @doc """
  计算当前局如果飞到下一个倍率点的预计亏损
  """
  defp calculate_current_game_expected_loss_smart(state, elapsed_time_ms) do
    # 使用传递的elapsed_time_ms参数计算当前和下一个时间点
    current_time_ms = elapsed_time_ms
    current_multiplier = get_current_multiplier(current_time_ms, state.game_data.config)
    next_time_ms = current_time_ms + 100  # 预测100ms后的情况
    next_multiplier = get_current_multiplier(next_time_ms, state.game_data.config)

    # 打印调试信息
    Logger.info("🔍 [EXPECTED_LOSS_DEBUG] 当前时间#{current_time_ms}ms，当前倍率#{Float.round(current_multiplier / 100, 2)}x，下个时间#{next_time_ms}ms，下个倍率#{Float.round(next_multiplier / 100, 2)}x")
    # 获取所有未下车的真实玩家
    remaining_players = state.players
    |> Map.values()
    |> Enum.filter(fn player ->
      not Map.get(player, :is_robot, false) and
      not Map.get(player, :cashed_out, false) and
      Map.get(player, :bet_amount, 0) > 0
    end)

    if Enum.empty?(remaining_players) do
      # 没有剩余玩家，无预计亏损
      0
    else
      # 计算如果飞到下一个倍率点，系统需要赔付多少
      total_expected_payout = Enum.reduce(remaining_players, 0, fn player, acc ->
        bet_amount = Map.get(player, :bet_amount, 0)
        auto_cash_out = Map.get(player, :auto_cash_out)
        player_id = Map.get(player, :numeric_id) || Map.get(player, :player_id)

        # 打印玩家信息
        Logger.info("🔍 [PLAYER_DEBUG] 玩家#{player_id}，下注#{bet_amount}，自动下车#{auto_cash_out}")

        # 判断玩家是否会在下一个倍率点前下车
        will_cash_out_before_next = if auto_cash_out && auto_cash_out <= next_multiplier do
          true  # 有自动下车且会在下一个倍率点前触发
        else
          false # 没有自动下车或不会在下一个倍率点前触发
        end

        expected_payout = if will_cash_out_before_next do
          # 玩家会在下一个倍率点前下车，按自动下车倍率计算赔付
          payout = bet_amount * auto_cash_out / 100
          Logger.info("🔍 [PAYOUT_DEBUG] 玩家#{player_id}会在#{Float.round(auto_cash_out / 100, 2)}x下车，预计赔付#{payout}")
          payout
        else
          # 玩家不会在下一个倍率点前下车，如果在下一个倍率点爆炸玩家损失下注，系统无需赔付
          Logger.info("🔍 [PAYOUT_DEBUG] 玩家#{player_id}不会在下个倍率点前下车，预计赔付0")
          0
        end

        acc + expected_payout
      end)

      # 计算总下注
      total_bet = Enum.reduce(remaining_players, 0, fn player, acc ->
        acc + Map.get(player, :bet_amount, 0)
      end)

      # 预计亏损 = 预计赔付 - 总下注
      expected_loss = total_expected_payout - total_bet
      max(0, expected_loss)  # 确保不为负数
    end
  end

  # ==================== 异常玩家检测 ====================

  @doc """
  检测异常玩家并调整爆炸点
  """
  defp detect_suspicious_players_and_adjust(state, normal_multiplier) do
    player_histories = Map.get(state, :player_histories, %{})
    system_recent_games = Map.get(state, :system_recent_games, [])

    # 检查系统是否连续超过5局亏损
    consecutive_losses = check_consecutive_system_losses(system_recent_games)

    # 获取库存控制状态
    stock_state = get_stock_control_state_from_config(state)

    # 只检查当前下注的真实玩家
    betting_player_ids = get_betting_player_ids(state)

    # 检查当前下注的玩家中是否有异常玩家
    suspicious_players = Enum.filter(player_histories, fn {player_id, history} ->
      # 只检查当前下注的玩家
      Enum.member?(betting_player_ids, player_id) and
      is_suspicious_player?(player_id, history, consecutive_losses, state)
    end)

    # 综合判断是否需要异常控制
    need_control = should_apply_suspicious_control?(suspicious_players, consecutive_losses, stock_state)

    if not need_control do
      Logger.info("🔍 [SUSPICIOUS_CHECK] 当前下注玩家#{length(betting_player_ids)}人，连续亏损#{consecutive_losses}局，库存状态#{stock_state.status}，无需异常控制，使用正常爆炸点: #{normal_multiplier / 100}x")
      normal_multiplier
    else
      Logger.warning("🚨 [SUSPICIOUS_DETECTED] 当前下注玩家中发现#{length(suspicious_players)}个异常玩家，连续亏损#{consecutive_losses}局，库存状态#{stock_state.status}，启动异常控制")

      # 针对异常玩家计算爆炸点
      calculate_targeted_crash_point(state, suspicious_players, normal_multiplier)
    end
  end

  @doc """
  判断是否应该应用异常控制
  """
  defp should_apply_suspicious_control?(suspicious_players, consecutive_losses, stock_state) do
    # 如果没有异常玩家，不需要控制
    if Enum.empty?(suspicious_players) do
      false
    else
      # 根据异常玩家数量、连续亏损和库存状态综合判断
      suspicious_count = length(suspicious_players)

      case {suspicious_count, consecutive_losses, stock_state.status} do
        # 多个异常玩家且连续亏损，需要控制
        {count, losses, _} when count >= 2 and losses >= 3 -> true
        # 单个异常玩家但连续亏损严重，需要控制
        {1, losses, _} when losses >= 5 -> true
        # 低库存状态下，降低控制阈值
        {count, losses, :low} when count >= 1 and losses >= 2 -> true
        # 其他情况不需要控制
        _ -> false
      end
    end
  end

  @doc """
  判断是否为异常玩家
  """
  defp is_suspicious_player?(player_id, history, consecutive_losses, state) do
    recent_games = Map.get(history, :recent_games, [])
    total_profit = Map.get(history, :total_profit, 0)

    # 检查条件1: 个人赢超过10000000分
    condition1 = total_profit > 1000_0000

    # 检查条件2: 赢超过自己金额的50倍
    condition2 = check_profit_vs_original_balance(recent_games)

    # 检查条件3: 总是在低倍率下车
    condition3 = check_low_multiplier_pattern(recent_games)

    # 检查条件4: 只有少数玩家时的异常行为
    condition4 = check_few_players_suspicious_behavior(player_id, state)
    # 只有在系统连续超过5局亏损时才触发异常玩家机制
    is_suspicious = (condition1 or condition2 or condition3 or condition4) and consecutive_losses > 5

    if is_suspicious do
      Logger.warning("🚨 [SUSPICIOUS_PLAYER] 玩家#{player_id} 异常: 总盈利#{total_profit}, 连续亏损#{consecutive_losses}局, 条件1:#{condition1}, 条件2:#{condition2}, 条件3:#{condition3}, 条件4:#{condition4}")
    end

    is_suspicious
  end

  @doc """
  检查盈利是否超过原始金额的50倍
  """
  defp check_profit_vs_original_balance(recent_games) do
    if Enum.empty?(recent_games) do
      false
    else
      # 获取最早的原始金额作为基准
      earliest_game = List.last(recent_games)
      original_balance = Map.get(earliest_game, :original_balance, 0)

      # 计算总盈利
      total_profit = Enum.sum(Enum.map(recent_games, &Map.get(&1, :profit, 0)))

      # 检查是否超过50倍
      original_balance > 0 and total_profit > original_balance * 50
    end
  end

  @doc """
  检查是否总是在低倍率下车
  """
  defp check_low_multiplier_pattern(recent_games) do
    if length(recent_games) < 5 do  # 至少5局才判断
      false
    else
      # 检查最近10局中是否80%以上都在1.3x以下下车
      recent_10_games = Enum.take(recent_games, 10)
      low_multiplier_games = Enum.count(recent_10_games, fn game ->
        Map.get(game, :cash_out_multiplier, 999) <= 130  # 1.3x以下
      end)

      low_multiplier_games >= length(recent_10_games) * 0.8
    end
  end

  @doc """
  检查少数玩家时的异常行为
  """
  defp check_few_players_suspicious_behavior(player_id, state) do
    # 获取当前真实玩家数量
    real_players = Enum.count(state.players, fn {_id, player} ->
      not Map.get(player, :is_robot, false)
    end)

    # 只有3个或更少真实玩家时才检查
    if real_players <= 3 do
      player_history = get_in(state, [:player_histories, player_id])
      if player_history do
        recent_games = Map.get(player_history, :recent_games, [])

        # 检查最近5局是否都在1.1x下车
        recent_5_games = Enum.take(recent_games, 5)
        if length(recent_5_games) >= 3 do
          very_low_multiplier_games = Enum.count(recent_5_games, fn game ->
            Map.get(game, :cash_out_multiplier, 999) <= 110  # 1.1x以下
          end)

          very_low_multiplier_games >= 3
        else
          false
        end
      else
        false
      end
    else
      false
    end
  end

  @doc """
  计算针对异常玩家的爆炸点
  """
  defp calculate_targeted_crash_point(state, suspicious_players, normal_multiplier) do
    # 获取异常玩家的预期下车点
    suspicious_cash_out_points = Enum.map(suspicious_players, fn {player_id, history} ->
      recent_games = Map.get(history, :recent_games, [])

      if Enum.empty?(recent_games) do
        150  # 默认1.5x
      else
        # 计算平均下车倍数
        avg_multiplier = Enum.sum(Enum.map(recent_games, &Map.get(&1, :cash_out_multiplier, 150))) / length(recent_games)
        trunc(avg_multiplier)
      end
    end)

    # 找到最低的异常玩家下车点
    min_suspicious_point = Enum.min(suspicious_cash_out_points)

    # 计算针对性爆炸点（在异常玩家下车前爆炸）
    targeted_multiplier = max(100, trunc(min_suspicious_point * 0.9))  # 90%的位置爆炸

    Logger.warning("🎯 [TARGETED_CRASH] 异常玩家平均下车点: #{Enum.sum(suspicious_cash_out_points) / length(suspicious_cash_out_points) / 100}x")
    Logger.warning("🎯 [TARGETED_CRASH] 最低下车点: #{min_suspicious_point / 100}x")
    Logger.warning("🎯 [TARGETED_CRASH] 针对性爆炸点: #{targeted_multiplier / 100}x (正常: #{normal_multiplier / 100}x)")

    targeted_multiplier
  end

  @doc """
  检查系统连续亏损局数
  """
  defp check_consecutive_system_losses(system_recent_games) do
    if Enum.empty?(system_recent_games) do
      0
    else
      # 从最新的游戏开始计算连续亏损
      system_recent_games
      |> Enum.take_while(fn profit -> profit < 0 end)  # 取连续的负数（亏损）
      |> length()
    end
  end

  @doc """
  获取当前下注的真实玩家ID列表
  """
  defp get_betting_player_ids(state) do
    state.players
    |> Enum.filter(fn {_player_id, player} ->
      # 只包含真实玩家且已下注的玩家
      not Map.get(player, :is_robot, false) and
      Map.get(player, :bet_amount, 0) > 0 and
      not Map.get(player, :cashed_out, false)
    end)
    |> Enum.map(fn {player_id, _player} -> player_id end)
  end

  # ==================== 实时赔付率计算函数 ====================

  @doc """
  计算当前局的实时盈亏状况
  """
  defp calculate_realtime_profit(state) do
    # 计算真实玩家总下注金额
    total_real_bet = calculate_total_real_player_bets(state)

    # 计算已下车真实玩家总赢取金额
    total_real_payout = calculate_total_real_player_payouts(state)

    # 当前系统盈亏 = 收到的下注 - 已支付的奖金
    current_profit = total_real_bet - total_real_payout

    %{
      total_bet: total_real_bet,
      total_payout: total_real_payout,
      current_profit: current_profit,
      remaining_players: count_remaining_real_players(state)
    }
  end

  @doc """
  计算当前赔付状况和风险等级
  """
  defp calculate_payout_status(state, profit_data, control_config) do
    # 获取库存控制配置（统一使用库存控制中的安全线）
    stock_config = CrashConfig.get_stock_control_config()
    current_stock = Map.get(state, :current_stock, stock_config.initial_stock)

    # 判断库存状态
    is_above_safety_line = current_stock >= stock_config.safety_line

    # 计算当前赔付率
    current_payout_ratio = if profit_data.total_bet > 0 do
      profit_data.total_payout / profit_data.total_bet
    else
      0.0
    end

    # 预测下个玩家的赔付风险
    next_player_risk = predict_next_player_payout_risk(state, profit_data, control_config)

    # 判断控制策略
    cond do
      # 高于安全线：赔付率超过110%时尽快爆炸
      is_above_safety_line and current_payout_ratio > control_config.high_stock_payout_threshold ->
        %{
          need_immediate_crash: true,
          need_adjustment: false,
          risk_level: :high,
          reason: "高库存赔付率#{Float.round(current_payout_ratio * 100, 1)}%超过#{control_config.high_stock_payout_threshold * 100}%"
        }

      # 低于安全线：当前+预测赔付率超过100%时尽快爆炸
      not is_above_safety_line and (current_payout_ratio + next_player_risk.predicted_ratio) > control_config.low_stock_payout_threshold ->
        %{
          need_immediate_crash: true,
          need_adjustment: false,
          risk_level: :critical,
          reason: "低库存总赔付率#{Float.round((current_payout_ratio + next_player_risk.predicted_ratio) * 100, 1)}%超过#{control_config.low_stock_payout_threshold * 100}%"
        }

      # 需要调整但不需要立即爆炸
      current_payout_ratio > 0.8 ->
        %{
          need_immediate_crash: false,
          need_adjustment: true,
          risk_level: calculate_risk_level(current_payout_ratio, is_above_safety_line),
          reason: "赔付率偏高需要调整"
        }

      # 正常状况
      true ->
        %{
          need_immediate_crash: false,
          need_adjustment: false,
          risk_level: :normal,
          reason: "正常状况"
        }
    end
  end

  # 私有函数：计算真实玩家总下注金额
  defp calculate_total_real_player_bets(state) do
    state.players
    |> Map.values()
    |> Enum.filter(fn player -> not Map.get(player, :is_robot, false) end)
    |> Enum.map(fn player -> Map.get(player, :bet_amount, 0) end)
    |> Enum.sum()
  end

  # 私有函数：计算已下车真实玩家总赢取金额
  defp calculate_total_real_player_payouts(state) do
    state.players
    |> Map.values()
    |> Enum.filter(fn player ->
      not Map.get(player, :is_robot, false) and Map.get(player, :cashed_out, false)
    end)
    |> Enum.map(fn player -> Map.get(player, :payout, 0) end)
    |> Enum.sum()
  end

  # 私有函数：计算剩余未下车的真实玩家数量
  defp count_remaining_real_players(state) do
    state.players
    |> Map.values()
    |> Enum.count(fn player ->
      not Map.get(player, :is_robot, false) and not Map.get(player, :cashed_out, false)
    end)
  end

  # 私有函数：预测下个玩家的赔付风险（使用现有的智能预测逻辑）
  defp predict_next_player_payout_risk(state, profit_data, control_config) do
    if profit_data.remaining_players == 0 do
      # 没有剩余玩家，无风险
      %{predicted_payout: 0, predicted_ratio: 0.0}
    else
      # 找到下注金额最大的未下车玩家（通常最可能下车）
      max_bet_player = state.players
      |> Map.values()
      |> Enum.filter(fn player ->
        not Map.get(player, :is_robot, false) and
        not Map.get(player, :cashed_out, false) and
        Map.get(player, :bet_amount, 0) > 0
      end)
      |> Enum.max_by(fn player -> Map.get(player, :bet_amount, 0) end, fn -> nil end)

      if max_bet_player do
        bet_amount = Map.get(max_bet_player, :bet_amount, 0)

        # 使用现有的智能预测逻辑，而不是固定值
        player_id = Map.get(max_bet_player, :numeric_id) || Map.get(max_bet_player, :player_id)
        history = get_player_history_from_state(player_id, state)
        behavior_pattern = analyze_player_behavior_pattern(history)
        {predicted_multiplier_int, _confidence} = predict_cashout_with_confidence(max_bet_player, behavior_pattern)

        # 转换为小数倍率
        predicted_multiplier = predicted_multiplier_int / 100.0
        predicted_payout = bet_amount * predicted_multiplier
        predicted_ratio = if profit_data.total_bet > 0 do
          predicted_payout / profit_data.total_bet
        else
          0.0
        end

        %{
          predicted_payout: predicted_payout,
          predicted_ratio: predicted_ratio,
          player_bet: bet_amount,
          predicted_multiplier: predicted_multiplier
        }
      else
        %{predicted_payout: 0, predicted_ratio: 0.0}
      end
    end
  end

  # 私有函数：计算风险等级
  defp calculate_risk_level(payout_ratio, is_above_safety_line) do
    cond do
      payout_ratio > 1.0 -> :critical
      payout_ratio > 0.9 -> :high
      payout_ratio > 0.8 -> :medium
      true -> :low
    end
  end
end
