defmodule Cypridina.Teen.GameSystem.Games.Crash.CrashRoom do
  @moduledoc """
  Crash游戏房间实现

  ## 游戏流程
  1. **等待阶段** - 房间等待玩家加入
  2. **下注阶段** - 玩家下注
  3. **飞行阶段** - 倍数上升，玩家可以下车
  4. **结算阶段** - 计算输赢，准备下一轮

  ## 游戏规则
  - Crash是一个百人场游戏
  - 玩家在下注阶段下注
  - 飞行阶段倍数不断上升，玩家可以随时下车获得收益
  - 游戏会在随机时刻爆炸，未下车的玩家失去下注金额
  """

  use Cypridina.Teen.GameSystem.RoomBase, game_type: :crash

  alias Cypridina.Teen.GameSystem.Games.Crash.{
    CrashGame,
    CrashLogic,
    CrashMessageBuilder,
    CrashAI,
    CrashRobotManager,
    CrashConfig
  }

  require Logger

  # ==================== RoomBehaviour 回调函数实现 ====================

  @doc """
  获取最小玩家数 - Crash是百人场游戏，最小1人即可开始
  """
  @impl true
  def min_players, do: 1

  @doc """
  获取最大玩家数 - Crash是百人场游戏，支持大量玩家
  """
  @impl true
  def max_players, do: 100

  @doc """
  判断游戏是否结束 - Crash是持续运行的百人场游戏，不会结束
  """
  @impl true
  def game_over?(_state), do: false

  @doc """
  获取游戏胜者 - Crash每局都有胜负，但不是传统意义的玩家对战
  """
  @impl true
  def get_winner(state) do
    case state.game_data.phase do
      :settling ->
        # 在结算阶段，可以返回本局的爆炸倍数
        case Map.get(state.game_data, :crash_multiplier) do
          nil -> :none
          multiplier -> {:ok, "#{multiplier / 100}x"}
        end

      _ ->
        :none
    end
  end

  # ==================== 1. 初始化阶段 ====================

  @doc """
  初始化游戏逻辑
  """
  @impl true
  def init_game_logic(state) do
    unified_config = CrashGame.full_config(state.config)

    state
    |> build_initial_game_state(unified_config)
    |> start_game_loop()
  end

  @doc """
  开始游戏循环
  """
  @impl true
  def on_game_start(state) do
    Logger.info("🎰 [CRASH_GAME_START] 开始新一轮游戏 - 房间: #{state.id}, 轮次: #{state.game_data.round + 1}")

    # 重置游戏数据，但保留上局的赢分排行榜记录
    Logger.info("🏆 [CRASH_WIN_RECORDS] 保留上局赢分排行榜记录，开始新一局")

    # 重置游戏数据
    game_data = %{
      state.game_data
      | phase: :waiting,
        round: state.game_data.round + 1,
        total_bets: 0,
        betting_players: [],
        crash_time: nil,
        crash_multiplier: nil,
        crashed: false,
        start_time: System.system_time(:millisecond),
        phase_timer: nil,
        # 保留上局的赢分排行榜记录，不清空
        # win_score_records 保持不变
    }

    # 重置玩家状态
    reset_players =
      state.players
      |> Enum.map(fn {numeric_id, player} ->
        reset_player =
          Map.merge(player, %{
            bet_amount: 0,
            cashed_out: false,
            cash_out_multiplier: nil,
            crashed: false,
            payout: 0,
            profit: 0
          })

        {numeric_id, reset_player}
      end)
      |> Enum.into(%{})

    # 启动游戏tick
    schedule_game_tick()

    updated_state = %{state | game_data: game_data, players: reset_players}

    # 启动等待阶段
    updated_state
    |> start_waiting_phase()
  end

  # ==================== 2. 玩家管理 ====================

  @doc """
  玩家加入房间
  """
  @impl true
  def on_player_joined(state, player) do
    Logger.info("🎰 [CRASH_PLAYER_JOIN] 玩家加入百人场 - ID: #{player.numeric_id}")

    updated_state =
      state
      |> add_player_to_state(player)
      |> send_initial_data_to_player(player)
      |> broadcast_player_count_change()

    # 如果是真实玩家加入，触发机器人管理
    if not player.is_robot do
      real_player_count = count_real_players(updated_state)
      config = updated_state.game_data.config
      CrashAI.schedule_delayed_robot_join(self(), real_player_count, config)
    end

    updated_state |> check_game_start()
  end

  @doc """
  玩家离开房间
  """
  @impl true
  def on_player_left(state, player) do
    Logger.info("🎰 [CRASH_PLAYER_LEAVE] 玩家离开百人场 - ID: #{player.numeric_id}")

    state
    |> remove_player_from_state(player)
    |> broadcast_player_count_change()
  end

  @doc """
  玩家重连
  """
  @impl true
  def on_player_rejoined(state, player) do
    Logger.info("🎰 [CRASH_PLAYER_REJOIN] 玩家重连 - ID: #{player.numeric_id}")

    # 发送完整的重连数据，不影响游戏流程
    send_complete_reconnect_data(state, player)

    state
  end

  # ==================== 3. 消息处理 ====================

  @doc """
  处理游戏消息
  """
  @impl true
  def handle_game_message(state, player, message) do
    client_protocols = CrashMessageBuilder.client_protocols()

    case message do
      # 处理MainID=4的退出房间协议
      %{"mainId" => 4, "subId" => 40} ->
        data = message["data"] || %{}
        handle_exit_room_protocol(state, player, data)

      %{"mainId" => 5, "subId" => subId} when subId == client_protocols.place_bet ->
        # 玩家下注
        handle_place_bet(state, player, message)

      %{"mainId" => 5, "subId" => subId} when subId == client_protocols.cash_out ->
        # 玩家下车
        handle_cash_out(state, player, message)

      %{"mainId" => 5, "subId" => subId} when subId == client_protocols.player_list_request ->
        # 玩家列表请求
        handle_player_list_request(state, player, message)

      _ ->
        Logger.info("🎰 [CRASH_UNKNOWN_MESSAGE] 未知消息 - #{inspect(message)}")
        state
    end
  end

  # 处理阶段超时
  def handle_info(:phase_timeout, state) do
    Logger.info(
      "🎰 [CRASH_PHASE_TIMEOUT] 阶段超时触发 - 阶段: #{state.game_data.phase}, 轮次: #{state.game_data.round}"
    )

    new_state =
      case state.game_data.phase do
        :waiting ->
          Logger.info("🎰 [CRASH_PHASE_TIMEOUT] 等待阶段超时，转换到下注阶段")
          start_betting_phase(state)

        :betting ->
          Logger.info("🎰 [CRASH_PHASE_TIMEOUT] 下注阶段超时，转换到飞行阶段")
          start_flying_phase(state)

        :settling ->
          Logger.info("🎰 [CRASH_PHASE_TIMEOUT] 结算阶段超时，开始新一轮")
          start_new_round(state)

        _ ->
          Logger.warning("🎰 [CRASH_PHASE_TIMEOUT] 未知阶段超时 - 阶段: #{state.game_data.phase}")
          state
      end

    {:noreply, new_state}
  end

  # 处理机器人相关消息
  def handle_info({:delayed_add_robots, target_count}, state) do
    # 延迟添加机器人
    current_robot_count = CrashAI.count_robots(state)
    robots_to_add = max(0, target_count - current_robot_count)

    if robots_to_add > 0 do
      Logger.info("🤖 [CRASH_ROOM] 延迟添加 #{robots_to_add} 个机器人")
      updated_state = CrashAI.add_robots_to_room(state, robots_to_add)
      # 广播玩家数量变化
      final_state = broadcast_player_count_change(updated_state)
      {:noreply, final_state}
    else
      {:noreply, state}
    end
  end

  def handle_info({:robot_bet, robot_id}, state) do
    # 机器人下注
    updated_state = CrashAI.execute_robot_bet(state, robot_id)

    # 如果机器人成功下注，发送协议通知
    case Map.get(updated_state.players, robot_id) do
      nil ->
        {:noreply, updated_state}

      robot ->
        if Map.get(robot, :bet_amount, 0) > 0 and state.game_data.phase == :betting do
          # 机器人使用统一的下注处理函数（机器人不使用自动下车）
          bet_amount = Map.get(robot, :bet_amount, 0)
          final_state = process_place_bet(updated_state, robot, bet_amount, nil)

          Logger.info(
            "🤖 [CRASH_ROOM] 机器人下注成功: #{Map.get(robot, :nickname, "机器人")}, 金额: #{bet_amount}"
          )

          {:noreply, final_state}
        else
          {:noreply, updated_state}
        end
    end
  end

  def handle_info({:immediate_add_robots, count}, state) do
    # 立即添加机器人（50%）
    current_robot_count = CrashAI.count_robots(state)
    max_robots = Map.get(state.game_data.config, :max_robots, 60)
    robots_to_add = min(count, max_robots - current_robot_count)

    if robots_to_add > 0 do
      Logger.info("🤖 [CRASH_ROOM] 立即添加 #{robots_to_add} 个机器人")
      updated_state = CrashAI.add_robots_to_room(state, robots_to_add)
      # 广播玩家数量变化
      final_state = broadcast_player_count_change(updated_state)
      {:noreply, final_state}
    else
      {:noreply, state}
    end
  end

  def handle_info({:gradual_add_robots, batch_size, remaining_count}, state) do
    # 分批添加机器人
    current_robot_count = CrashAI.count_robots(state)
    max_robots = Map.get(state.game_data.config, :max_robots, 60)
    robots_to_add = min(batch_size, max_robots - current_robot_count)

    updated_state =
      if robots_to_add > 0 do
        Logger.info("🤖 [CRASH_ROOM] 分批添加 #{robots_to_add} 个机器人")
        temp_state = CrashAI.add_robots_to_room(state, robots_to_add)
        # 广播玩家数量变化
        broadcast_player_count_change(temp_state)
      else
        state
      end

    # 继续安排剩余机器人的添加
    if remaining_count > 0 and robots_to_add > 0 do
      CrashAI.schedule_gradual_robot_addition(self(), remaining_count)
    end

    {:noreply, updated_state}
  end

  # 处理机器人下班消息
  def handle_info({:robot_end_work, robot_id}, state) do
    Logger.info("🤖 [CRASH_ROOM] 机器人下班: #{robot_id}")

    robot_manager = state.game_data.robot_manager

    # 处理机器人下班
    case CrashRobotManager.robot_end_work(robot_manager, robot_id) do
      {:ok, :robot_end_work, ^robot_id} ->
        # 从房间移除机器人
        updated_state = remove_player_from_state(state, robot_id)

        # 广播机器人离开消息
        leave_message = %{
          "mainId" => 4,
          "subId" => 14,
          "data" => %{
            "player_id" => robot_id
          }
        }

        broadcast_to_all_except(updated_state, leave_message, robot_id)

        # 广播玩家数量变化
        final_state = broadcast_player_count_change(updated_state)
        {:noreply, final_state}

      {:error, reason} ->
        Logger.warning("🤖 [CRASH_ROOM] 机器人下班失败: #{robot_id}, 原因: #{reason}")
        {:noreply, state}
    end
  end

  # 处理机器人下班消息（带原因）
  def handle_info({:robot_end_work, robot_id, reason}, state) do
    Logger.info("🤖 [CRASH_ROOM] 机器人下班: #{robot_id}, 原因: #{reason}")

    robot_manager = state.game_data.robot_manager

    # 处理机器人下班
    case CrashRobotManager.robot_end_work(robot_manager, robot_id) do
      {:ok, :robot_end_work, ^robot_id} ->
        # 从房间移除机器人
        updated_state = remove_player_from_state(state, robot_id)

        # 广播机器人离开消息
        leave_message = %{
          "mainId" => 4,
          "subId" => 14,
          "data" => %{
            "player_id" => robot_id,
            "reason" => to_string(reason)
          }
        }

        broadcast_to_all_except(updated_state, leave_message, robot_id)

        # 广播玩家数量变化
        final_state = broadcast_player_count_change(updated_state)
        {:noreply, final_state}

      {:error, error_reason} ->
        Logger.warning("🤖 [CRASH_ROOM] 机器人下班失败: #{robot_id}, 原因: #{error_reason}")
        {:noreply, state}
    end
  end

  # 处理机器人上班消息
  def handle_info({:robot_start_work, robot_id}, state) do
    Logger.info("🤖 [CRASH_ROOM] 机器人上班: #{robot_id}")

    robot_manager = state.game_data.robot_manager

    # 处理机器人上班
    case CrashRobotManager.robot_start_work(robot_manager, robot_id) do
      {:ok, :robot_start_work, ^robot_id} ->
        # 创建新的机器人并加入房间
        managed_robot = CrashAI.create_managed_robot_player(robot_id, robot_manager)
        updated_state = add_player_to_state(state, managed_robot)

        # 广播机器人加入消息
        join_message = %{
          "mainId" => 4,
          "subId" => 4,
          "data" => %{
            "player" => build_player_info(managed_robot)
          }
        }

        broadcast_to_all_except(updated_state, join_message, robot_id)

        # 广播玩家数量变化
        final_state = broadcast_player_count_change(updated_state)
        {:noreply, final_state}

      {:error, reason} ->
        Logger.warning("🤖 [CRASH_ROOM] 机器人上班失败: #{robot_id}, 原因: #{reason}")
        {:noreply, state}
    end
  end

  # 处理退出房间协议
  defp handle_exit_room_protocol(state, player, _data) do
    Logger.info("🎰 [CRASH_EXIT_ROOM] 玩家请求退出房间 - ID: #{player.numeric_id}")

    # 移除玩家
    new_state = remove_player_from_state(state, player)

    # 广播玩家离开
    leave_message = %{
      "mainId" => 4,
      "subId" => 14,
      "data" => %{
        "player_id" => player.numeric_id
      }
    }

    broadcast_to_all_except(new_state, leave_message, player.numeric_id)

    new_state
  end

  # ==================== 4. 游戏阶段处理 ====================

  # ==================== 私有函数 ====================

  defp build_initial_game_state(state, config) do
    # 初始化机器人管理器
    robot_manager = CrashRobotManager.init_robot_manager(self(), config)

    # 获取库存控制配置
    stock_config = CrashConfig.get_stock_control_config(config)

    game_data = %{
      phase: :waiting,
      round: 0,
      config: config,
      total_bets: 0,
      betting_players: [],
      crash_time: nil,
      crash_multiplier: nil,
      crashed: false,
      start_time: nil,
      betting_time_left: Map.get(config, :betting_time, 15),
      flying_start_time: nil,
      multiplier_history: [],
      # 赢分排行榜数据存储（独立于玩家数据，不会被清除）
      win_score_records: %{},
      # 添加阶段定时器管理
      phase_timer: nil,
      # 添加机器人管理器
      robot_manager: robot_manager,
      # 添加库存控制配置
      stock_config: stock_config
    }

    state
    |> Map.put(:game_data, game_data)
    |> Map.put(:current_stock, stock_config.initial_stock)  # 初始化库存分
    |> Map.put(:player_histories, %{})                      # 初始化玩家历史记录
    |> Map.put(:system_recent_games, [])                    # 初始化系统最近20局输赢记录
  end

  defp start_game_loop(state) do
    # 百人场游戏自动开始，无需等待玩家数量
    Logger.info("🎰 [CRASH_GAME_LOOP] 启动游戏循环")

    # 直接开始第一轮游戏
    on_game_start(state)
  end

  defp start_waiting_phase(state) do
    config = state.game_data.config
    free_time = Map.get(config, :free_time, 5)
    current_time = System.system_time(:millisecond)

    Logger.info("🎰 [CRASH_WAITING_PHASE] 开始等待阶段 - 时间: #{free_time}秒")

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 设置新的阶段定时器
    timer = Process.send_after(self(), :phase_timeout, free_time * 1000)

    # 更新游戏状态
    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :waiting,
        # 记录等待开始时间
        waiting_start_time: current_time,
        phase_timer: timer
      })

    updated_state = %{state | game_data: updated_game_data}

    # 发送游戏空闲消息
    message = CrashMessageBuilder.build_game_free(updated_state)
    broadcast_to_all(updated_state, message)

    updated_state
  end

  defp start_betting_phase(state) do
    config = state.game_data.config
    betting_time = Map.get(config, :betting_time, 15)

    Logger.info("🎰 [CRASH_BETTING_PHASE] 开始下注阶段 - 时间: #{betting_time}秒")

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 设置新的阶段定时器
    timer = Process.send_after(self(), :phase_timeout, betting_time * 1000)

    # 更新游戏状态
    current_time = System.system_time(:millisecond)

    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :betting,
        betting_time_left: betting_time,
        # 记录下注开始时间
        betting_start_time: current_time,
        phase_timer: timer
      })

    updated_state = %{state | game_data: updated_game_data}

    # 为机器人安排下注时间
    CrashAI.schedule_robot_bets(updated_state, self())

    # 发送游戏开始消息
    message = CrashMessageBuilder.build_game_start(updated_state)
    broadcast_to_all(updated_state, message)

    updated_state
  end

  defp start_flying_phase(state) do
    Logger.info("🚀 [CRASH_FLYING_PHASE] 开始飞行阶段")

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 获取下注玩家信息
    betting_players = get_betting_players(state)

    # 计算爆炸点（倍数和时间）
    {crash_multiplier, crash_time} = CrashLogic.calculate_crash_point(state, betting_players)

    # 更新游戏状态（飞行阶段不设置固定定时器，由爆炸逻辑控制）
    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :flying,
        flying_start_time: System.system_time(:millisecond),
        crash_time: crash_time,
        crash_multiplier: crash_multiplier,
        phase_timer: nil
      })

    updated_state = %{state | game_data: updated_game_data}

    # 发送飞行开始消息
    fly_message = CrashMessageBuilder.build_fly_start(updated_state)
    broadcast_to_all(updated_state, fly_message)

    # 启动游戏tick来处理飞行阶段
    schedule_game_tick()

    updated_state
  end

  defp handle_betting_tick(state) do
    # 处理机器人下注行为
    state_with_robot_bets = CrashAI.handle_robot_betting_phase(state)

    # 更新下注倒计时
    time_left = Map.get(state_with_robot_bets.game_data, :betting_time_left, 0) - 1

    if time_left <= 0 do
      # 下注时间结束，进入飞行阶段
      start_flying_phase(state_with_robot_bets)
    else
      updated_game_data = Map.put(state_with_robot_bets.game_data, :betting_time_left, time_left)
      %{state_with_robot_bets | game_data: updated_game_data}
    end
  end

  defp handle_flying_tick(state) do
    current_time = System.system_time(:millisecond)
    flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
    elapsed_time = current_time - flying_start_time

    # 检查是否应该爆炸
    if CrashLogic.should_crash?(state, elapsed_time) do
      handle_crash(state, elapsed_time)
    else
      # 获取当前倍数
      config = state.game_data.config
      current_multiplier = CrashLogic.get_current_multiplier(elapsed_time, config)

      # 处理机器人下车行为
      {state_after_ai, robots_to_cash_out} =
        CrashAI.handle_robot_flying_phase(state, current_multiplier, elapsed_time)

      # 统一处理机器人下车（使用相同的process_cash_out函数）
      state_with_robot_cashouts =
        Enum.reduce(robots_to_cash_out, state_after_ai, fn robot, acc_state ->
          Logger.info(
            "🤖 [CRASH_ROOM] 机器人准备下车: #{get_unified_player_name(robot)}, 倍数: #{current_multiplier / 100}x"
          )

          process_cash_out(acc_state, robot, elapsed_time)
        end)

      # 检查并处理自动下车的真实玩家
      auto_cash_out_players = CrashLogic.get_auto_cash_out_players(state_with_robot_cashouts, current_multiplier)

      state_with_auto_cashouts =
        Enum.reduce(auto_cash_out_players, state_with_robot_cashouts, fn player, acc_state ->
          Logger.info(
            "🚗 [CRASH_AUTO_CASH_OUT] 玩家自动下车: #{get_unified_player_name(player)}, 设置倍率: #{Map.get(player, :auto_cash_out, 0) / 100}x, 当前倍率: #{current_multiplier / 100}x"
          )

          process_cash_out(acc_state, player, elapsed_time)
        end)

      # 只在倍数有显著变化时才发送更新（减少协议发送频率）
      last_sent_multiplier = Map.get(state.game_data, :last_sent_multiplier, 100)
      multiplier_diff = abs(current_multiplier - last_sent_multiplier)

      # 每当倍数变化超过5个点（0.05x）或每秒发送一次更新
      last_update_time = Map.get(state.game_data, :last_update_time, 0)
      should_send_update = multiplier_diff >= 5 or current_time - last_update_time >= 500

      updated_state =
        if should_send_update do
          # 发送倍数更新消息（使用时间通知协议而不是飞行开始协议）
          time_data = %{
            "multiplier" => current_multiplier / 100.0,
            "time" => elapsed_time / 1000.0,
            "timestamp" => current_time
          }

          # 使用时间通知协议（1008）发送倍数更新
          message =
            CrashMessageBuilder.build_message(
              CrashMessageBuilder.protocol_ids().time_notify,
              time_data
            )

          broadcast_to_all(state_with_auto_cashouts, message)

          # 更新最后发送的倍数和时间
          updated_game_data =
            Map.merge(state_with_auto_cashouts.game_data, %{
              last_sent_multiplier: current_multiplier,
              last_update_time: current_time
            })

          %{state_with_auto_cashouts | game_data: updated_game_data}
        else
          state_with_auto_cashouts
        end

      updated_state
    end
  end

  defp handle_settling_tick(state) do
    # 结算阶段处理 - 由定时器控制，这里不需要额外处理
    state
  end

  defp handle_crash(state, crash_time) do
    Logger.info("💥 [CRASH_EXPLODE] 游戏爆炸 - 爆炸时间: #{crash_time}ms")

    # 处理爆炸逻辑
    {:ok, updated_state} = CrashLogic.handle_crash(state, crash_time)

    # 修复玩家结果数据格式
    player_results =
      Enum.map(updated_state.players, fn {numeric_id, player} ->
        # 使用统一的余额获取函数，遵循DRY原则
        player_coin = get_unified_player_balance(player)

        %{
          # 修正字段名
          "playerid" => player.numeric_id,
          "bet_amount" => Map.get(player, :bet_amount, 0),
          "cash_out_multiplier" => Map.get(player, :cash_out_multiplier, nil),
          # 修正字段名
          "win_amount" => Map.get(player, :payout, 0),
          # 直接使用获取的金币值
          "coin" => player_coin,
          # 修正字段名和逻辑
          "is_crashed" => not Map.get(player, :cashed_out, false)
        }
      end)

    # 发送游戏结束消息
    crash_multiplier = Map.get(updated_state.game_data, :crash_multiplier, 100)
    config = updated_state.game_data.config
    settling_time = Map.get(config, :settling_time, 5)

    # 使用合并后的build_game_end，包含详细数据
    opts = %{
      round_id: Map.get(updated_state.game_data, :round, 1),
      player_results: player_results
    }
    message = CrashMessageBuilder.build_game_end(crash_time, crash_multiplier, settling_time, opts)
    broadcast_to_all(updated_state, message)

    # 记录历史
    updated_game_data =
      Map.update(updated_state.game_data, :multiplier_history, [], fn history ->
        new_record = %{
          round_id: Map.get(updated_state.game_data, :round, 1),
          crash_multiplier: crash_multiplier,
          timestamp: System.system_time(:millisecond),
          player_count: map_size(updated_state.players)
        }

        # 保留最近100条，数组排序从新到旧
        [new_record | history] |> Enum.take(100)
      end)

    # 更新当局赢分排行榜记录（替换上局记录）
    current_round_win_records = build_current_round_win_records(updated_state)
    final_game_data = Map.put(updated_game_data, :win_score_records, current_round_win_records)

    Logger.info("🏆 [CRASH_WIN_RECORDS] 更新当局赢分排行榜记录 - 记录数: #{map_size(current_round_win_records)}")

    # 统一更新系统输赢记录（每局游戏结束时）
    state_with_system_record = update_system_record_on_game_end(%{updated_state | game_data: final_game_data})

    # 转换到结算阶段
    transition_to_settling(state_with_system_record)
  end

  defp transition_to_settling(state) do
    config = state.game_data.config
    settling_time = Map.get(config, :settling_time, 5)

    Logger.info("🎰 [CRASH_SETTLING_PHASE] 开始结算阶段 - 时间: #{settling_time}秒")

    # 更新机器人余额
    updated_state = update_robot_balances_after_game(state)

    # 设置结算阶段定时器
    timer = Process.send_after(self(), :phase_timeout, settling_time * 1000)

    # 更新游戏状态
    current_time = System.system_time(:millisecond)
    updated_game_data = Map.merge(updated_state.game_data, %{
      phase: :settling,
      settling_start_time: current_time,  # 记录结算开始时间
      phase_timer: timer
    })
    final_state = %{updated_state | game_data: updated_game_data}

    # 注意：不再发送额外的状态消息，因为game_end协议已经包含了结算信息
    # 避免与game_end协议重复

    final_state
  end

  # ==================== 5. 消息处理函数 ====================

  defp handle_place_bet(state, player, message) do
    # 前端发送的字段是 lBetScore，不是 betAmount
    bet_amount = get_in(message, ["data", "lBetScore"]) || 0
    # 提取自动下车倍率字段
    auto_cash_out = get_in(message, ["data", "autoCashOut"])

    # 调用统一的下注处理函数
    process_place_bet(state, player, bet_amount, auto_cash_out)
  end

  # 统一的下注处理函数，供真实玩家和机器人共同使用
  defp process_place_bet(state, player, bet_amount, auto_cash_out \\ nil) do
    case CrashLogic.place_bet(state, player, bet_amount, auto_cash_out) do
      {:ok, updated_state, updated_player} ->
        Logger.info("💰 [CRASH_PLACE_BET] 玩家下注成功 - ID: #{player.numeric_id}, 下注金额: #{bet_amount}")

        # 记录下注时间和下注前原始金额
        bet_time = System.system_time(:millisecond)
        original_balance = get_player_points(updated_state, updated_player.numeric_id)

        player_with_bet_info = updated_player
        |> Map.put(:bet_time, bet_time)
        |> Map.put(:original_balance, original_balance)  # 记录下注前金额

        # 更新状态中的玩家数据
        state_with_bet_info = %{updated_state |
          players: Map.put(updated_state.players, updated_player.numeric_id, player_with_bet_info)
        }

        # 先扣除玩家积分，然后获取扣除后的金币数量
        state_after_points = subtract_player_points(state_with_bet_info, updated_player.numeric_id, bet_amount)
        current_money = get_player_points(state_after_points, updated_player.numeric_id)

        # 更新库存分（下注时库存增加）
        final_state = on_player_bet_stock_update(state_after_points, updated_player.numeric_id, bet_amount)

        # 计算游戏统计信息
        game_stats = calculate_game_stats(final_state)

        # 构建下注成功消息
        success_message =
          CrashMessageBuilder.build_place_bet_success(
            updated_player,
            bet_amount,
            current_money,
            game_stats
          )

        # 发送下注成功消息给所有真实玩家（包括下注的玩家自己）
        broadcast_to_real_players(final_state, success_message)

        final_state

      {:error, error_code} ->
        Logger.warning(
          "❌ [CRASH_PLACE_BET_FAIL] 玩家下注失败 - ID: #{player.numeric_id}, 下注金额: #{bet_amount}, 错误: #{error_code}"
        )

        # 发送下注失败消息
        fail_message = CrashMessageBuilder.build_place_bet_fail(error_code)
        send_to_player(state, player, fail_message)

        state
    end
  end

  @doc """
  计算当前游戏轮次的统计信息
  """
  defp calculate_game_stats(state) do
    players = Map.values(state.players)

    # 计算下注总余额（所有玩家的下注金额总和）
    total_bet_amount =
      players
      |> Enum.map(&Map.get(&1, :bet_amount, 0))
      |> Enum.sum()

    # 计算下车总余额（所有已下车玩家的获得金额总和）
    total_cash_out_amount =
      players
      |> Enum.filter(&Map.get(&1, :cashed_out, false))
      |> Enum.map(&Map.get(&1, :payout, 0))
      |> Enum.sum()

    # 计算上车总人数（有下注的玩家数量）
    total_bet_players =
      players
      |> Enum.count(&(Map.get(&1, :bet_amount, 0) > 0))

    # 计算下车总人数（已下车的玩家数量）
    total_cash_out_players =
      players
      |> Enum.count(&Map.get(&1, :cashed_out, false))

    %{
      total_bet_amount: total_bet_amount,
      total_cash_out_amount: total_cash_out_amount,
      total_bet_players: total_bet_players,
      total_cash_out_players: total_cash_out_players
    }
  end

  defp handle_cash_out(state, player, _message) do
    current_time = System.system_time(:millisecond)
    flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
    elapsed_time = current_time - flying_start_time

    # 调用统一的下车处理函数
    process_cash_out(state, player, elapsed_time)
  end

  # 统一的下车处理函数，供真实玩家和机器人共同使用
  defp process_cash_out(state, player, elapsed_time) do
    case CrashLogic.cash_out(state, player, elapsed_time) do
      {:ok, updated_state, updated_player, payout_info} ->
        Logger.info(
          "🚗 [CRASH_CASH_OUT] 玩家下车成功 - ID: #{player.numeric_id}, 下车时间: #{elapsed_time}ms, 下车倍数: #{updated_player.cash_out_multiplier / 100}x, 下车金额: #{payout_info.gross_payout}, 净收益: #{payout_info.net_payout}, 利润: #{payout_info.profit}"
        )

        # 先增加玩家积分，然后获取更新后的金币数量
        state_after_points =
          add_player_points(updated_state, updated_player.numeric_id, payout_info.net_payout)

        current_money = get_player_points(state_after_points, updated_player.numeric_id)

        # 更新库存分和玩家历史记录（下车时库存减少，记录历史）
        final_state = on_player_cash_out_update(
          state_after_points,
          updated_player.numeric_id,
          Map.get(updated_player, :bet_amount, 0),
          Map.get(updated_player, :cash_out_multiplier, 100),
          payout_info.gross_payout,
          payout_info.profit
        )

        # 计算游戏统计信息
        game_stats = calculate_game_stats(final_state)

        # 发送下车成功消息
        cash_out_message =
          CrashMessageBuilder.build_cash_out_success(
            updated_player,
            # 下车时间（相对于飞行开始的毫秒数）
            elapsed_time,
            # 下车倍数（实际倍数，如2.5）
            updated_player.cash_out_multiplier,
            # 下车所得金额
            payout_info.gross_payout,
            # 玩家剩余金币（已包含奖励）
            current_money,
            # 游戏统计信息
            game_stats
          )

        Logger.info("🚗 [CRASH_CASH_OUT] 构建的下车消息: #{inspect(cash_out_message)}")

        # 向所有真实玩家广播下车消息（包括下车的玩家自己）
        Logger.info(
          "🚗 [CRASH_CASH_OUT] 准备广播下车成功消息 - 玩家ID: #{updated_player.numeric_id}, 倍数: #{updated_player.cash_out_multiplier / 100}x"
        )

        broadcast_to_real_players(final_state, cash_out_message)
        Logger.info("🚗 [CRASH_CASH_OUT] 下车成功消息已广播")

        # 更新赢分排行榜（只记录赢钱的玩家）
        final_state_with_records = update_win_score_records(final_state, updated_player, payout_info)

        final_state_with_records

      {:error, error_code} ->
        Logger.warning(
          "❌ [CRASH_CASH_OUT_FAIL] 玩家下车失败 - ID: #{player.numeric_id}, 错误: #{error_code}, 当前时间: #{elapsed_time}ms"
        )

        # 发送下车失败消息（使用下注失败协议，错误消息不同）
        fail_message = CrashMessageBuilder.build_place_bet_fail(error_code)
        send_to_player(state, player, fail_message)

        state
    end
  end

  # ==================== 6. 玩家列表处理 ====================

  defp handle_player_list_request(state, player, message) do
    data = message["data"] || %{}
    page = Map.get(data, "page", 0)

    Logger.info("🎰 [CRASH_PLAYER_LIST_REQUEST] 玩家请求列表 - ID: #{player.numeric_id}, 页码: #{page}")

    send_player_list(state, player.numeric_id, page)
    state
  end

  defp send_player_list(state, player_id, page \\ 0) do
    # 构建玩家列表数据（支持分页）
    player_list = build_player_list(state, page)

    # 构建下注排行榜数据（前端桌面显示用）
    betrank_data = build_betrank_data(state)

    # 构建赢分排行榜数据
    winscorerank_data = build_winscorerank_data(state)

    # 构建响应消息
    message = CrashMessageBuilder.build_player_list_response(
      state,
      player_list,
      betrank_data,
      winscorerank_data
    )

    case Map.get(state.players, player_id) do
      nil ->
        Logger.error("🎰 [CRASH_PLAYER_LIST] 玩家不存在 - ID: #{player_id}")
      player ->
        send_to_player(state, player, message)
    end

    Logger.info(
      "📤 [CRASH_SEND_PLAYER_LIST] 发送玩家列表 - 请求者: #{player_id}, 玩家数量: #{map_size(state.players)}, 页码: #{page}"
    )
  end

  # 构建玩家列表数据（支持分页）
  defp build_player_list(state, page \\ 0) do
    players_per_page = 12  # 每页显示12个玩家
    start_index = page * players_per_page

    # 获取所有玩家并按加入时间排序
    all_players =
      state.players
      |> Map.values()
      |> Enum.sort_by(fn player ->
        Map.get(player, :joined_at, System.system_time(:millisecond))
      end)

    # 分页处理
    page_players =
      all_players
      |> Enum.drop(start_index)
      |> Enum.take(players_per_page)

    # 转换为前端期望的格式（对象格式，以索引为键）
    page_players
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {player, index}, acc ->
      player_data = %{
        "playerid" => player.numeric_id,
        "name" => get_unified_player_name(player),
        "coin" => get_player_balance(player),
        "headid" => get_player_avatar_id(player),
        "wxheadurl" => get_player_avatar_url(player),
        "bet_amount" => Map.get(player, :bet_amount, 0)
      }
      Map.put(acc, Integer.to_string(index), player_data)
    end)
  end

  # 构建下注排行榜数据（前端桌面显示用，显示前6名）
  defp build_betrank_data(state) do
    state.players
    |> Map.values()
    |> Enum.filter(fn player -> Map.get(player, :bet_amount, 0) > 0 end)
    |> Enum.sort_by(fn player -> Map.get(player, :bet_amount, 0) end, :desc)
    |> Enum.take(6)
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {player, rank}, acc ->
      player_data = %{
        "playerid" => player.numeric_id,
        "name" => get_unified_player_name(player),
        "coin" => get_player_balance(player),
        "headid" => get_player_avatar_id(player),
        "wxheadurl" => get_player_avatar_url(player),
        "betnum" => Map.get(player, :bet_amount, 0)
      }
      Map.put(acc, Integer.to_string(rank), player_data)
    end)
  end

  # 构建当局赢分排行榜记录（游戏结束时调用）
  defp build_current_round_win_records(state) do
    state.players
    |> Map.values()
    |> Enum.filter(fn player ->
      # 只记录赢钱的玩家（profit > 0）
      profit = Map.get(player, :payout, 0) - Map.get(player, :bet_amount, 0)
      profit > 0
    end)
    |> Enum.sort_by(fn player ->
      # 按盈利金额排序
      profit = Map.get(player, :payout, 0) - Map.get(player, :bet_amount, 0)
      profit
    end, :desc)
    |> Enum.take(20)  # 最多保留20条记录
    |> Enum.reduce(%{}, fn player, acc ->
      player_id = player.numeric_id
      profit = Map.get(player, :payout, 0) - Map.get(player, :bet_amount, 0)

      new_record = %{
        playerid: player_id,
        name: get_unified_player_name(player),
        headid: get_player_avatar_id(player),
        wxheadurl: get_player_avatar_url(player),
        bet_time: Map.get(player, :cash_out_time, System.system_time(:millisecond)),  # 改为下车时间
        total_bet_amount: Map.get(player, :bet_amount, 0),
        total_win_amount: Map.get(player, :payout, 0),
        multiplier: Map.get(player, :cash_out_multiplier, 100),
        winscore: profit,
        updated_at: System.system_time(:millisecond),
        round: state.game_data.round
      }

      Map.put(acc, player_id, new_record)
    end)
  end

  # 更新当局赢分排行榜记录（玩家下车时调用，但不再使用）
  defp update_win_score_records(state, _player, _payout_info) do
    # 现在改为在游戏结束时统一更新，这个函数不再使用
    state
  end

  # 构建赢分排行榜数据（从独立存储中获取）
  defp build_winscorerank_data(state) do
    win_records = Map.get(state.game_data, :win_score_records, %{})

    win_records
    |> Map.values()
    |> Enum.sort_by(fn record -> record.winscore end, :desc)
    |> Enum.take(10)
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {record, rank}, acc ->
      player_data = %{
        "playerid" => record.playerid,
        "name" => record.name,
        "headid" => record.headid,
        "wxheadurl" => record.wxheadurl,
        "bet_time" => record.bet_time,
        "total_bet_amount" => record.total_bet_amount,
        "total_win_amount" => record.total_win_amount,
        "multiplier" => record.multiplier
        # 移除 winscore 和 is_robot 字段，不让玩家知道是机器人
      }
      Map.put(acc, Integer.to_string(rank), player_data)
    end)
  end

  @doc """
  统一获取玩家余额的函数
  支持机器人和真实玩家，遵循DRY原则和一致性原则
  """
  defp get_unified_player_balance(player) do
    case player do
      # PlayerData结构体（新格式）
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        Cypridina.Teen.GameSystem.PlayerData.get_points(p)

      # 兼容旧格式：检查是否为机器人
      _ ->
        if Map.get(player, :is_robot, false) do
          # 机器人使用user.points字段（根据PlayerDataBuilder的实现）
          Map.get(player.user, :points, 1_000_000)
        else
          # 从账户系统获取真实玩家积分
          case Cypridina.Accounts.get_user_points(player.user_id) do
            points when is_integer(points) -> points
            _ -> 0
          end
        end
    end
  end

  # 保持向后兼容的别名
  defp get_player_balance(player), do: get_unified_player_balance(player)

  # 获取玩家头像ID
  defp get_player_avatar_id(player) do
    case player do
      # PlayerData结构体（新格式）- 统一从user字段获取
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        Map.get(p.user, :avatar_id, 1)

      # 兼容旧格式
      _ ->
        if Map.get(player, :is_robot, false) do
          # 机器人使用avatar_id字段
          Map.get(player, :avatar_id, 1)
        else
          # 真实玩家
          Map.get(player, :avatar_id, 1)
        end
    end
  end

  # 获取玩家头像URL
  defp get_player_avatar_url(player) do
    case player do
      # PlayerData结构体（新格式）- 统一从user字段获取
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        Map.get(p.user, :avatar_url, "")

      # 兼容旧格式
      _ ->
        if Map.get(player, :is_robot, false) do
          # 机器人通常不使用头像URL，返回空字符串
          ""
        else
          # 真实玩家的头像URL
          Map.get(player, :avatar_url, "")
        end
    end
  end

  # ==================== 9. 辅助函数 ====================

  defp add_player_to_state(state, player) do
    # 初始化玩家游戏数据
    game_player =
      Map.merge(player, %{
        bet_amount: 0,
        cashed_out: false,
        cash_out_multiplier: nil,
        crashed: false,
        payout: 0,
        profit: 0,
        joined_at: System.system_time(:millisecond)
      })

    # 使用 numeric_id 作为键，与 RoomBase 保持一致
    %{state | players: Map.put(state.players, player.numeric_id, game_player)}
  end

  defp remove_player_from_state(state, player) do
    # 使用 numeric_id 作为键，与 RoomBase 保持一致
    %{state | players: Map.delete(state.players, player.numeric_id)}
  end

  defp send_initial_data_to_player(state, player) do
    player_id = player.numeric_id

    # 1. 发送房间信息协议 (mainId=4, subId=2) - 必须发送
    send_room_info_protocol(state, player_id)

    # 2. 广播玩家加入 (mainId=4, subId=12) - 必须发送，在协议2后面
    broadcast_player_join_protocol(state, player)

    # 3. 发送游戏配置
    config_message = CrashMessageBuilder.build_game_config(state)
    send_to_player(state, player, config_message)

    # 4. 发送游戏状态
    state_message = CrashMessageBuilder.build_game_state(state)
    send_to_player(state, player, state_message)

    # 5. 发送游戏记录（包含历史记录）
    history = Map.get(state.game_data, :multiplier_history, [])

    if length(history) > 0 do
      history_message = CrashMessageBuilder.build_game_record(history)
      send_to_player(state, player, history_message)
    end

    # 6. 发送断线重连协议 (mainId=5, subId=0) - 必须发送
    send_reconnect_data_to_player(state, player)

    state
  end

  defp broadcast_player_count_change(state) do
    total_players = map_size(state.players)
    Logger.info("🎰 [CRASH_BROADCAST_PLAYER_COUNT] 广播玩家数量变化 - 总数: #{total_players}")

    message = CrashMessageBuilder.build_player_count_change(total_players)
    broadcast_to_room(state, message)

    state
  end

  defp check_game_start(state) do
    player_count = map_size(state.players)
    min_players = Map.get(state.game_data.config, :min_players, 1)

    # 如果玩家不足，添加机器人
    state_with_robots =
      if player_count < 5 do
        temp_state = CrashAI.add_robots_to_room(state, 5 - player_count)
        # 广播玩家数量变化
        broadcast_player_count_change(temp_state)
      else
        state
      end

    updated_player_count = map_size(state_with_robots.players)

    if state_with_robots.room_state == @room_states.waiting and
         updated_player_count >= min_players do
      Logger.info(
        "🎰 [CRASH_ROOM] 房间满足开始条件 - 房间: #{state_with_robots.id}, 玩家数: #{updated_player_count}"
      )

      start_game(state_with_robots)
    else
      state_with_robots
    end
  end

  defp broadcast_to_all(state, message) do
    Enum.each(state.players, fn {_numeric_id, player} ->
      send_to_player(state, player, message)
    end)
  end

  defp broadcast_to_all_except(state, message, exclude_numeric_id) do
    Enum.each(state.players, fn {numeric_id, player} ->
      if numeric_id != exclude_numeric_id do
        send_to_player(state, player, message)
      end
    end)
  end

  # 注意：send_to_player 函数已经由 RoomBase 提供

  defp schedule_game_tick() do
    # 每100ms一次tick
    Process.send_after(self(), :game_tick, 100)
  end

  defp get_betting_players(state) do
    state.players
    |> Enum.filter(fn {_user_id, player} ->
      Map.get(player, :bet_amount, 0) > 0
    end)
    |> Enum.map(fn {_user_id, player} -> player end)
  end

  # ==================== 7. GenServer消息处理 ====================

  # 处理开始新一轮消息
  def handle_info(:start_new_round, state) do
    Logger.info("🎰 [CRASH_START_NEW_ROUND] 开始新一轮游戏")
    new_state = start_new_round(state)
    {:noreply, new_state}
  end

  # 处理游戏tick
  def handle_info(:game_tick, state) do
    # 处理当前阶段的tick逻辑
    new_state = handle_game_tick(state)
    {:noreply, new_state}
  end

  # 游戏tick处理逻辑
  @impl true
  def handle_game_tick(state) do
    # 处理当前阶段的tick
    new_state =
      case state.game_data.phase do
        :betting ->
          handle_betting_tick(state)

        :flying ->
          handle_flying_tick(state)

        :settling ->
          handle_settling_tick(state)

        _ ->
          state
      end

    # 继续调度下一次tick（只有在飞行阶段需要持续tick）
    if new_state.game_data.phase == :flying do
      schedule_game_tick()
    end

    new_state
  end

  # 开始新一轮游戏
  defp start_new_round(state) do
    # 记录上一轮的机器人行为统计
    log_robot_behavior(state)

    # 在新一轮开始前进行智能机器人管理
    state_with_managed_robots = CrashAI.smart_robot_management(state)

    # 如果机器人数量发生变化，广播玩家数量变化
    final_state = if map_size(state.players) != map_size(state_with_managed_robots.players) do
      broadcast_player_count_change(state_with_managed_robots)
    else
      state_with_managed_robots
    end

    # 百人场游戏自动循环，直接开始新一轮
    on_game_start(final_state)
  end

  # ==================== 8. 协议处理函数 ====================

  # 发送房间信息协议 (mainId=4, subId=2)
  defp send_room_info_protocol(state, player_id) do
    room_info = %{
      "mainId" => 4,
      "subId" => 2,
      "data" => %{
        "room_id" => state.id,
        "game_id" => state.game_id,
        "server_id" => state.config[:server_id] || 2301,
        "max_players" => state.max_players,
        "current_players" => map_size(state.players),
        "room_state" => state.room_state,
        "game_state" => Map.get(state.game_data, :phase, :waiting),
        "round" => Map.get(state.game_data, :round, 0)
      }
    }

    case Map.get(state.players, player_id) do
      nil ->
        Logger.error("🎰 [CRASH_ROOM_INFO] 玩家不存在 - ID: #{player_id}")

      player ->
        send_to_player(state, player, room_info)
        Logger.info("🎰 [CRASH_ROOM_INFO] 发送房间信息 - ID: #{player_id}")
    end
  end

  # 发送完整的重连数据给玩家
  defp send_complete_reconnect_data(state, player) do
    player_id = player.numeric_id

    # 1. 发送房间信息协议 (mainId=4, subId=2)
    send_room_info_protocol(state, player_id)

    # 2. 发送游戏配置协议 (mainId=5, subId=1002)
    config_message = CrashMessageBuilder.build_game_config(state)
    send_to_player(state, player, config_message)

    # 3. 发送当前游戏状态协议 (mainId=5, subId=1001)
    state_message = CrashMessageBuilder.build_game_state(state)
    send_to_player(state, player, state_message)

    # 4. 发送游戏记录协议 (mainId=5, subId=1003)
    history = Map.get(state.game_data, :multiplier_history, [])

    if length(history) > 0 do
      history_message = CrashMessageBuilder.build_game_record(history)
      send_to_player(state, player, history_message)
    end

    # 5. 发送断线重连协议 (mainId=5, subId=0) - 包含完整状态信息和时间戳
    current_time = System.system_time(:millisecond)

    reconnect_data = %{
      "mainId" => 5,
      "subId" => 0,
      "data" => %{
        "room_id" => state.id,
        "player_id" => player_id,
        "game_state" => Map.get(state.game_data, :phase, :waiting),
        "round" => Map.get(state.game_data, :round, 0),
        "time_left" => get_time_left(state),
        "current_multiplier" => get_current_multiplier(state),
        "total_players" => map_size(state.players),
        "total_bets" => get_total_bets(state),
        "player_bet" => get_player_bet(state, player_id),
        "timestamp" => current_time,
        # 当前阶段开始时间戳
        "phase_start_time" => get_current_phase_start_time(state),
        # 添加配置信息，前端可以据此计算总时长
        "phase_durations" => %{
          "free_time" => Map.get(state.game_data.config, :free_time, 5),
          "betting_time" => Map.get(state.game_data.config, :betting_time, 15),
          "settling_time" => Map.get(state.game_data.config, :settling_time, 5)
        }
      }
    }

    send_to_player(state, player, reconnect_data)
    Logger.info("🎰 [CRASH_RECONNECT] 发送完整重连数据 - ID: #{player_id}")

    state
  end

  # 发送重连数据给玩家（简化版本，保持向后兼容）
  defp send_reconnect_data_to_player(state, player) do
    send_complete_reconnect_data(state, player)
  end

  # 广播玩家加入协议 (mainId=4, subId=12)
  defp broadcast_player_join_protocol(state, player) do
    # 使用统一的余额获取函数，遵循DRY原则
    player_points = get_unified_player_balance(player)

    join_message = %{
      "mainId" => 4,
      "subId" => 12,
      "data" => %{
        "player_id" => player.numeric_id,
        "nickname" => get_player_nickname(player),
        "avatar" => get_player_avatar(player),
        "points" => player_points,
        "is_robot" => player.is_robot
      }
    }

    broadcast_to_all_except(state, join_message, player.numeric_id)
    Logger.info("🎰 [CRASH_PLAYER_JOIN_BROADCAST] 广播玩家加入 - ID: #{player.numeric_id}")
  end

  # 只向真实玩家广播消息
  defp broadcast_to_real_players(state, message) do
    real_players =
      state.players
      |> Enum.filter(fn {_numeric_id, player} ->
        not Map.get(player, :is_robot, false)
      end)

    Logger.info(
      "📡 [BROADCAST] 准备向 #{length(real_players)} 个真实玩家广播消息: mainId=#{message["mainId"]}, subId=#{message["subId"]}"
    )

    Enum.each(real_players, fn {numeric_id, player} ->
      is_robot = Map.get(player, :is_robot, false)
      user_id = Map.get(player, :user_id, "unknown")

      Logger.info(
        "📡 [BROADCAST] 发送消息给玩家 #{numeric_id} (user_id: #{user_id}, is_robot: #{is_robot})"
      )

      Logger.info("📡 [BROADCAST] 消息内容: #{inspect(message)}")

      # 直接调用Phoenix广播，添加更多调试信息
      try do
        CypridinaWeb.Endpoint.broadcast!("user:#{user_id}", "private_message", message)
        Logger.info("📡 [BROADCAST] ✅ 消息发送成功给 user:#{user_id}")
      rescue
        e ->
          Logger.error("📡 [BROADCAST] ❌ 消息发送失败给 user:#{user_id}, 错误: #{inspect(e)}")
      end
    end)

    Logger.info("📡 [BROADCAST] 消息广播完成")
  end

  # 获取统一的玩家名字 - 参考longhu游戏的实现
  defp get_unified_player_name(player) do
    case player do
      # PlayerData结构体（新格式）- 统一使用标准方法
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        Cypridina.Teen.GameSystem.PlayerData.get_display_name(p)

      # 兼容旧格式：优先检查是否为机器人
      _ ->
        if Map.get(player, :is_robot, false) do
          # 机器人使用nickname字段
          Map.get(player, :nickname, "Robot#{Map.get(player, :numeric_id, 0)}")
        else
          case player do
            # 兼容旧格式：Map格式，有user字段
            %{user: user} when is_map(user) ->
              Map.get(user, :nickname, "玩家#{Map.get(player, :numeric_id, 0)}")

            # 兼容旧格式：Map格式，直接有nickname字段
            %{nickname: nickname} when is_binary(nickname) ->
              nickname

            # 其他情况
            _ ->
              "玩家#{Map.get(player, :numeric_id, 0)}"
          end
        end
    end
  end

  # 获取统一的玩家头像 - 参考longhu游戏的实现
  defp get_unified_player_avatar(player) do
    case player do
      # 使用PlayerData结构体的标准方法
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        client_data = Cypridina.Teen.GameSystem.PlayerData.format_for_client(p)
        Map.get(client_data, "headid", 1)

      # 兼容旧格式：Map格式，有user字段
      %{user: user} when is_map(user) ->
        Map.get(user, :avatar_id, 1)

      # 兼容旧格式：Map格式，直接有headid字段
      %{headid: headid} when is_integer(headid) ->
        headid

      # 其他情况
      _ ->
        1
    end
  end

  # 获取当前阶段开始时间
  defp get_current_phase_start_time(state) do
    current_time = System.system_time(:millisecond)

    case state.game_data.phase do
      :waiting -> Map.get(state.game_data, :waiting_start_time, current_time)
      :betting -> Map.get(state.game_data, :betting_start_time, current_time)
      :flying -> Map.get(state.game_data, :flying_start_time, current_time)
      :settling -> Map.get(state.game_data, :settling_start_time, current_time)
      _ -> current_time
    end
  end

  # 获取剩余时间 - 基于阶段开始时间戳计算
  defp get_time_left(state) do
    current_time = System.system_time(:millisecond)
    phase_start_time = get_current_phase_start_time(state)

    case state.game_data.phase do
      :waiting ->
        total_time = Map.get(state.game_data.config, :free_time, 5) * 1000
        remaining = max(0, total_time - (current_time - phase_start_time))
        div(remaining, 1000)

      :betting ->
        total_time = Map.get(state.game_data.config, :betting_time, 15) * 1000
        remaining = max(0, total_time - (current_time - phase_start_time))
        div(remaining, 1000)

      :flying ->
        # 飞行阶段没有固定时间限制，返回已飞行时间
        div(current_time - phase_start_time, 1000)

      :settling ->
        total_time = Map.get(state.game_data.config, :settling_time, 5) * 1000
        remaining = max(0, total_time - (current_time - phase_start_time))
        div(remaining, 1000)

      _ ->
        0
    end
  end

  # 获取当前倍数
  defp get_current_multiplier(state) do
    case state.game_data.phase do
      :flying ->
        current_time = System.system_time(:millisecond)
        flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
        elapsed_time = current_time - flying_start_time
        CrashLogic.get_current_multiplier(elapsed_time, state.game_data.config)

      _ ->
        # 默认1.00x
        100
    end
  end

  # 获取总下注金额
  defp get_total_bets(state) do
    Map.get(state.game_data, :total_bets, 0)
  end

  # 获取玩家下注信息
  defp get_player_bet(state, player_id) do
    bets = Map.get(state.game_data, :player_bets, %{})
    Map.get(bets, player_id, nil)
  end

  defp count_real_players(state) do
    state.players
    |> Enum.count(fn {_numeric_id, player} ->
      not Map.get(player, :is_robot, false)
    end)
  end

  @doc """
  获取房间统计信息（包括机器人统计）
  """
  def get_room_statistics(state) do
    robot_stats = CrashAI.get_robot_statistics(state)

    %{
      room_id: state.id,
      phase: state.game_data.phase,
      total_players: map_size(state.players),
      real_players: count_real_players(state),
      robot_statistics: robot_stats,
      game_round: Map.get(state.game_data, :round, 1)
    }
  end

  @doc """
  记录机器人行为日志（用于调试和监控）
  """
  def log_robot_behavior(state) do
    stats = get_room_statistics(state)

    Logger.info("🎰 [CRASH_ROOM_STATS] 房间统计 - 房间: #{stats.room_id}, 阶段: #{stats.phase}")

    Logger.info(
      "🎰 [CRASH_ROOM_STATS] 玩家统计 - 总数: #{stats.total_players}, 真实: #{stats.real_players}, 机器人: #{stats.robot_statistics.total_robots}"
    )

    Logger.info(
      "🎰 [CRASH_ROOM_STATS] 机器人行为 - 下注: #{stats.robot_statistics.betting_robots}, 下车: #{stats.robot_statistics.cashed_out_robots}"
    )

    Logger.info("🎰 [CRASH_ROOM_STATS] 机器人类型分布: #{inspect(stats.robot_statistics.robot_types)}")
  end

  # ==================== 机器人余额管理 ====================

  @doc """
  游戏结算后更新机器人余额
  """
  defp update_robot_balances_after_game(state) do
    robot_manager = state.game_data.robot_manager

    if robot_manager do
      # 获取所有机器人
      robots = Enum.filter(state.players, fn {_id, player} ->
        Map.get(player, :is_robot, false)
      end)

      # 更新每个机器人的余额
      Enum.each(robots, fn {robot_id, robot} ->
        # 使用统一的余额获取函数，遵循DRY原则
        current_balance = get_unified_player_balance(robot)
        profit = Map.get(robot, :profit, 0)
        new_balance = current_balance + profit

        # 更新机器人管理器中的余额记录
        reason = if profit > 0, do: "游戏获胜", else: "游戏失败"
        CrashAI.update_robot_balance(robot_manager, robot_id, new_balance, reason)

        Logger.debug("🤖 [CRASH_BALANCE_UPDATE] 机器人#{robot_id}余额更新: #{current_balance} -> #{new_balance}, 盈亏: #{profit}")
      end)

      state
    else
      Logger.warning("🤖 [CRASH_BALANCE_UPDATE] 机器人管理器未初始化")
      state
    end
  end

  # 辅助函数：从状态中移除玩家（支持robot_id）
  defp remove_player_from_state(state, robot_id) when is_integer(robot_id) do
    updated_players = Map.delete(state.players, robot_id)
    %{state | players: updated_players}
  end

  # 辅助函数：从状态中移除玩家（支持player结构）
  defp remove_player_from_state(state, player) when is_map(player) do
    player_id = Map.get(player, :numeric_id)
    updated_players = Map.delete(state.players, player_id)
    %{state | players: updated_players}
  end

  # 辅助函数：添加玩家到状态
  defp add_player_to_state(state, player) do
    player_id = Map.get(player, :numeric_id)
    updated_players = Map.put(state.players, player_id, player)
    %{state | players: updated_players}
  end

  # 辅助函数：构建玩家信息
  defp build_player_info(player) do
    %{
      "numeric_id" => Map.get(player, :numeric_id),
      "nickname" => get_player_nickname(player),
      "avatar_id" => get_player_avatar_id(player),
      "points" => Map.get(player, :points, 0),
      "is_robot" => Map.get(player, :is_robot, false)
    }
  end

  # 获取玩家昵称
  defp get_player_nickname(player) do
    case player do
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        Cypridina.Teen.GameSystem.PlayerData.get_display_name(p)
      %{nickname: nickname} when is_binary(nickname) ->
        nickname
      _ ->
        "Player#{Map.get(player, :numeric_id, 0)}"
    end
  end

  # 获取玩家头像ID
  defp get_player_avatar_id(player) do
    case player do
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        Map.get(p.user, :avatar_id, 1)
      %{avatar_id: avatar_id} when is_integer(avatar_id) ->
        avatar_id
      _ ->
        1
    end
  end

  # ==================== 库存和历史记录管理 ====================

  @doc """
  更新库存分（下注时增加，下车时减少）
  """
  defp update_stock(state, amount_change) do
    # 获取当前库存，如果不存在则使用配置中的初始值
    default_stock = get_in(state, [:game_data, :stock_config, :initial_stock]) || 10_000_000
    current_stock = Map.get(state, :current_stock, default_stock)
    new_stock = current_stock + amount_change

    Logger.info("💰 [STOCK_UPDATE] 库存变化: #{current_stock} + #{amount_change} = #{new_stock}")

    Map.put(state, :current_stock, new_stock)
  end

  @doc """
  玩家下注时更新库存（只有真实玩家才影响库存）
  """
  defp on_player_bet_stock_update(state, player_id, bet_amount) do
    # 获取玩家信息
    player = Map.get(state.players, player_id)
    is_robot = if player, do: Map.get(player, :is_robot, false), else: false

    if not is_robot do
      # 只有真实玩家下注时，系统收到钱，库存增加
      Logger.info("💰 [STOCK_UPDATE] 真实玩家#{player_id}下注#{bet_amount}，库存增加")
      update_stock(state, bet_amount)
    else
      # 机器人下注不影响库存
      Logger.debug("🤖 [STOCK_UPDATE] 机器人#{player_id}下注#{bet_amount}，库存不变")
      state
    end
  end

  @doc """
  玩家下车时更新库存和历史记录（只有真实玩家才影响库存和系统记录）
  """
  defp on_player_cash_out_update(state, player_id, bet_amount, cash_out_multiplier, payout, profit) do
    # 获取玩家数据
    player = Map.get(state.players, player_id)

    # 只记录真实玩家的历史
    is_robot = if player, do: player.is_robot, else: false

    if not is_robot do
      # 真实玩家：更新库存分（下车时系统支付赔付金额，库存减少）
      Logger.info("💰 [STOCK_UPDATE] 真实玩家#{player_id}下车获得#{payout}，库存减少")
      updated_state = update_stock(state, -payout)  # 减少整个赔付金额，不是只减少盈利

      # 获取玩家下注前的原始金额
      original_balance = get_player_original_balance(state, player_id)

      # 更新玩家历史记录和系统输赢记录
      update_player_history(updated_state, player_id, bet_amount, cash_out_multiplier, profit, original_balance)
    else
      # 机器人：不影响库存和系统记录
      Logger.debug("🤖 [STOCK_UPDATE] 机器人#{player_id}下车盈利#{profit}，库存不变，不记录系统输赢")
      state
    end
  end

  @doc """
  更新玩家历史记录
  """
  defp update_player_history(state, player_id, bet_amount, cash_out_multiplier, profit, original_balance) do
    player_histories = Map.get(state, :player_histories, %{})

    # 获取当前玩家历史
    current_history = Map.get(player_histories, player_id, %{
      total_profit: 0,
      recent_games: []
    })

    # 更新总盈利
    new_total_profit = current_history.total_profit + profit

    # 添加新的游戏记录（包含下注前原始金额）
    new_game_record = %{
      bet_amount: bet_amount,
      cash_out_multiplier: cash_out_multiplier,
      profit: profit,
      original_balance: original_balance,  # 下注前的原始金额
      timestamp: System.system_time(:millisecond)
    }

    # 保持最近20局记录
    new_recent_games = [new_game_record | current_history.recent_games]
    |> Enum.take(20)

    # 更新玩家历史
    updated_history = %{
      total_profit: new_total_profit,
      recent_games: new_recent_games
    }

    updated_player_histories = Map.put(player_histories, player_id, updated_history)

    Logger.info("📊 [PLAYER_HISTORY] 玩家#{player_id} 历史更新: 总盈利#{new_total_profit}, 记录数#{length(new_recent_games)}")

    # 更新系统最近20局输赢记录
    updated_state_with_history = Map.put(state, :player_histories, updated_player_histories)
    update_system_recent_games(updated_state_with_history, profit)
  end

  @doc """
  更新系统最近20局输赢记录
  """
  defp update_system_recent_games(state, profit) do
    system_recent_games = Map.get(state, :system_recent_games, [])

    # 系统视角：玩家盈利为负数，玩家亏损为正数
    system_profit = -profit

    # 添加新记录并保持最近20局
    new_system_games = [system_profit | system_recent_games]
    |> Enum.take(20)

    Map.put(state, :system_recent_games, new_system_games)
  end

  @doc """
  获取玩家下注前的原始金额
  """
  defp get_player_original_balance(state, player_id) do
    # 从玩家数据中获取下注前记录的原始金额
    player = Map.get(state.players, player_id)
    if player, do: Map.get(player, :original_balance, 0), else: 0
  end

  @doc """
  游戏结束时统一更新系统输赢记录
  """
  defp update_system_record_on_game_end(state) do
    # 计算本局真实玩家的总盈亏
    real_players_profit = state.players
    |> Map.values()
    |> Enum.filter(fn player -> not Map.get(player, :is_robot, false) end)
    |> Enum.map(fn player -> Map.get(player, :profit, 0) end)
    |> Enum.sum()

    if real_players_profit != 0 do
      # 有真实玩家参与，更新系统记录
      Logger.info("📊 [SYSTEM_RECORD] 本局真实玩家总盈亏: #{real_players_profit}分")
      update_system_recent_games(state, real_players_profit)
    else
      # 没有真实玩家参与，不更新系统记录
      Logger.info("📊 [SYSTEM_RECORD] 本局无真实玩家参与，不更新系统输赢记录")
      state
    end
  end

  # 注意：handle_info 函数已经由 RoomBase 提供，这里只需要实现特定的回调
end
