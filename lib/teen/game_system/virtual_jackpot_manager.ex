defmodule Teen.GameSystem.VirtualJackpotManager do
  @moduledoc """
  虚拟Jackpot管理器 - 预生成调度版本

  功能特点：
  1. 虚拟奖池与真实奖池智能联动
  2. 严格按游戏规则生成虚拟中奖
  3. 智能增量算法，根据奖池差异调整速度
  4. 预生成中奖时间和奖品的调度系统
  5. 主动式调度执行，替代被动式检查
  """

  use GenServer
  require Logger
  alias Teen.RobotManagement.RobotEntity

  # ==================== 模块配置 ====================

  # 最大虚拟记录数量
  @max_records_per_game 20

  # 虚拟奖池更新间隔（毫秒）
  @pool_update_interval 3_000

  # 真实奖池同步间隔（毫秒）- 每60秒同步一次真实奖池余额
  @real_pool_sync_interval 60_000

  # 极端差异自动调整配置
  @extreme_adjustment_config %{
    # 触发条件：虚拟奖池与真实奖池差异超过20%（百万级奖池需要更严格控制）
    trigger_threshold: 0.20,
    # 调整目标范围：真实奖池的93-98%（新的健康区间）
    target_range: {0.93, 0.98},
    # 分阶段调整：3-5次渐进调整
    adjustment_phases: {3, 5},
    # 每次调整间隔（毫秒）：1-2分钟
    phase_interval: {60_000, 120_000},
    # 最大调整幅度（防止过度调整）
    max_adjustment_per_phase: 0.25
  }

  # 预生成调度配置 - 优化为更短间隔和平均分布
  @schedule_generation_config %{
    # 基础间隔范围（秒）- 大幅缩短，特别slotniu需要更高频率
    base_intervals: %{
      "slot777" => {30, 90},    # 0.5-1.5分钟（高频）
      "slotniu" => {20, 60},    # 0.33-1分钟（超高频！）
      "slotcat" => {30, 90}     # 0.5-1.5分钟（高频）
    },
    # 预生成时间窗口（小时）
    schedule_window_hours: 2,
    # 调度重新生成间隔（分钟）
    reschedule_interval_minutes: 30,
    # 频率调整系数（根据奖池状态）
    frequency_modifiers: %{
      :much_higher => 0.5,   # 增加中奖频率以消耗虚拟奖池
      :slightly_higher => 0.7,
      :balanced => 1.0,
      :slightly_lower => 1.2,  # 减少中奖频率让奖池恢复
      :much_lower => 1.5
    }
  }

  # ==================== 游戏规则配置 ====================

  # Slot777 规则配置
  @slot777_rules %{
    # 合法的单线下注金额 (₹)
    valid_single_line_bets: [10, 20, 100, 200],
    # 单线下注 => 总下注的映射 (注意：这里的单位应该是系统内部单位，需要×100)
    single_to_total_bet: %{10 => 9000, 20 => 18000, 100 => 90000, 200 => 180000},
    # Jackpot比例配置 (根据7的数量和单线下注金额) - 提高大奖比例
    percentage_table: %{
      200 => %{5 => 35, 4 => 28, 3 => 20},  # 大额下注，更高比例
      100 => %{5 => 25, 4 => 20, 3 => 15},  # 中额下注，合理比例
      20 => %{5 => 18, 4 => 12, 3 => 8},    # 小额下注也能有不错的大奖
      10 => %{5 => 15, 4 => 10, 3 => 5}     # 最小下注，保证大奖吸引力
    }
  }

  # SlotCat 规则配置（三个奖池系统）
  @slotcat_rules %{
    # 合法的总下注金额 (系统单位)
    valid_total_bets: [9000, 18000, 90000, 180_000],
    # Jackpot比例配置 (根据奖池类型和总下注金额) - 提高大奖比例
    percentage_table: %{
      180_000 => %{left: 20, right: 30, center: 60},  # 大额下注，更高比例
      90000 => %{left: 15, right: 25, center: 50},    # 中额下注，合理比例
      18000 => %{left: 12, right: 18, center: 35},    # 小额下注也能有不错大奖
      9000 => %{left: 8, right: 12, center: 25}       # 最小下注，保证吸引力
    },
    # 奖池类型映射（jackpot数量 -> 奖池类型）
    pool_types: %{3 => :left, 4 => :right, 5 => :center}
  }

  # SlotNiu 规则配置
  @slotniu_rules %{
    # 合法的下注金额 (系统单位)
    valid_bets: [180, 900, 1800, 9000, 18000, 90000, 180_000],
    # Jackpot倍率表 (根据下注金额和牛头数量)
    multipliers: %{
      180_000 => %{3 => 2000, 4 => 3000, 5 => 4000, 6 => 5000, 7 => 6000, 8 => 7000, 9 => 8000},
      90000 => %{3 => 1000, 4 => 1500, 5 => 2000, 6 => 2500, 7 => 3000, 8 => 3500, 9 => 4000},
      18000 => %{3 => 200, 4 => 300, 5 => 400, 6 => 500, 7 => 600, 8 => 700, 9 => 800},
      9000 => %{3 => 100, 4 => 150, 5 => 200, 6 => 250, 7 => 300, 8 => 350, 9 => 400},
      1800 => %{3 => 20, 4 => 30, 5 => 40, 6 => 50, 7 => 60, 8 => 70, 9 => 80},
      900 => %{3 => 10, 4 => 15, 5 => 20, 6 => 25, 7 => 30, 8 => 35, 9 => 40},
      180 => %{3 => 2, 4 => 3, 5 => 4, 6 => 5, 7 => 6, 8 => 7, 9 => 8}
    },
    # Jackpot等级范围
    jackpot_levels: [1, 2, 3, 4, 5]
  }

  # ==================== 公共API ====================

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc "添加虚拟中奖记录"
  def add_virtual_win(game_type, win_record) do
    GenServer.call(__MODULE__, {:add_virtual_win, game_type, win_record})
  end

  @doc "获取虚拟中奖记录"
  def get_virtual_records(game_type, limit \\ 10) do
    GenServer.call(__MODULE__, {:get_virtual_records, game_type, limit})
  end

  @doc "获取显示奖池状态"
  def get_display_jackpot_status do
    GenServer.call(__MODULE__, :get_display_jackpot_status)
  end

  @doc "获取特定游戏的显示奖池"
  def get_display_jackpot(game_type) do
    GenServer.call(__MODULE__, {:get_display_jackpot, game_type})
  end

  @doc "获取游戏房间初始化用的虚拟奖池金额"
  def get_virtual_jackpot_for_room(game_type) do
    GenServer.call(__MODULE__, {:get_virtual_jackpot_for_room, game_type})
  end

  @doc "获取SlotCat三个奖池的虚拟余额（专门用于游戏显示）"
  def get_slotcat_virtual_jackpots do
    GenServer.call(__MODULE__, :get_slotcat_virtual_jackpots)
  end

  @doc "手动生成虚拟中奖（用于测试）"
  def generate_virtual_win_manual(game_type) do
    GenServer.call(__MODULE__, {:generate_virtual_win_manual, game_type})
  end

  @doc "同步真实奖池金额"
  def sync_real_jackpot(game_type, real_amount) do
    GenServer.call(__MODULE__, {:sync_real_jackpot, game_type, real_amount})
  end

  @doc "获取虚拟奖池状态"
  def get_virtual_pool_status(game_type) do
    GenServer.call(__MODULE__, {:get_virtual_pool_status, game_type})
  end

  @doc "获取中奖调度状态"
  def get_win_schedule_status do
    GenServer.call(__MODULE__, :get_win_schedule_status)
  end

  @doc "获取当前中奖调度表"
  def get_current_schedule(limit \\ 10) do
    GenServer.call(__MODULE__, {:get_current_schedule, limit})
  end

  @doc "手动重新生成调度"
  def regenerate_schedule_manual do
    GenServer.call(__MODULE__, :regenerate_schedule_manual)
  end

  @doc "手动同步所有真实奖池余额"
  def sync_real_pools_manual do
    GenServer.call(__MODULE__, :sync_real_pools_manual)
  end

  @doc """
  处理真实Jackpot中奖，同步扣除虚拟奖池金额
  
  参数:
  - game_id: 游戏ID (40=slot777, 41=slotniu, 42=slotcat)
  - jackpot_amount: 中奖金额
  - pool_type: 奖池类型（可选，SlotCat需要指定 :center, :left, :right）
  """
  def handle_real_jackpot_win(game_id, jackpot_amount, pool_type \\ nil) do
    GenServer.call(__MODULE__, {:handle_real_jackpot_win, game_id, jackpot_amount, pool_type})
  end

  # ==================== GenServer 回调 ====================

  @impl true
  def init(_opts) do
    Logger.info("🎰 [VIRTUAL_JACKPOT] 启动虚拟奖池管理器...")

    # 初始化状态
    state = %{
      # 虚拟中奖记录
      virtual_records: %{},
      # 虚拟奖池状态
      virtual_pools: init_virtual_pools(),
      # 显示奖池（前端看到的）
      display_jackpots: init_display_jackpots(),
      # 预生成中奖调度表
      win_schedule: init_win_schedule(),
      # 调度状态控制
      schedule_control: init_schedule_control(),
      # 极端差异调整状态
      extreme_adjustments: init_extreme_adjustments(),
      # 上次奖池更新时间
      last_pool_update: System.system_time(:millisecond)
    }

    # 启动定时任务
    schedule_pool_update()
    schedule_win_execution()
    schedule_schedule_regeneration()
    schedule_real_pool_sync()

    # 立即生成一次初始调度
    Process.send_after(__MODULE__, :regenerate_schedule, 1000)

    Logger.info("✅ [VIRTUAL_JACKPOT] 虚拟奖池管理器启动完成")
    {:ok, state}
  end

  @impl true
  def handle_call({:add_virtual_win, game_type, win_record}, _from, state) do
    game_records = Map.get(state.virtual_records, game_type, [])
    updated_records = [win_record | game_records] |> Enum.take(@max_records_per_game)
    new_virtual_records = Map.put(state.virtual_records, game_type, updated_records)
    new_state = %{state | virtual_records: new_virtual_records}
    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call({:get_virtual_records, game_type, limit}, _from, state) do
    records = Map.get(state.virtual_records, game_type, []) |> Enum.take(limit)
    {:reply, records, state}
  end

  @impl true
  def handle_call(:get_display_jackpot_status, _from, state) do
    {:reply, state.display_jackpots, state}
  end

  @impl true
  def handle_call({:get_display_jackpot, game_type}, _from, state) do
    display_jackpot = Map.get(state.display_jackpots, game_type)
    {:reply, display_jackpot, state}
  end

  @impl true
  def handle_call({:get_virtual_jackpot_for_room, game_type}, _from, state) do
    virtual_jackpot = case game_type do
      "slotcat" ->
        # SlotCat返回三个奖池的总和用于房间初始化显示
        slotcat_data = Map.get(state.virtual_pools, "slotcat")
        if slotcat_data && slotcat_data.pools do
          pools = slotcat_data.pools
          %{
            center: pools.center.virtual_pool,
            left: pools.left.virtual_pool,
            right: pools.right.virtual_pool,
            total: pools.center.virtual_pool +
                   pools.left.virtual_pool +
                   pools.right.virtual_pool
          }
        else
          %{center: 0, left: 0, right: 0, total: 0}
        end
      _ ->
        # 其他游戏返回单一虚拟奖池余额
        pool_info = Map.get(state.virtual_pools, game_type)
        if pool_info, do: pool_info.virtual_pool, else: 0
    end
    {:reply, virtual_jackpot, state}
  end

  @impl true
  def handle_call(:get_slotcat_virtual_jackpots, _from, state) do
    slotcat_data = Map.get(state.virtual_pools, "slotcat")
    jackpots = if slotcat_data && slotcat_data.pools do
      pools = slotcat_data.pools
      %{
        center: pools.center.virtual_pool,
        left: pools.left.virtual_pool,
        right: pools.right.virtual_pool
      }
    else
      %{center: 0, left: 0, right: 0}
    end
    {:reply, jackpots, state}
  end

  @impl true
  def handle_call({:generate_virtual_win_manual, game_type}, _from, state) do
    case generate_virtual_win_with_state(state, game_type) do
      {:ok, win_record} ->
        # 检查是否应该阻止这次虚拟中奖（保护奖池底线）
        if should_block_virtual_win?(state, game_type, win_record) do
          {current_ratio, reason} = get_block_reason(state, game_type, win_record)
          Logger.warn("🚫 [VIRTUAL_JACKPOT] 阻止手动虚拟中奖: #{game_type} - #{reason} (当前比例: #{Float.round(current_ratio * 100, 1)}%, 需达到93%以上才能恢复)")
          {:reply, {:error, :virtual_pool_too_low}, state}
        else
          # 更新状态
          updated_state = add_virtual_win_to_state(state, game_type, win_record)
          # 广播
          broadcast_virtual_win(game_type, win_record)
          # 影响虚拟奖池
          final_state = apply_virtual_win_impact(updated_state, game_type, win_record)
          {:reply, {:ok, win_record}, final_state}
        end
      error ->
        {:reply, error, state}
    end
  end

  @impl true
  def handle_call({:sync_real_jackpot, game_type, real_amount}, _from, state) do
    updated_pools = put_in(state.virtual_pools, [game_type, :real_pool], real_amount)
    new_state = %{state | virtual_pools: updated_pools}
    Logger.info("🔄 [VIRTUAL_JACKPOT] 同步真实奖池 #{game_type}: #{real_amount}")
    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call({:get_virtual_pool_status, game_type}, _from, state) do
    pool_status = Map.get(state.virtual_pools, game_type)
    {:reply, pool_status, state}
  end

  @impl true
  def handle_call(:get_win_schedule_status, _from, state) do
    current_time = System.system_time(:second)
    scheduled_count = length(state.win_schedule)
    due_count = Enum.count(state.win_schedule, fn win -> win.execute_time <= current_time end)

    status = %{
      total_scheduled: scheduled_count,
      due_for_execution: due_count,
      schedule_version: state.schedule_control.schedule_version,
      last_generation: state.schedule_control.last_generation_time,
      next_generation: state.schedule_control.next_generation_time
    }
    {:reply, status, state}
  end

  @impl true
  def handle_call({:get_current_schedule, limit}, _from, state) do
    current_time = System.system_time(:second)
    upcoming_wins = state.win_schedule
                   |> Enum.filter(fn win -> win.execute_time > current_time end)
                   |> Enum.sort_by(& &1.execute_time)
                   |> Enum.take(limit)
    {:reply, upcoming_wins, state}
  end

  @impl true
  def handle_call(:regenerate_schedule_manual, _from, state) do
    updated_state = regenerate_win_schedule(state)
    {:reply, :ok, updated_state}
  end

  @impl true
  def handle_call(:sync_real_pools_manual, _from, state) do
    updated_state = sync_all_real_pools(state)
    {:reply, :ok, updated_state}
  end

  @impl true
  def handle_call({:handle_real_jackpot_win, game_id, jackpot_amount, pool_type}, _from, state) do
    case handle_real_jackpot_win_internal(state, game_id, jackpot_amount, pool_type) do
      {:ok, updated_state} ->
        {:reply, :ok, updated_state}
      {:error, reason} ->
        Logger.error("❌ [VIRTUAL_JACKPOT] 处理真实中奖失败: #{inspect(reason)}")
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call(:get_state, _from, state) do
    {:reply, state, state}
  end

  @impl true
  def handle_info(:update_virtual_pools, state) do
    updated_state = update_all_virtual_pools(state)
    # 继续调度
    schedule_pool_update()
    {:noreply, updated_state}
  end

  @impl true
  def handle_info(:execute_scheduled_wins, state) do
    updated_state = execute_due_wins(state)
    # 继续调度
    schedule_win_execution()
    {:noreply, updated_state}
  end

  @impl true
  def handle_info(:regenerate_schedule, state) do
    updated_state = regenerate_win_schedule(state)
    # 继续调度
    schedule_schedule_regeneration()
    {:noreply, updated_state}
  end

  @impl true
  def handle_info(:sync_real_pools, state) do
    updated_state = sync_all_real_pools(state)
    # 继续调度
    schedule_real_pool_sync()
    {:noreply, updated_state}
  end

  @impl true
  def handle_info({:execute_adjustment_phase, game_type}, state) do
    updated_state = execute_next_adjustment_phase(state, game_type)
    {:noreply, updated_state}
  end

  @impl true
  def handle_info(msg, state) do
    Logger.warning("🎰 [VIRTUAL_JACKPOT] 收到未处理的消息: #{inspect(msg)}")
    {:noreply, state}
  end

  # ==================== 初始化函数 ====================

  defp init_virtual_pools do
    %{
      "slot777" => %{
        real_pool: get_current_jackpot_amount("slot777"),
        virtual_pool: trunc(get_current_jackpot_amount("slot777") * 0.95),  # 从95%开始，接近真实奖池
        target_ratio: 0.955,  # 目标比例调整为95.5%（93-98%健康区间中点）
        last_sync_time: System.system_time(:millisecond),
        increment_history: []  # 增量历史
      },
      "slotniu" => %{
        real_pool: get_current_jackpot_amount("slotniu"),
        virtual_pool: trunc(get_current_jackpot_amount("slotniu") * 0.95),  # 从95%开始，接近真实奖池
        target_ratio: 0.955,  # 目标比例调整为95.5%（93-98%健康区间中点）
        last_sync_time: System.system_time(:millisecond),
        increment_history: []
      },
      "slotcat" => %{
        # SlotCat 有三个独立的奖池
        pools: %{
          center: %{
            real_pool: Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :center),
            virtual_pool: trunc(Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :center) * 0.95),  # 从95%开始，接近真实奖池
            target_ratio: 0.955,  # 目标比例调整为95.5%（93-98%健康区间中点）
            last_sync_time: System.system_time(:millisecond),
            increment_history: []
          },
          left: %{
            real_pool: Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :left),
            virtual_pool: trunc(Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :left) * 0.95),  # 从95%开始，接近真实奖池
            target_ratio: 0.955,  # 目标比例调整为95.5%（93-98%健康区间中点）
            last_sync_time: System.system_time(:millisecond),
            increment_history: []
          },
          right: %{
            real_pool: Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :right),
            virtual_pool: trunc(Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :right) * 0.95),  # 从95%开始，接近真实奖池
            target_ratio: 0.955,  # 目标比例调整为95.5%（93-98%健康区间中点）
            last_sync_time: System.system_time(:millisecond),
            increment_history: []
          }
        }
      }
    }
  end

  defp init_display_jackpots do
    %{
      "slot777" => get_current_jackpot_amount("slot777"),
      "slotniu" => get_current_jackpot_amount("slotniu"),
      "slotcat" => %{
        # SlotCat使用三个奖池的结构
        center: get_slotcat_single_pool_amount(:center),
        left: get_slotcat_single_pool_amount(:left),
        right: get_slotcat_single_pool_amount(:right),
        total: get_current_jackpot_amount("slotcat")
      }
    }
  end

  defp init_win_schedule do
    # 初始化空的中奖调度表
    []
  end

  defp init_schedule_control do
    current_time = System.system_time(:second)
    %{
      last_generation_time: current_time,
      next_generation_time: current_time + (@schedule_generation_config.reschedule_interval_minutes * 60),
      schedule_version: 1,
      total_scheduled_wins: 0
    }
  end

  # ==================== 虚拟奖池更新逻辑 ====================

  defp update_all_virtual_pools(state) do
    game_types = ["slot777", "slotniu", "slotcat"]

    updated_pools = Enum.reduce(game_types, state.virtual_pools, fn game_type, pools ->
      update_single_virtual_pool(pools, game_type)
    end)

    updated_display = Enum.reduce(game_types, state.display_jackpots, fn game_type, display ->
      case game_type do
        "slotcat" ->
          # SlotCat 使用三个奖池的总和作为显示奖池
          slotcat_pools = updated_pools[game_type][:pools]
          if slotcat_pools do
            total_virtual = slotcat_pools.center.virtual_pool +
                           slotcat_pools.left.virtual_pool +
                           slotcat_pools.right.virtual_pool
            Map.put(display, game_type, total_virtual)
          else
            display
          end
        _ ->
          # 其他游戏使用单一虚拟奖池
          virtual_pool = updated_pools[game_type][:virtual_pool]
          Map.put(display, game_type, virtual_pool)
      end
    end)

    # 广播奖池更新
    broadcast_jackpot_updates(updated_display, updated_pools)

    # 更新状态
    updated_state = %{state |
      virtual_pools: updated_pools,
      display_jackpots: updated_display,
      last_pool_update: System.system_time(:millisecond)
    }

    # 检查是否需要极端差异调整
    check_extreme_adjustment_needed(updated_state)
  end

  defp update_single_virtual_pool(pools, game_type) do
    pool_state = Map.get(pools, game_type)
    if pool_state do
      case game_type do
        "slotcat" ->
          # SlotCat 有三个独立的奖池需要分别更新
          updated_pools = Enum.reduce([:center, :left, :right], pool_state.pools, fn pool_id, pools_acc ->
            pool_data = pools_acc[pool_id]
            if pool_data do
              # 计算智能增量
              increment = calculate_smart_increment(pool_data)
              new_virtual_pool = pool_data.virtual_pool + increment

              # 更新奖池状态
              updated_pool_data = %{pool_data |
                virtual_pool: new_virtual_pool,
                increment_history: add_to_history(pool_data.increment_history, increment)
              }

              Map.put(pools_acc, pool_id, updated_pool_data)
            else
              pools_acc
            end
          end)

          # 更新 SlotCat 的奖池结构
          updated_slotcat_state = %{pool_state | pools: updated_pools}
          Map.put(pools, game_type, updated_slotcat_state)

        _ ->
          # 其他游戏的单一奖池逻辑
          increment = calculate_smart_increment(pool_state)
          new_virtual_pool = pool_state.virtual_pool + increment

          # 更新奖池状态
          updated_pool_state = %{pool_state |
            virtual_pool: new_virtual_pool,
            increment_history: add_to_history(pool_state.increment_history, increment)
          }

          Map.put(pools, game_type, updated_pool_state)
      end
    else
      pools
    end
  end

  defp calculate_smart_increment(pool_state) do
    real_pool = pool_state.real_pool
    virtual_pool = pool_state.virtual_pool
    increment_history = Map.get(pool_state, :increment_history, [])

    # 计算奖池比例
    ratio = if real_pool > 0, do: virtual_pool / real_pool, else: 1.0

    # 基础增量：根据奖池规模设定基础比例（动态增量核心算法）
    base_rate = cond do
      real_pool >= 200_000_000 -> 0.003    # 200万以上：0.3%
      real_pool >= 100_000_000 -> 0.0025   # 100万以上：0.25%
      real_pool >= 50_000_000 -> 0.002     # 50万以上：0.2%
      real_pool >= 10_000_000 -> 0.0015    # 10万以上：0.15%
      true -> 0.001                         # 10万以下：0.1%
    end

    base_increment = trunc(real_pool * base_rate)

    # 差距调整倍数：密集梯度追赶系统（每10%/15%/20%一个档次）
    gap_multiplier = cond do
      # 极低区间：每5%一档，超强追赶
      ratio < 0.10 -> 25.0     # < 10%：25倍超级紧急追赶
      ratio < 0.15 -> 20.0     # 10-15%：20倍紧急追赶
      ratio < 0.20 -> 18.0     # 15-20%：18倍高速追赶
      ratio < 0.25 -> 15.0     # 20-25%：15倍强力追赶
      ratio < 0.30 -> 12.0     # 25-30%：12倍快速追赶
      
      # 低区间：每10%一档，强力追赶
      ratio < 0.40 -> 10.0     # 30-40%：10倍加速追赶
      ratio < 0.50 -> 8.0      # 40-50%：8倍中高速追赶
      ratio < 0.60 -> 6.0      # 50-60%：6倍中速追赶
      ratio < 0.70 -> 4.5      # 60-70%：4.5倍稳定追赶
      ratio < 0.80 -> 3.0      # 70-80%：3倍温和追赶
      
      # 接近健康区间：每3%一档，精细控制
      ratio < 0.83 -> 2.5      # 80-83%：2.5倍缓慢追赶
      ratio < 0.86 -> 2.0      # 83-86%：2倍轻度追赶
      ratio < 0.89 -> 1.5      # 86-89%：1.5倍微调追赶
      ratio < 0.92 -> 1.2      # 89-92%：1.2倍精细追赶
      
      # 健康区间：1%一档，精确控制
      ratio < 0.93 -> 1.0      # 92-93%：1倍标准增长（即将进入健康区间）
      ratio < 0.94 -> 0.9      # 93-94%：0.9倍健康维持
      ratio < 0.95 -> 0.8      # 94-95%：0.8倍健康维持
      ratio < 0.96 -> 0.8      # 95-96%：0.8倍健康维持
      ratio < 0.97 -> 0.8      # 96-97%：0.8倍健康维持
      ratio < 0.98 -> 0.8      # 97-98%：0.8倍健康维持
      
      # 超出健康区间：逐步减速
      ratio < 0.99 -> 0.6      # 98-99%：0.6倍轻微减速
      ratio < 1.00 -> 0.4      # 99-100%：0.4倍减速
      ratio < 1.02 -> 0.3      # 100-102%：0.3倍明显减速
      ratio < 1.05 -> 0.2      # 102-105%：0.2倍强力减速
      ratio < 1.10 -> 0.15     # 105-110%：0.15倍防超调
      true -> 0.1              # > 110%：0.1倍最小维持
    end

    # 最终增量
    final_increment = trunc(base_increment * gap_multiplier)

    # 设置合理的上下限
    min_increment = max(1000, trunc(real_pool * 0.0005))  # 最小0.05%
    max_increment = min(20_000_000, trunc(real_pool * 0.02))  # 最大2%

    # 应用上下限
    final_increment = max(min_increment, min(max_increment, final_increment))

    # 智能随机变化：根据奖池健康状态调整波动幅度
    variance_rate = cond do
      ratio < 0.30 -> 0.02   # 严重不足时：±2%小幅波动，保持追赶稳定性
      ratio < 0.80 -> 0.03   # 追赶期：±3%适度波动
      ratio < 0.93 -> 0.02   # 接近健康：±2%精细波动
      ratio < 0.98 -> 0.01   # 健康区间：±1%微量波动，避免过度干扰
      true -> 0.015          # 超出健康：±1.5%轻微波动
    end
    
    variance = trunc(final_increment * variance_rate)
    random_adjustment = if variance > 0, do: :rand.uniform(variance * 2 + 1) - variance - 1, else: 0
    
    # 计算初步增量
    calculated_increment = max(min_increment, final_increment + random_adjustment)
    
    # 确保不低于前值：获取历史记录中的最近增量
    previous_increment = case increment_history do
      [last_increment | _] when is_number(last_increment) -> last_increment
      _ -> 0  # 没有历史记录时，使用0作为基准
    end
    
    # 平滑增量变化：允许适度下降但防止大幅波动
    if previous_increment > 0 do
      # 计算允许的下降幅度（最多下降20%），但不能低于绝对最小增量
      min_allowed = max(trunc(previous_increment * 0.8), min_increment)
      # 计算允许的上升幅度（最多上升50%）
      max_allowed = trunc(previous_increment * 1.5)
      
      # 对于追赶加速的情况，允许更大的上升幅度
      adjusted_max_allowed = if calculated_increment > previous_increment * 1.2 do
        # 允许最大200%的增长，但仍要受到绝对最大值限制
        min(trunc(previous_increment * 2.0), max_increment)
      else
        max_allowed
      end
      
      # 将计算增量限制在合理范围内，确保单调递增趋势
      max(min_allowed, min(adjusted_max_allowed, calculated_increment))
    else
      # 没有历史记录时，直接使用计算值
      calculated_increment
    end
  end

  defp add_to_history(history, increment) do
    updated_history = [increment | history] |> Enum.take(10)
    updated_history
  end

  # ==================== 预生成调度系统 ====================

  defp execute_due_wins(state) do
    current_time = System.system_time(:second)
    {due_wins, remaining_schedule} = Enum.split_with(state.win_schedule, fn scheduled_win ->
      scheduled_win.execute_time <= current_time
    end)

    if length(due_wins) > 0 do
      Logger.info("🎰 [VIRTUAL_JACKPOT] 执行预定中奖: #{length(due_wins)} 个")
    end

    updated_state = Enum.reduce(due_wins, state, fn scheduled_win, acc_state ->
      execute_scheduled_win(acc_state, scheduled_win)
    end)

    %{updated_state | win_schedule: remaining_schedule}
  end

  defp execute_scheduled_win(state, scheduled_win) do
    win_record = scheduled_win.win_record

    # 检查是否应该阻止这次虚拟中奖（保护奖池底线）
    if should_block_virtual_win?(state, win_record.game_type, win_record) do
      {current_ratio, reason} = get_block_reason(state, win_record.game_type, win_record)
      Logger.warn("🚫 [VIRTUAL_JACKPOT] 阻止虚拟中奖: #{win_record.game_type} - #{reason} (当前比例: #{Float.round(current_ratio * 100, 1)}%, 需达到93%以上才能恢复)")
      state
    else
      Logger.info("✅ [VIRTUAL_JACKPOT] 执行预定中奖: #{win_record.game_type} - #{win_record.jackpot_amount}")

      # 添加中奖记录
      updated_state = add_virtual_win_to_state(state, win_record.game_type, win_record)
      # 广播中奖
      broadcast_virtual_win(win_record.game_type, win_record)
      # 应用中奖影响
      apply_virtual_win_impact(updated_state, win_record.game_type, win_record)
    end
  end

  # 检查是否应该阻止虚拟中奖（保护奖池底线）
  # 新策略：只在真正的恢复期阻止，允许健康奖池的大奖消耗
  defp should_block_virtual_win?(state, game_type, win_record) do
    case game_type do
      "slotcat" ->
        # SlotCat 检查特定奖池
        pool_type = Map.get(win_record.game_specific_data, :pool_type, :center)
        slotcat_pools = get_in(state, [:virtual_pools, "slotcat", :pools])

        if slotcat_pools && slotcat_pools[pool_type] do
          pool_data = slotcat_pools[pool_type]
          virtual_pool = pool_data.virtual_pool
          real_pool = pool_data.real_pool

          if real_pool > 0 do
            current_ratio = virtual_pool / real_pool

            # 只有在恢复期才阻止中奖
            if current_ratio < 0.93 do
              true
            else
              # 虚拟奖池健康时，允许大奖消耗，但设置合理底线
              after_win_virtual = virtual_pool - win_record.jackpot_amount
              after_win_ratio = after_win_virtual / real_pool

              # 只有在中奖后会造成严重损害时才阻止（降到40%以下）
              after_win_ratio < 0.40
            end
          else
            false
          end
        else
          # 如果奖池数据不存在，不阻止
          false
        end

      _ ->
        # 其他游戏的单一奖池逻辑
        virtual_pool = get_in(state, [:virtual_pools, game_type, :virtual_pool])
        real_pool = get_in(state, [:virtual_pools, game_type, :real_pool])

        if virtual_pool && real_pool && real_pool > 0 do
          current_ratio = virtual_pool / real_pool

          # 只有在恢复期才阻止中奖
          if current_ratio < 0.93 do
            true
          else
            # 虚拟奖池健康时，允许大奖消耗，但设置合理底线
            after_win_virtual = virtual_pool - win_record.jackpot_amount
            after_win_ratio = after_win_virtual / real_pool

            # 只有在中奖后会造成严重损害时才阻止（降到40%以下）
            after_win_ratio < 0.40
          end
        else
          false
        end
    end
  end

  # 获取阻止虚拟中奖的原因和当前比例（用于详细日志）
  defp get_block_reason(state, game_type, win_record) do
    case game_type do
      "slotcat" ->
        pool_type = Map.get(win_record.game_specific_data, :pool_type, :center)
        slotcat_pools = get_in(state, [:virtual_pools, "slotcat", :pools])

        if slotcat_pools && slotcat_pools[pool_type] do
          pool_data = slotcat_pools[pool_type]
          virtual_pool = pool_data.virtual_pool
          real_pool = pool_data.real_pool

          current_ratio = if real_pool > 0, do: virtual_pool / real_pool, else: 1.0

          if current_ratio < 0.93 do
            {current_ratio, "虚拟奖池在恢复期（需>=93%）"}
          else
            after_win_virtual = virtual_pool - win_record.jackpot_amount
            after_win_ratio = if real_pool > 0, do: after_win_virtual / real_pool, else: 1.0
            {current_ratio, "中奖后会造成严重损害（#{Float.round(after_win_ratio * 100, 1)}% < 40%底线）"}
          end
        else
          {0.0, "奖池数据不存在"}
        end

      _ ->
        virtual_pool = get_in(state, [:virtual_pools, game_type, :virtual_pool]) || 0
        real_pool = get_in(state, [:virtual_pools, game_type, :real_pool]) || 1

        current_ratio = virtual_pool / real_pool

        if current_ratio < 0.93 do
          {current_ratio, "虚拟奖池在恢复期（需>=93%）"}
        else
          after_win_virtual = virtual_pool - win_record.jackpot_amount
          after_win_ratio = after_win_virtual / real_pool
          {current_ratio, "中奖后会造成严重损害（#{Float.round(after_win_ratio * 100, 1)}% < 40%底线）"}
        end
    end
  end

  defp regenerate_win_schedule(state) do
    Logger.info("🔄 [VIRTUAL_JACKPOT] 重新生成中奖调度...")

    # 分析当前奖池状态
    pool_analysis = analyze_all_pools(state)

    # 生成新的调度计划
    new_schedule = generate_intelligent_schedule(pool_analysis, state)

    # 更新调度控制状态
    current_time = System.system_time(:second)
    updated_control = %{state.schedule_control |
      last_generation_time: current_time,
      next_generation_time: current_time + (@schedule_generation_config.reschedule_interval_minutes * 60),
      schedule_version: state.schedule_control.schedule_version + 1,
      total_scheduled_wins: length(new_schedule)
    }

    Logger.info("✅ [VIRTUAL_JACKPOT] 调度重新生成完成: #{length(new_schedule)} 个中奖事件")

    # 添加调试信息显示每个游戏的分析结果和生成的中奖数
    game_win_details = Enum.reduce(pool_analysis, [], fn {game_type, analysis}, acc ->
      # 重新计算这个游戏的中奖数来显示
      time_window_hours = (@schedule_generation_config.schedule_window_hours || 3)
      win_count = case analysis.pool_state do
        :much_higher -> trunc(analysis.recommended_wins_per_hour * time_window_hours * 1.2)
        :slightly_higher -> trunc(analysis.recommended_wins_per_hour * time_window_hours * 1.0)
        :balanced -> trunc(analysis.recommended_wins_per_hour * time_window_hours * 1.0)
        :slightly_lower -> trunc(analysis.recommended_wins_per_hour * time_window_hours * 0.8)
        :much_lower -> trunc(analysis.recommended_wins_per_hour * time_window_hours * 0.7)
      end
      win_count = max(win_count, 8)

      Logger.info("📊 [VIRTUAL_JACKPOT] #{game_type}: 健康度=#{Float.round(analysis.health_score, 3)}, 比例=#{Float.round(analysis.ratio, 3)}, 状态=#{analysis.pool_state}, 推荐频率=#{analysis.recommended_wins_per_hour}/小时, 生成中奖=#{win_count}个")
      [{game_type, win_count} | acc]
    end)

    total_generated = Enum.reduce(game_win_details, 0, fn {_, count}, acc -> acc + count end)
    Logger.info("🎯 [VIRTUAL_JACKPOT] 总计生成 #{total_generated} 个虚拟中奖事件，时间窗口: #{@schedule_generation_config.schedule_window_hours || 3}小时")

    %{state |
      win_schedule: new_schedule,
      schedule_control: updated_control
    }
  end

  # ==================== 智能调度生成算法 ====================

  defp analyze_all_pools(state) do
    game_types = ["slot777", "slotniu"]

    # 处理普通游戏（单奖池）
    base_analysis = Enum.reduce(game_types, %{}, fn game_type, analysis ->
      pool_info = Map.get(state.virtual_pools, game_type)
      pool_state = determine_pool_state(state, game_type)

      {virtual_pool, real_pool} = if pool_info do
        {pool_info.virtual_pool, pool_info.real_pool}
      else
        {0, 1}
      end

      pool_analysis = %{
        game_type: game_type,
        pool_state: pool_state,
        virtual_pool: virtual_pool,
        real_pool: real_pool,
        ratio: virtual_pool / max(real_pool, 1),
        health_score: calculate_pool_health_score(pool_info, pool_state),
        recommended_wins_per_hour: calculate_recommended_frequency_for_game(game_type, pool_state)
      }

      Map.put(analysis, game_type, pool_analysis)
    end)

    # 特殊处理SlotCat的三个独立奖池
    slotcat_analysis = analyze_slotcat_pools(state)

    # 合并分析结果
    Map.merge(base_analysis, slotcat_analysis)
  end

  # 新增：独立分析SlotCat的三个奖池
  defp analyze_slotcat_pools(state) do
    pool_info = Map.get(state.virtual_pools, "slotcat")

    if pool_info && pool_info[:pools] do
      slotcat_pools = pool_info.pools

      # 为每个奖池独立分析
      pool_analysis = Enum.reduce([:center, :left, :right], %{}, fn pool_id, acc ->
        pool_data = slotcat_pools[pool_id]

        if pool_data do
          virtual_pool = pool_data.virtual_pool
          real_pool = pool_data.real_pool
          ratio = virtual_pool / max(real_pool, 1)

          # 为单个奖池确定状态
          individual_pool_state = determine_individual_pool_state(ratio)

          analysis = %{
            game_type: "slotcat",
            pool_id: pool_id,  # 新增：标识具体奖池
            pool_state: individual_pool_state,
            virtual_pool: virtual_pool,
            real_pool: real_pool,
            ratio: ratio,
            health_score: calculate_health_score_by_ratio(ratio),
            recommended_wins_per_hour: calculate_recommended_frequency_for_game("slotcat", individual_pool_state)
          }

          # 使用复合键来区分不同奖池
          Map.put(acc, "slotcat_#{pool_id}", analysis)
        else
          acc
        end
      end)

      pool_analysis
    else
      # 如果SlotCat奖池不存在，返回空分析
      %{}
    end
  end

  # 新增：为单个奖池确定状态
  defp determine_individual_pool_state(ratio) do
    cond do
      ratio >= 1.25 -> :much_higher    # 虚拟奖池是真实的125%以上
      ratio >= 1.10 -> :slightly_higher # 虚拟奖池是真实的110-125%
      ratio >= 0.90 -> :balanced        # 虚拟奖池是真实的90-110%
      ratio >= 0.93 -> :slightly_lower  # 虚拟奖池是真实的93-98%
      true -> :much_lower               # 虚拟奖池低于真实的93%
    end
  end

  # 新增：根据奖池状态计算中奖数量
  defp calculate_win_count_by_state(analysis, time_window_hours) do
    case analysis.pool_state do
      :much_higher ->
        # 奖池过高：全力消耗，多生成中奖
        trunc(analysis.recommended_wins_per_hour * time_window_hours * 1.2)
      :slightly_higher ->
        # 奖池偏高：鼓励消耗
        trunc(analysis.recommended_wins_per_hour * time_window_hours * 1.0)
      :balanced ->
        # 平衡状态：正常频率
        trunc(analysis.recommended_wins_per_hour * time_window_hours * 1.0)
      :slightly_lower ->
        # 追赶期：保持活跃度，让执行时的保护机制来控制
        trunc(analysis.recommended_wins_per_hour * time_window_hours * 0.8)
      :much_lower ->
        # 恢复期：基础活跃度，依靠保护机制过滤
        trunc(analysis.recommended_wins_per_hour * time_window_hours * 0.7)
    end
  end

  # 新增：为不同游戏类型生成虚拟中奖（支持SlotCat独立奖池）
  defp generate_virtual_win_for_game_type(state, game_type) do
    cond do
      String.starts_with?(game_type, "slotcat_") ->
        # SlotCat特定奖池
        pool_id = extract_pool_id(game_type)
        pool_state = get_slotcat_pool_state(state, pool_id)
        generate_slotcat_virtual_win_with_state(state, pool_id, pool_state)

      true ->
        # 普通游戏
        generate_virtual_win_with_state(state, game_type)
    end
  end

  # 新增：提取奖池ID
  defp extract_pool_id(game_type) do
    cond do
      String.starts_with?(game_type, "slotcat_center") -> :center
      String.starts_with?(game_type, "slotcat_left") -> :left
      String.starts_with?(game_type, "slotcat_right") -> :right
      true -> nil
    end
  end

  # 新增：获取SlotCat特定奖池的状态
  defp get_slotcat_pool_state(state, pool_id) do
    pool_info = get_in(state, [:virtual_pools, "slotcat", :pools, pool_id])
    if pool_info do
      ratio = pool_info.virtual_pool / max(pool_info.real_pool, 1)
      determine_individual_pool_state(ratio)
    else
      :balanced
    end
  end

  # 新增：为SlotCat特定奖池生成虚拟中奖
  defp generate_slotcat_virtual_win_with_state(state, pool_id, pool_state) do
    case select_slotcat_pool_win_strategy(pool_id, pool_state) do
      {:ok, bet_amount, win_type} ->
        jackpot_amount = calculate_slotcat_pool_jackpot(state, bet_amount, win_type)
        win_record = %{
          id: "virtual_#{System.system_time(:millisecond)}_#{:rand.uniform(9999)}",
          game_type: "slotcat",
          pool_id: pool_id,  # 标识哪个奖池
          player_id: "fake_#{:rand.uniform(999999)}",
          player_name: RobotEntity.random_robot_name(),
          avatar_id: RobotEntity.random_robot_avatar(),
          avatar_url: "",
          jackpot_amount: jackpot_amount,
          bet_amount: bet_amount,
          jackpot_level: map_win_type_to_level(win_type),
          game_specific_data: generate_game_specific_data("slotcat", win_type, bet_amount),
          win_time: DateTime.utc_now(),
          is_virtual: true
        }
        {:ok, win_record}
      error ->
        error
    end
  end

  # 新增：计算SlotCat单个奖池的中奖金额
  defp calculate_slotcat_pool_jackpot(state, bet_amount, {pool_id, jackpot_count}) do
    # 获取对应单个虚拟奖池的金额
    single_virtual_pool_amount = get_slotcat_single_virtual_pool_amount(state, pool_id)
    # 根据下注金额和奖池类型计算百分比
    percentage = get_in(@slotcat_rules.percentage_table, [bet_amount, pool_id]) || 0
    # 计算中奖金额：单个虚拟奖池金额 * 百分比 / 100
    jackpot_amount = trunc(single_virtual_pool_amount * percentage / 100)
    # 确保中奖金额合理（至少是下注的2倍）
    min_amount = bet_amount * 2
    max(jackpot_amount, min_amount)
  end

  # 计算单个奖池的健康度分数（支持SlotCat的独立奖池）
  defp calculate_pool_health_score(pool_info, pool_state, pool_id \\ nil) do
    # 获取特定奖池的虚拟和真实余额
    {virtual_pool_amount, real_pool_amount} = if pool_info[:pools] && pool_id do
      # SlotCat 的特定奖池
      pool_data = pool_info.pools[pool_id]
      if pool_data do
        {pool_data.virtual_pool, pool_data.real_pool}
      else
        {0, 1}
      end
    else
      # 其他游戏的单一奖池或SlotCat的总和（用于分析）
      if pool_info[:pools] do
        # SlotCat 使用三个奖池的总和（仅用于整体分析）
        slotcat_pools = pool_info.pools
        total_virtual = slotcat_pools.center.virtual_pool +
                       slotcat_pools.left.virtual_pool +
                       slotcat_pools.right.virtual_pool
        total_real = slotcat_pools.center.real_pool +
                    slotcat_pools.left.real_pool +
                    slotcat_pools.right.real_pool
        {total_virtual, total_real}
      else
        # 其他游戏使用单一奖池
        {pool_info.virtual_pool || 0, pool_info.real_pool || 1}
      end
    end

    # 计算虚拟奖池与真实奖池的比例
    pool_ratio = virtual_pool_amount / max(real_pool_amount, 1)

    # 基于比例的动态健康度评分 - 以100%为理想平衡点
    base_score = calculate_health_score_by_ratio(pool_ratio)

    # 根据奖池状态调整
    state_modifier = case pool_state do
      :much_higher -> 1.2  # 虚拟奖池过高，鼓励中奖
      :slightly_higher -> 1.1
      :balanced -> 1.0
      :slightly_lower -> 0.9  # 略微调高，避免过度限制
      :much_lower -> 0.7   # 虚拟奖池过低，仍要保持活跃度
    end

    base_score * state_modifier
  end

  # 基于比例计算健康度的通用函数 - 密集梯度控制系统
  defp calculate_health_score_by_ratio(pool_ratio) do
    cond do
      # 极低区间：每5%一档，严格限制中奖
      pool_ratio < 0.10 -> 0.05    # < 10%：极严格限制
      pool_ratio < 0.15 -> 0.1     # 10-15%：严格限制
      pool_ratio < 0.20 -> 0.15    # 15-20%：高度限制
      pool_ratio < 0.25 -> 0.2     # 20-25%：明显限制
      pool_ratio < 0.30 -> 0.25    # 25-30%：适度限制
      
      # 低区间：每10%一档，逐步放宽
      pool_ratio < 0.40 -> 0.3     # 30-40%：较多限制
      pool_ratio < 0.50 -> 0.4     # 40-50%：中等限制
      pool_ratio < 0.60 -> 0.5     # 50-60%：轻度限制
      pool_ratio < 0.70 -> 0.6     # 60-70%：少量限制
      pool_ratio < 0.80 -> 0.65    # 70-80%：微量限制
      
      # 接近健康区间：每3%一档，精细控制
      pool_ratio < 0.83 -> 0.7     # 80-83%：准备进入健康期
      pool_ratio < 0.86 -> 0.75    # 83-86%：接近健康
      pool_ratio < 0.89 -> 0.8     # 86-89%：即将健康
      pool_ratio < 0.92 -> 0.85    # 89-92%：趋向健康
      
      # 健康区间：1%一档，最优控制
      pool_ratio < 0.93 -> 0.9     # 92-93%：健康边缘
      pool_ratio < 0.94 -> 0.95    # 93-94%：初期健康
      pool_ratio < 0.95 -> 1.0     # 94-95%：标准健康
      pool_ratio < 0.96 -> 1.0     # 95-96%：理想健康
      pool_ratio < 0.97 -> 1.0     # 96-97%：最佳健康
      pool_ratio < 0.98 -> 1.0     # 97-98%：完美健康
      
      # 超出健康区间：逐步鼓励中奖
      pool_ratio < 0.99 -> 1.05    # 98-99%：轻微鼓励
      pool_ratio < 1.00 -> 1.1     # 99-100%：适度鼓励
      pool_ratio < 1.02 -> 1.15    # 100-102%：明显鼓励
      pool_ratio < 1.05 -> 1.2     # 102-105%：强力鼓励
      pool_ratio < 1.10 -> 1.25    # 105-110%：高度鼓励
      pool_ratio < 1.15 -> 1.3     # 110-115%：重度鼓励
      pool_ratio < 1.25 -> 1.4     # 115-125%：极力鼓励
      pool_ratio < 1.50 -> 1.5     # 125-150%：最大鼓励
      true -> 1.6                  # > 150%：超强鼓励消耗
    end
  end

  # 通用频率计算（保留给需要直接调用的地方）
  defp calculate_recommended_frequency(pool_state) do
    case pool_state do
      :much_higher -> 120   # 每小时建议120次中奖（虚拟奖池过高，积极消耗）
      :slightly_higher -> 90
      :balanced -> 75       # 平衡状态每小时75次（大幅提升）
      :slightly_lower -> 60  # 追赶期保持高活跃度
      :much_lower -> 50      # 恢复期也要保持一定活跃度
    end
  end

  # 根据游戏类型计算推荐频率（特别对slotniu加强）
  defp calculate_recommended_frequency_for_game(game_type, pool_state) do
    base_frequency = calculate_recommended_frequency(pool_state)

    case game_type do
      "slotniu" ->
        # SlotNiu特別加强：在基础频率上增加50%，确保高活跃度
        trunc(base_frequency * 1.5)
      "slotcat" ->
        # SlotCat三个奖池，略微增加
        trunc(base_frequency * 1.2)
      _ ->
        # Slot777和其他游戏使用正常频率
        base_frequency
    end
  end

  defp generate_intelligent_schedule(pool_analysis, state) do
    current_time = System.system_time(:second)
    schedule_window = @schedule_generation_config.schedule_window_hours * 3600  # 转换为秒
    end_time = current_time + schedule_window

    game_types = ["slot777", "slotniu", "slotcat"]

    # 新策略：平均分布时间段，让三个游戏均匀交错
    all_scheduled_wins = generate_balanced_schedule(pool_analysis, current_time, end_time, state)

    # 按执行时间排序
    Enum.sort_by(all_scheduled_wins, & &1.execute_time)
  end

  # 新的平衡调度生成器 - 支持SlotCat三个独立奖池
  defp generate_balanced_schedule(pool_analysis, start_time, end_time, state) do
    time_window_hours = (end_time - start_time) / 3600

    # 处理普通游戏（单奖池）
    normal_games = ["slot777", "slotniu"]
    normal_game_counts = Enum.map(normal_games, fn game_type ->
      analysis = Map.get(pool_analysis, game_type)
      if analysis do
        win_count = calculate_win_count_by_state(analysis, time_window_hours)
        win_count = max(win_count, 8)
        {game_type, win_count, analysis}
      else
        {game_type, 0, nil}
      end
    end)

    # 处理SlotCat的三个独立奖池
    slotcat_pools = [:center, :left, :right]
    slotcat_game_counts = Enum.map(slotcat_pools, fn pool_id ->
      analysis_key = "slotcat_#{pool_id}"
      analysis = Map.get(pool_analysis, analysis_key)

      if analysis do
        win_count = calculate_win_count_by_state(analysis, time_window_hours)
        # SlotCat每个奖池最少保证4次中奖
        win_count = max(win_count, 4)

        # 创建特殊的game_type标识符，用于后续处理
        game_identifier = "slotcat_#{pool_id}"
        {game_identifier, win_count, analysis}
      else
        {"slotcat_#{pool_id}", 0, nil}
      end
    end)

    # 合并所有游戏的中奖数量
    game_win_counts = normal_game_counts ++ slotcat_game_counts

    # 总中奖数
    total_wins = Enum.reduce(game_win_counts, 0, fn {_, count, _}, acc -> acc + count end)

    if total_wins <= 0 do
      []
    else
      # 生成均匀分布的时间间隔
      time_span = end_time - start_time
      base_interval = max(time_span / total_wins, 30)  # 最小30秒间隔

      # 创建时间序列，交错分配给三个游戏
      Logger.info("🕒 [VIRTUAL_JACKPOT] 均衡调度: 总计#{total_wins}次中奖, 基础间隔#{trunc(base_interval)}秒, 时间窗口#{trunc(time_span/60)}分钟")
      scheduled_wins = generate_interleaved_schedule(game_win_counts, start_time, base_interval, state)

      # 验证时间分布
      if length(scheduled_wins) > 0 do
        first_time = Enum.min_by(scheduled_wins, & &1.execute_time).execute_time
        last_time = Enum.max_by(scheduled_wins, & &1.execute_time).execute_time
        span_minutes = trunc((last_time - first_time) / 60)
        Logger.info("✅ [VIRTUAL_JACKPOT] 时间分布验证: 跨度#{span_minutes}分钟, 平均间隔#{trunc(span_minutes*60/total_wins)}秒")

        # 统计每个游戏的中奖分布
        game_distribution = Enum.group_by(scheduled_wins, & &1.game_type)
        Enum.each(game_distribution, fn {game_type, wins} ->
          Logger.info("📈 [VIRTUAL_JACKPOT] #{game_type}: #{length(wins)}次中奖")
        end)
      end

      scheduled_wins
    end
  end

  # 交错生成调度 - 让三个游戏的中奖时间交错分布
  defp generate_interleaved_schedule(game_win_counts, start_time, base_interval, state) do
    # 创建游戏序列：重复循环三个游戏，直到所有中奖都分配完
    game_sequence = create_game_sequence(game_win_counts)

    # 为每个位置生成中奖记录
    game_sequence
    |> Enum.with_index()
    |> Enum.map(fn {game_type, index} ->
      # 计算执行时间：基础间隔 + 智能随机偏移
      # 偏移范围根据间隔大小动态调整，但限制在合理范围内
      max_offset = min(trunc(base_interval * 0.3), 60)  # 最多偏移间隔的30%或60秒
      random_offset = :rand.uniform(max_offset * 2 + 1) - max_offset - 1  # -max_offset到+max_offset
      execute_time = start_time + trunc(index * base_interval) + random_offset

      # 确保时间不会倒序或重叠
      execute_time = max(execute_time, start_time + index * 10)  # 保证最少10秒间隔

      # 生成虚拟中奖记录 - 支持SlotCat独立奖池
      case generate_virtual_win_for_game_type(state, game_type) do
        {:ok, win_record} ->
          # 确定实际的游戏类型（用于广播）
          actual_game_type = if String.starts_with?(game_type, "slotcat_") do
            "slotcat"
          else
            game_type
          end

          %{
            id: "balanced_#{System.system_time(:millisecond)}_#{:rand.uniform(9999)}",
            execute_time: execute_time,
            game_type: actual_game_type,  # 用于广播的游戏类型
            pool_id: extract_pool_id(game_type),  # SlotCat的奖池ID
            win_record: win_record,
            created_at: System.system_time(:second)
          }
        {:error, _} ->
          nil
      end
    end)
    |> Enum.filter(& &1 != nil)
  end

  # 创建交错的游戏序列
  defp create_game_sequence(game_win_counts) do
    # 创建每个游戏的列表
    game_lists = Enum.map(game_win_counts, fn {game_type, count, _} ->
      List.duplicate(game_type, count)
    end)

    # 交错合并三个列表
    interleave_lists(game_lists)
  end

  # 交错合并多个列表
  defp interleave_lists(lists) do
    interleave_lists_acc(lists, [])
  end

  defp interleave_lists_acc(lists, acc) do
    # 过滤掉空列表
    non_empty_lists = Enum.filter(lists, fn list -> length(list) > 0 end)

    if length(non_empty_lists) == 0 do
      Enum.reverse(acc)
    else
      # 从每个非空列表取第一个元素
      {new_elements, remaining_lists} = Enum.map(non_empty_lists, fn [head | tail] ->
        {head, tail}
      end) |> Enum.unzip()

      # 递归处理剩余元素
      interleave_lists_acc(remaining_lists, new_elements ++ acc)
    end
  end

  defp generate_wins_for_game(analysis, start_time, end_time, state) do
    if analysis.health_score < 0.3 do
      # 奖池太不健康，不安排中奖
      Logger.warning("⚠️ [VIRTUAL_JACKPOT] 奖池不健康，跳过调度: #{analysis.game_type}")
      []
    else
      # 计算需要生成的中奖数量
      time_window_hours = (end_time - start_time) / 3600
      total_wins_needed = trunc(analysis.recommended_wins_per_hour * time_window_hours * analysis.health_score)

      Logger.info("📅 [VIRTUAL_JACKPOT] 为 #{analysis.game_type} 计划 #{total_wins_needed} 个中奖 (未来1#{trunc(time_window_hours)}小时)")

      generate_scheduled_wins_for_period(analysis, start_time, end_time, total_wins_needed, state)
    end
  end

  defp generate_scheduled_wins_for_period(analysis, start_time, end_time, win_count, state) do
    if win_count <= 0 do
      []
    else
      {base_min, base_max} = @schedule_generation_config.base_intervals[analysis.game_type]
      frequency_modifier = @schedule_generation_config.frequency_modifiers[analysis.pool_state]

      # 计算调整后的间隔范围
      adjusted_min = trunc(base_min * frequency_modifier)
      adjusted_max = trunc(base_max * frequency_modifier)

      # 生成预定中奖时间点
      generate_win_times(start_time, end_time, win_count, adjusted_min, adjusted_max)
      |> Enum.map(fn execute_time ->
        # 为每个时间点生成中奖记录
        case generate_virtual_win_with_state(state, analysis.game_type) do
          {:ok, win_record} ->
            %{
              id: "scheduled_#{System.system_time(:millisecond)}_#{:rand.uniform(9999)}",
              execute_time: execute_time,
              game_type: analysis.game_type,
              win_record: win_record,
              created_at: System.system_time(:second)
            }
          {:error, _} ->
            # 如果生成失败，跳过这个时间点
            nil
        end
      end)
      |> Enum.filter(& &1 != nil)
    end
  end

  defp generate_win_times(start_time, end_time, win_count, min_interval, max_interval) do
    if win_count <= 0 do
      []
    else
      # 使用泊松分布生成更真实的时间间隔
      time_span = end_time - start_time
      average_interval = time_span / win_count

      # 限制平均间隔在合理范围内
      clamped_avg = max(min_interval, min(average_interval, max_interval))

      generate_poisson_times(start_time, end_time, clamped_avg, win_count)
    end
  end

  defp generate_poisson_times(start_time, end_time, avg_interval, max_count) do
    generate_poisson_times_acc(start_time, end_time, avg_interval, max_count, [], start_time)
  end

  defp generate_poisson_times_acc(_start_time, end_time, _avg_interval, max_count, acc, _current_time) when length(acc) >= max_count do
    Enum.take(acc, max_count)
  end

  defp generate_poisson_times_acc(_start_time, end_time, _avg_interval, _max_count, acc, current_time) when current_time >= end_time do
    acc
  end

  defp generate_poisson_times_acc(start_time, end_time, avg_interval, max_count, acc, current_time) do
    # 使用指数分布生成下一个间隔
    random_interval = trunc(-avg_interval * :math.log(:rand.uniform()))
    next_time = current_time + random_interval

    if next_time <= end_time do
      generate_poisson_times_acc(start_time, end_time, avg_interval, max_count, [next_time | acc], next_time)
    else
      acc
    end
  end

  # 检查奖池是否健康，可以进行中奖 - 动态健康检查
  defp pool_healthy_for_win?(nil), do: false
  defp pool_healthy_for_win?(pool_info) do
    # 获取虚拟奖池和真实奖池总量（处理SlotCat的特殊结构）
    {virtual_pool, real_pool} = if pool_info[:pools] do
      # SlotCat 使用三个奖池的总和
      slotcat_pools = pool_info.pools
      total_virtual = slotcat_pools.center.virtual_pool +
                     slotcat_pools.left.virtual_pool +
                     slotcat_pools.right.virtual_pool
      total_real = slotcat_pools.center.real_pool +
                  slotcat_pools.left.real_pool +
                  slotcat_pools.right.real_pool
      {total_virtual, total_real}
    else
      # 其他游戏使用单一奖池
      {pool_info.virtual_pool || 0, pool_info.real_pool || 1}
    end

    # 基本健康检查：虚拟奖池不能为负数或过低
    basic_healthy = virtual_pool > 0 and real_pool > 0

    # 动态健康检查：虚拟奖池应该至少是真实奖池的3%，最低1000
    min_healthy_amount = max(real_pool * 0.03, 1000)
    dynamic_healthy = virtual_pool >= min_healthy_amount

    basic_healthy and dynamic_healthy
  end

  # 根据奖池状态决定是否允许中奖 - 使用动态阈值
  defp pool_allows_win?(pool_info, pool_state) do
    # 获取虚拟奖池和真实奖池总量（处理SlotCat的特殊结构）
    {virtual_pool, real_pool} = if pool_info[:pools] do
      # SlotCat 使用三个奖池的总和
      slotcat_pools = pool_info.pools
      total_virtual = slotcat_pools.center.virtual_pool +
                     slotcat_pools.left.virtual_pool +
                     slotcat_pools.right.virtual_pool
      total_real = slotcat_pools.center.real_pool +
                  slotcat_pools.left.real_pool +
                  slotcat_pools.right.real_pool
      {total_virtual, total_real}
    else
      # 其他游戏使用单一奖池
      {pool_info.virtual_pool || 0, pool_info.real_pool || 1}
    end

    # 动态计算健康阈值：10%和30%的真实奖池
    moderate_threshold = real_pool * 0.10
    high_threshold = real_pool * 0.30

    case pool_state do
      # 虚拟奖池远超真实奖池：鼓励中奖以平衡
      :much_higher ->
        # 90%概率允许中奖
        :rand.uniform(100) <= 90

      # 虚拟奖池略高：正常中奖概率
      :slightly_higher ->
        # 70%概率允许中奖
        :rand.uniform(100) <= 70

      # 平衡状态：正常中奖概率
      :balanced ->
        # 60%概率允许中奖
        :rand.uniform(100) <= 60

      # 虚拟奖池略低：减少中奖，让奖池恢复
      :slightly_lower ->
        # 30%概率允许中奖，且需要奖池足够健康（真实奖池的10%以上）
        virtual_pool >= moderate_threshold and :rand.uniform(100) <= 30

      # 虚拟奖池远低：严格控制中奖
      :much_lower ->
        # 只有奖池很健康时才10%概率中奖（真实奖池的30%以上）
        virtual_pool >= high_threshold and :rand.uniform(100) <= 10
    end
  end

  defp determine_pool_state(state, game_type) do
    pool_info = Map.get(state.virtual_pools, game_type)
    if pool_info do
      {virtual_pool, real_pool} = case game_type do
        "slotcat" ->
          # SlotCat 使用三个奖池的总和
          if pool_info[:pools] do
            slotcat_pools = pool_info.pools
            total_virtual = slotcat_pools.center.virtual_pool +
                           slotcat_pools.left.virtual_pool +
                           slotcat_pools.right.virtual_pool
            total_real = slotcat_pools.center.real_pool +
                        slotcat_pools.left.real_pool +
                        slotcat_pools.right.real_pool
            {total_virtual, total_real}
          else
            {0, 1}
          end
        _ ->
          # 其他游戏使用单一奖池
          {pool_info.virtual_pool, pool_info.real_pool}
      end

      ratio = virtual_pool / max(real_pool, 1)
      cond do
        ratio > 1.3 -> :much_higher
        ratio > 1.1 -> :slightly_higher
        ratio >= 0.9 -> :balanced
        ratio >= 0.7 -> :slightly_lower
        true -> :much_lower
      end
    else
      :balanced
    end
  end

  defp generate_virtual_win_with_state(state, game_type) do
    # 根据奖池状态选择合适的中奖类型和金额
    pool_state = determine_pool_state(state, game_type)

    case select_win_strategy(game_type, pool_state, state) do
      {:ok, bet_amount, win_type} ->
        jackpot_amount = calculate_jackpot_amount_by_rules(game_type, bet_amount, win_type, state)

        game_specific_data = generate_game_specific_data(game_type, win_type, bet_amount)

        # 添加调试信息
        # Logger.info("🐛 [DEBUG] 生成#{game_type}虚拟中奖记录 - win_type: #{inspect(win_type)}, game_specific_data: #{inspect(game_specific_data)}")

        win_record = %{
          id: "virtual_#{System.system_time(:millisecond)}_#{:rand.uniform(9999)}",
          game_type: game_type,
          player_id: "fake_#{:rand.uniform(999999)}",
          player_name: RobotEntity.random_robot_name(),
          avatar_id: RobotEntity.random_robot_avatar(),
          avatar_url: "",
          jackpot_amount: jackpot_amount,
          bet_amount: bet_amount,
          jackpot_level: map_win_type_to_level(win_type),
          game_specific_data: game_specific_data,
          win_time: DateTime.utc_now(),
          is_virtual: true
        }

        {:ok, win_record}
      error ->
        error
    end
  end

  defp select_win_strategy(game_type, pool_state, state) do
    case game_type do
      "slot777" ->
        select_slot777_win_strategy(pool_state)
      "slotniu" ->
        select_slotniu_win_strategy(pool_state)
      "slotcat" ->
        select_slotcat_win_strategy(pool_state, state)
      _ ->
        {:error, "不支持的游戏类型"}
    end
  end

  defp select_slot777_win_strategy(pool_state) do
    # 第一步：先随机选择下注金额
    valid_single_line_bets = @slot777_rules.valid_single_line_bets
    single_line_bet = Enum.random(valid_single_line_bets)
    total_bet = @slot777_rules.single_to_total_bet[single_line_bet]

    # 第二步：从该下注金额对应的sevennum选项中随机选择
    available_sevens = Map.keys(@slot777_rules.percentage_table[single_line_bet] || %{})

    if length(available_sevens) > 0 do
      # 根据奖池状态调整sevennum的选择权重
      weighted_sevens = case pool_state do
        :much_higher ->
          # 虚拟奖池过高，偏向大奖（5个七）
          expand_weighted_list(available_sevens, fn seven -> if seven == 5, do: 4, else: 1 end)
        :slightly_higher ->
          # 略高，大奖优先
          expand_weighted_list(available_sevens, fn seven -> if seven >= 4, do: 3, else: 1 end)
        :balanced ->
          # 平衡状态，正常分布
          expand_weighted_list(available_sevens, fn _seven -> 2 end)
        :slightly_lower ->
          # 略低，减少大奖频率
          expand_weighted_list(available_sevens, fn seven -> if seven <= 3, do: 3, else: 1 end)
        :much_lower ->
          # 过低，主要是小奖
          expand_weighted_list(available_sevens, fn seven -> if seven == 3, do: 4, else: 1 end)
      end

      seven_count = Enum.random(weighted_sevens)
      {:ok, total_bet, seven_count}
    else
      # 如果百分比表有问题，使用默认值
      {:ok, 9000, 3}
    end
  end

  # 辅助函数：根据权重展开列表
  defp expand_weighted_list(items, weight_fn) do
    Enum.flat_map(items, fn item ->
      weight = weight_fn.(item)
      List.duplicate(item, weight)
    end)
  end

  defp select_slotniu_win_strategy(pool_state) do
    valid_bets = @slotniu_rules.valid_bets
    bet_amount = Enum.random(valid_bets)

    # 根据下注金额确定可用的牛头数量范围
    available_bull_counts = Map.keys(@slotniu_rules.multipliers[bet_amount])

    # 根据奖池状态选择牛头数量 - 大幅提高大奖概率
    bull_count = case pool_state do
      :much_higher ->
        # 生成大奖：70%大奖(7-9牛)，30%中奖(5-6牛)
        weight_random = :rand.uniform(10)
        if weight_random <= 7 do
          high_bulls = Enum.filter(available_bull_counts, &(&1 >= 7))
          if length(high_bulls) > 0, do: Enum.random(high_bulls), else: Enum.random(available_bull_counts)
        else
          mid_bulls = Enum.filter(available_bull_counts, &(&1 >= 5 and &1 <= 6))
          if length(mid_bulls) > 0, do: Enum.random(mid_bulls), else: Enum.random(available_bull_counts)
        end
      :slightly_higher ->
        # 60%大奖(7-9牛)，40%中奖(5-6牛)
        weight_random = :rand.uniform(10)
        if weight_random <= 6 do
          high_bulls = Enum.filter(available_bull_counts, &(&1 >= 7))
          if length(high_bulls) > 0, do: Enum.random(high_bulls), else: Enum.random(available_bull_counts)
        else
          mid_bulls = Enum.filter(available_bull_counts, &(&1 >= 5 and &1 <= 6))
          if length(mid_bulls) > 0, do: Enum.random(mid_bulls), else: Enum.random(available_bull_counts)
        end
      :balanced ->
        # 正常分布：60%大奖(7-9牛)，30%中奖(5-6牛)，10%小奖(3-4牛)
        weight_random = :rand.uniform(10)
        cond do
          weight_random <= 6 -> # 60% 大奖
            high_bulls = Enum.filter(available_bull_counts, &(&1 >= 7))
            if length(high_bulls) > 0, do: Enum.random(high_bulls), else: Enum.random(available_bull_counts)
          weight_random <= 9 -> # 30% 中奖
            mid_bulls = Enum.filter(available_bull_counts, &(&1 >= 5 and &1 <= 6))
            if length(mid_bulls) > 0, do: Enum.random(mid_bulls), else: Enum.random(available_bull_counts)
          true -> # 10% 小奖
            low_bulls = Enum.filter(available_bull_counts, &(&1 <= 4))
            if length(low_bulls) > 0, do: Enum.random(low_bulls), else: Enum.random(available_bull_counts)
        end
      :slightly_lower ->
        # 40%大奖(7-9牛)，50%中奖(5-6牛)，10%小奖(3-4牛)
        weight_random = :rand.uniform(10)
        cond do
          weight_random <= 4 -> # 40% 大奖
            high_bulls = Enum.filter(available_bull_counts, &(&1 >= 7))
            if length(high_bulls) > 0, do: Enum.random(high_bulls), else: Enum.random(available_bull_counts)
          weight_random <= 9 -> # 50% 中奖
            mid_bulls = Enum.filter(available_bull_counts, &(&1 >= 5 and &1 <= 6))
            if length(mid_bulls) > 0, do: Enum.random(mid_bulls), else: Enum.random(available_bull_counts)
          true -> # 10% 小奖
            low_bulls = Enum.filter(available_bull_counts, &(&1 <= 4))
            if length(low_bulls) > 0, do: Enum.random(low_bulls), else: Enum.random(available_bull_counts)
        end
      :much_lower ->
        # 20%大奖(7-9牛)，60%中奖(5-6牛)，20%小奖(3-4牛)
        weight_random = :rand.uniform(10)
        cond do
          weight_random <= 2 -> # 20% 大奖
            high_bulls = Enum.filter(available_bull_counts, &(&1 >= 7))
            if length(high_bulls) > 0, do: Enum.random(high_bulls), else: Enum.random(available_bull_counts)
          weight_random <= 8 -> # 60% 中奖
            mid_bulls = Enum.filter(available_bull_counts, &(&1 >= 5 and &1 <= 6))
            if length(mid_bulls) > 0, do: Enum.random(mid_bulls), else: Enum.random(available_bull_counts)
          true -> # 20% 小奖
            low_bulls = Enum.filter(available_bull_counts, &(&1 <= 4))
            if length(low_bulls) > 0, do: Enum.random(low_bulls), else: Enum.random(available_bull_counts)
        end
    end

    jackpot_level = Enum.random(@slotniu_rules.jackpot_levels)
    {:ok, bet_amount, {bull_count, jackpot_level}}
  end

  defp select_slotcat_win_strategy(pool_state, state) do
    # SlotCat需要为每个奖池独立处理，这里只是选择一个奖池进行中奖
    # 实际的三奖池独立逻辑在调度生成时处理
    valid_bets = @slotcat_rules.valid_total_bets
    bet_amount = Enum.random(valid_bets)

    # 这个函数现在用于生成单个奖池的中奖策略
    # pool_state 参数实际上应该是特定奖池的状态
    {selected_pool, jackpot_count} = case pool_state do
      :much_higher ->
        # 奖池过高：生成大奖消耗
        pool_choice = Enum.random([:center, :center, :right, :left])
        jackpot_count = case pool_choice do
          :center -> 5
          :right -> 4
          :left -> 3
        end
        {pool_choice, jackpot_count}

      :slightly_higher ->
        # 略高：平衡分布
        pool_choice = Enum.random([:center, :right, :right, :left])
        jackpot_count = case pool_choice do
          :center -> 5
          :right -> 4
          :left -> 3
        end
        {pool_choice, jackpot_count}

      :balanced ->
        # 平衡：正常分布
        pool_choice = Enum.random([:center, :right, :left, :left])
        jackpot_count = case pool_choice do
          :center -> 5
          :right -> 4
          :left -> 3
        end
        {pool_choice, jackpot_count}

      :slightly_lower ->
        # 略低：偏向中小奖
        pool_choice = Enum.random([:right, :left, :left])
        jackpot_count = case pool_choice do
          :right -> 4
          :left -> 3
        end
        {pool_choice, jackpot_count}

      :much_lower ->
        # 过低：主要小奖
        {:left, 3}
    end

    {:ok, bet_amount, {selected_pool, jackpot_count}}
  end

  # 新增：为SlotCat特定奖池生成中奖策略
  defp select_slotcat_pool_win_strategy(pool_id, pool_state) do
    valid_bets = @slotcat_rules.valid_total_bets
    bet_amount = Enum.random(valid_bets)

    # 根据奖池ID和状态确定中奖类型
    jackpot_count = case {pool_id, pool_state} do
      {:center, _} -> 5  # center奖池始终是5个jackpot
      {:right, _} -> 4   # right奖池始终是4个jackpot
      {:left, _} -> 3    # left奖池始终是3个jackpot
    end

    {:ok, bet_amount, {pool_id, jackpot_count}}
  end

  # 基于健康度分数智能选择最佳SlotCat奖池
  defp select_best_slotcat_pool(pool_scores) do
    # 优先选择需要消耗的奖池（略高于真实奖池的），然后是健康状态的奖池

    # 分类奖池：超过100%的需要消耗，90%-100%的是健康状态
    {high_pools, healthy_pools, low_pools} = Enum.reduce(pool_scores, {[], [], []}, fn {pool, score, ratio}, {high, healthy, low} ->
      cond do
        ratio >= 1.00 and ratio <= 1.25 ->  # 100%-125%，需要消耗
          {[{pool, score, ratio} | high], healthy, low}
        ratio >= 0.90 and ratio < 1.00 ->   # 90%-100%，健康状态
          {high, [{pool, score, ratio} | healthy], low}
        true ->                              # < 90%，需要保护
          {high, healthy, [{pool, score, ratio} | low]}
      end
    end)

    cond do
      # 优先选择高于真实奖池的（需要消耗）
      length(high_pools) > 0 ->
        {best_pool, _, _} = Enum.max_by(high_pools, fn {_pool, _score, ratio} -> ratio end)
        best_pool

      # 其次选择健康状态的
      length(healthy_pools) > 0 ->
        {best_pool, _, _} = Enum.max_by(healthy_pools, fn {_pool, score, _ratio} -> score end)
        best_pool

      # 最后无奈选择最高的（虽然偏低）
      true ->
        {best_pool, _, _} = Enum.max_by(pool_scores, fn {_pool, score, _ratio} -> score end)
        best_pool
    end
  end

  # 基于健康度分数的加权随机选择
  defp weighted_selection(pool_scores) do
    total_weight = Enum.sum(Enum.map(pool_scores, fn {_pool, score, _ratio} -> max(score, 0.1) end))

    random_value = :rand.uniform() * total_weight

    {selected_pool, _} = Enum.reduce_while(pool_scores, {nil, 0}, fn {pool, score, _ratio}, {_acc_pool, acc_weight} ->
      new_weight = acc_weight + max(score, 0.1)
      if random_value <= new_weight do
        {:halt, {pool, new_weight}}
      else
        {:cont, {pool, new_weight}}
      end
    end)

    selected_pool || :center  # 默认返回center
  end

  defp calculate_jackpot_amount_by_rules(game_type, bet_amount, win_type, state) do
    case game_type do
      "slot777" ->
        virtual_pool = get_in(state, [:virtual_pools, game_type, :virtual_pool])
        calculate_slot777_jackpot(bet_amount, win_type, virtual_pool)
      "slotniu" ->
        virtual_pool = get_in(state, [:virtual_pools, game_type, :virtual_pool])
        calculate_slotniu_jackpot(bet_amount, win_type, virtual_pool)
      "slotcat" ->
        # SlotCat 使用特殊处理，传递整个状态
        calculate_slotcat_jackpot(bet_amount, win_type, state)
      _ ->
        0
    end
  end

  defp calculate_slot777_jackpot(total_bet, seven_count, virtual_pool) do
    # 获取单线下注金额：需要除以100转换为₹单位，再除以9（线数）
    single_line_bet = trunc(total_bet / 100 / 9)

    # 从百分比表获取中奖百分比
    percentage = get_in(@slot777_rules.percentage_table, [single_line_bet, seven_count]) || 0

    # 计算中奖金额：奖池金额 * 百分比 / 100
    trunc(virtual_pool * percentage / 100)
  end

  defp calculate_slotniu_jackpot(bet_amount, {bull_count, jackpot_level}, virtual_pool) do
    # 获取倍率
    multiplier = get_in(@slotniu_rules.multipliers, [bet_amount, bull_count]) || 0

    # SlotNiu特殊计算：(奖池金额 ÷ 10000) × 倍率 × jackpot_level
    base_amount = trunc(virtual_pool / 10000) * multiplier
    trunc(base_amount * jackpot_level)
  end

  defp calculate_slotcat_jackpot(bet_amount, {pool_type, jackpot_count}, state) do
    # 获取对应单个虚拟奖池的金额
    single_virtual_pool_amount = get_slotcat_single_virtual_pool_amount(state, pool_type)

    # 根据下注金额和奖池类型计算百分比
    percentage = get_in(@slotcat_rules.percentage_table, [bet_amount, pool_type]) || 0

    # 计算中奖金额：单个虚拟奖池金额 * 百分比 / 100
    jackpot_amount = trunc(single_virtual_pool_amount * percentage / 100)

    # 确保中奖金额合理（至少是下注的2倍）
    min_amount = bet_amount * 2
    max(jackpot_amount, min_amount)
  end

  # 获取SlotCat单个虚拟奖池金额
  defp get_slotcat_single_virtual_pool_amount(state, pool_type) do
    slotcat_pools = get_in(state, [:virtual_pools, "slotcat", :pools])
    if slotcat_pools && slotcat_pools[pool_type] do
      slotcat_pools[pool_type].virtual_pool
    else
      # 如果虚拟奖池不存在，回退到真实奖池
      Teen.GameSystem.JackpotManager.get_jackpot_balance(42, pool_type)
    end
  end

  # 获取SlotCat单个奖池金额（保持原始金额）
  defp get_slotcat_single_pool_amount(pool_type) do
    Teen.GameSystem.JackpotManager.get_jackpot_balance(42, pool_type)
  end

  # ==================== 辅助函数 ====================

  defp add_virtual_win_to_state(state, game_type, win_record) do
    game_records = Map.get(state.virtual_records, game_type, [])
    updated_records = [win_record | game_records] |> Enum.take(@max_records_per_game)
    new_virtual_records = Map.put(state.virtual_records, game_type, updated_records)
    %{state | virtual_records: new_virtual_records}
  end

  defp apply_virtual_win_impact(state, game_type, win_record) do
    case game_type do
      "slotcat" ->
        # SlotCat 需要从特定的奖池中扣除
        # 优先使用win_record中的pool_id，否则从game_specific_data获取
        pool_type = Map.get(win_record, :pool_id) ||
                   Map.get(win_record.game_specific_data, :pool_type, :center)
        slotcat_pools = get_in(state, [:virtual_pools, "slotcat", :pools])

        if slotcat_pools && slotcat_pools[pool_type] do
          current_pool = slotcat_pools[pool_type].virtual_pool
          new_pool_amount = max(current_pool - win_record.jackpot_amount, 0)

          Logger.info("🎰 [VIRTUAL_JACKPOT] SlotCat #{pool_type}奖池中奖: #{win_record.jackpot_amount}, 剩余: #{new_pool_amount}")

          # 更新特定奖池
          updated_pool_data = %{slotcat_pools[pool_type] | virtual_pool: new_pool_amount}
          updated_slotcat_pools = Map.put(slotcat_pools, pool_type, updated_pool_data)
          updated_pools = put_in(state.virtual_pools, ["slotcat", :pools], updated_slotcat_pools)

          # 计算新的显示奖池（三个奖池的总和）
          total_virtual = updated_slotcat_pools.center.virtual_pool +
                         updated_slotcat_pools.left.virtual_pool +
                         updated_slotcat_pools.right.virtual_pool
          updated_display = Map.put(state.display_jackpots, game_type, total_virtual)

          # 注释掉中奖后的立即广播，避免与定时更新冲突
          # Logger.info("🐛 [DEBUG_WIN_IMPACT] SlotCat中奖后广播，显示奖池=#{total_virtual}")
          # broadcast_jackpot_updates(updated_display, updated_pools)

          %{state |
            virtual_pools: updated_pools,
            display_jackpots: updated_display
          }
        else
          # 如果奖池数据不存在，返回原状态
          state
        end

      _ ->
        # 其他游戏的单一奖池逻辑
        current_virtual_pool = get_in(state, [:virtual_pools, game_type, :virtual_pool])
        new_virtual_pool = max(current_virtual_pool - win_record.jackpot_amount, 0)

        # 更新虚拟奖池
        updated_pools = put_in(state.virtual_pools, [game_type, :virtual_pool], new_virtual_pool)

        # 更新显示奖池
        updated_display = Map.put(state.display_jackpots, game_type, new_virtual_pool)

        # 注释掉中奖后的立即广播，避免与定时更新冲突
        # Logger.info("🐛 [DEBUG_WIN_IMPACT] #{game_type}中奖后广播，显示奖池=#{new_virtual_pool}")
        # broadcast_jackpot_updates(updated_display, updated_pools)

        %{state |
          virtual_pools: updated_pools,
          display_jackpots: updated_display
        }
    end
  end

  defp map_win_type_to_level(win_type) do
    case win_type do
      # Slot777 - 新格式：直接的数字
      5 -> 3  # 5个7，最高级别
      4 -> 2  # 4个7，中级别
      3 -> 1  # 3个7，低级别
      # Slot777 - 旧格式：元组（兼容性）
      {5, _} -> 3  # 5个7，最高级别
      {4, _} -> 2  # 4个7，中级别
      {3, _} -> 1  # 3个7，低级别
      # SlotNiu格式
      {_, jackpot_level} when is_integer(jackpot_level) -> jackpot_level
      # SlotCat格式
      {:center, _} -> 3  # center奖池，最高级别
      {:right, _} -> 2   # right奖池，中级别
      {:left, _} -> 1    # left奖池，低级别
      # 其他格式
      :grand -> 3
      :major -> 2
      :minor -> 1
      _ -> 1
    end
  end

  defp generate_game_specific_data(game_type, win_type, bet_amount) do
    case game_type do
      "slot777" ->
        # win_type 现在是单个数字（七的数量）
        seven_count = if is_integer(win_type), do: win_type, else: elem(win_type, 0)

        # 严格按照百分比表：sevennum 必须等于 seven_count
        # 因为 seven_count 是根据下注金额和百分比表严格选择的，不允许随机变化
        sevennum = seven_count

        %{
          seven_count: seven_count,        # 内部使用
          sevennum: sevennum,              # 历史记录API期待的字段名，严格等于seven_count
          single_line_bet: trunc(bet_amount / 100 / 9)
        }
      "slotniu" ->
        {bull_count, jackpot_level} = win_type
        # 获取对应的倍数
        multiplier = get_in(@slotniu_rules.multipliers, [bet_amount, bull_count]) || 0

        # 严格按照规则：niunum 必须等于 bull_count
        # 因为 bull_count 是根据下注金额和倍率表严格选择的，不允许随机变化
        niunum = bull_count

        %{
          bull_count: bull_count,
          niunum: niunum,               # 历史记录API期待的字段名，严格等于bull_count
          jackpot_level: jackpot_level,
          mult: multiplier              # 倍数字段
        }
      "slotcat" ->
        {pool_type, jackpot_count} = win_type

        # 严格按照规则：jackpotnum 必须等于 jackpot_count
        # 因为 jackpot_count 是根据奖池类型和下注金额的百分比表严格选择的，不允许随机变化
        jackpotnum = jackpot_count

        %{
          pool_type: pool_type,
          jackpot_count: jackpot_count,
          jackpotnum: jackpotnum        # 历史记录API期待的字段名，严格等于jackpot_count
        }
      _ ->
        %{}
    end
  end

  # ==================== 广播函数 ====================

  defp broadcast_virtual_win(game_type, win_record) do
    try do
      # 添加调试信息
      Logger.info("🐛 [DEBUG] 广播#{game_type}虚拟中奖 - game_specific_data: #{inspect(win_record.game_specific_data)}")

      # 构建游戏特定的广播数据格式
      jackpot_record = build_game_specific_broadcast_data(game_type, win_record)

      Logger.info("🐛 [DEBUG] 广播数据 - sevennum/bull_count: #{inspect(Map.get(jackpot_record, "sevennum") || Map.get(jackpot_record, "bull_count"))}")

      # 使用旧的广播方式：通过RoomManager.broadcast_to_game_type发送{:broadcast, message}
      atom_game_type = String.to_atom(game_type)
      sub_id = case game_type do
        "slot777" -> 1006  # SC_SLOT777_JPAWARD_P - Jackpot中奖通知
        "slotniu" -> 1008  # SC_SLOTNIU_JPAWARD_P - Jackpot中奖通知
        "slotcat" -> 1006  # SC_SLOTCAT_JPAWARD_P - Jackpot中奖通知
        _ -> 1006
      end

      protocol_message = %{
        "mainId" => 5,
        "subId" => sub_id,
        "data" => jackpot_record
      }

      case Cypridina.RoomSystem.RoomManager.broadcast_to_game_type(atom_game_type, {:broadcast, protocol_message}) do
        {:ok, %{success: success_count, total: total_count}} ->
          # Logger.info(
          #   "📢 [VIRTUAL_JACKPOT] 虚拟中奖广播成功: 游戏类型=#{game_type}, 协议=#{sub_id}, 成功房间数=#{success_count}/#{total_count}"
          # )
          # Logger.info(
          #   "📢 [VIRTUAL_JACKPOT] 广播内容: 玩家 #{jackpot_record["name"]} 虚拟中得 #{jackpot_record["winscore"]} 大奖"
          # )
          :ok
        {:error, reason} ->
          Logger.error("❌ [VIRTUAL_JACKPOT] 虚拟中奖广播失败: #{inspect(reason)}")
      end
    rescue
      e ->
        Logger.error("❌ [VIRTUAL_JACKPOT] 广播虚拟中奖失败: #{inspect(e)}")
    end
  end

  # 构建游戏特定的广播数据
  defp build_game_specific_broadcast_data(game_type, win_record) do
    # 基础广播数据（所有游戏共用）
    base_data = %{
      "userid" => win_record.player_id,
      "name" => win_record.player_name,
      "headid" => win_record.avatar_id,
      "wxheadurl" => win_record.avatar_url,
      "winscore" => win_record.jackpot_amount,
      "time" => DateTime.to_unix(win_record.win_time, :millisecond)
    }

    # 根据游戏类型添加特定字段
    case game_type do
      "slot777" ->
        # Slot777: 添加sevennum字段 - 优先使用sevennum字段
        sevennum = Map.get(win_record.game_specific_data, :sevennum) ||
                   Map.get(win_record.game_specific_data, :seven_count, 5)
        Map.merge(base_data, %{
          "sevennum" => sevennum,
          "bet" => win_record.bet_amount
        })

      "slotniu" ->
        # SlotNiu: 添加niunum(显示用), bull_count(内部用), jackpot_level, mult字段
        niunum = Map.get(win_record.game_specific_data, :niunum) ||
                 Map.get(win_record.game_specific_data, :bull_count, 7)
        Map.merge(base_data, %{
          "niunum" => niunum,  # 历史记录API期待的字段名
          "bull_count" => Map.get(win_record.game_specific_data, :bull_count, 7),
          "jackpotlevel" => Map.get(win_record.game_specific_data, :jackpot_level, 1),
          "mult" => Map.get(win_record.game_specific_data, :mult, 100),
          "betamount" => win_record.bet_amount
        })

      "slotcat" ->
        # SlotCat: 优先使用win_record中的pool_id，否则从game_specific_data获取
        pool_type = Map.get(win_record, :pool_id) ||
                   Map.get(win_record.game_specific_data, :pool_type, :center)
        # 优先使用智能生成的jackpotnum字段
        jackpotnum = Map.get(win_record.game_specific_data, :jackpotnum) ||
                     Map.get(win_record.game_specific_data, :jackpot_count, 5)

        Map.merge(base_data, %{
          "jackpotnum" => jackpotnum,  # 历史记录API期待的字段名，智能生成
          "pool_type" => pool_type,
          "bet" => win_record.bet_amount
        })

      _ ->
        # 其他游戏使用基础数据
        base_data
    end
  end

  defp broadcast_jackpot_updates(display_jackpots, virtual_pools) do
    try do
      # 为每种游戏类型广播奖池更新
      game_types = ["slot777", "slotniu", "slotcat"]

      Enum.each(game_types, fn game_type ->
        atom_game_type = String.to_atom(game_type)
        # 使用 display_jackpots 中的显示值（这个值在广播前已经更新过）
        jackpot_amount = Map.get(display_jackpots, game_type, 0)

        # 使用统一的协议消息构建函数，支持SlotCat的三个奖池
        # 对于SlotCat，会直接从virtual_pools中获取最新的三个奖池数据
        protocol_message = build_jackpot_protocol_message(game_type, jackpot_amount, virtual_pools)
        
        # 临时调试日志
        if game_type == "slotcat" do
          data = protocol_message["data"]
          Logger.info("🐛 [DEBUG_BROADCAST] SlotCat广播数据: 中间=#{data["jackpot"]}, 左边=#{data["jackpotleft"]}, 右边=#{data["jackpotright"]}")
        else
          Logger.info("🐛 [DEBUG_BROADCAST] #{game_type}广播数据: 奖池=#{jackpot_amount}")
        end

        case Cypridina.RoomSystem.RoomManager.broadcast_to_game_type(atom_game_type, {:broadcast, protocol_message}) do
          {:ok, %{success: success_count, total: total_count}} ->
            #构建日志信息，为SlotCat显示三个奖池数据
            # log_info = case game_type do
            #   "slotcat" ->
            #     data = protocol_message["data"]
            #     "游戏类型=#{game_type}, 协议=#{protocol_message["subId"]}, 中间奖池=#{data["jackpot"]}, 左边奖池=#{data["jackpotleft"]}, 右边奖池=#{data["jackpotright"]}, 成功房间数=#{success_count}/#{total_count}"
            #   _ ->
            #     "游戏类型=#{game_type}, 协议=#{protocol_message["subId"]}, 奖池=#{jackpot_amount}, 成功房间数=#{success_count}/#{total_count}"
            # end

            # Logger.info("📢 [VIRTUAL_JACKPOT] 奖池更新广播成功: #{log_info}")
              :ok
          {:error, reason} ->
            Logger.error("❌ [VIRTUAL_JACKPOT] 奖池更新广播失败: 游戏类型=#{game_type}, 原因=#{inspect(reason)}")
        end
      end)
    rescue
      e ->
        Logger.error("❌ [VIRTUAL_JACKPOT] 广播奖池更新异常: #{inspect(e)}")
    end
  end

  # ==================== 定时任务 ====================

  defp schedule_pool_update do
    Process.send_after(__MODULE__, :update_virtual_pools, @pool_update_interval)
  end

  defp schedule_win_execution do
    # 每5秒检查一次是否有需要执行的中奖
    Process.send_after(__MODULE__, :execute_scheduled_wins, 5_000)
  end

  defp schedule_real_pool_sync do
    # 每60秒同步一次真实奖池余额
    Process.send_after(__MODULE__, :sync_real_pools, @real_pool_sync_interval)
  end

  defp schedule_schedule_regeneration do
    # 每30分钟重新生成一次调度
    interval = @schedule_generation_config.reschedule_interval_minutes * 60 * 1000
    Process.send_after(__MODULE__, :regenerate_schedule, interval)
  end

  # ==================== 工具函数 ====================

  defp get_current_jackpot_amount(game_type) do
    # 使用真实的奖池查询函数，不除以100，保持原始金额
    case game_type do
      "slot777" ->
        # Slot777 使用 main 奖池
        Teen.GameSystem.JackpotManager.get_jackpot_balance(40, :main)

      "slotniu" ->
        # SlotNiu 使用 jackpot 奖池
        Teen.GameSystem.JackpotManager.get_jackpot_balance(41, :jackpot)

      "slotcat" ->
        # SlotCat 使用三个奖池的总和
        center = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :center)
        left = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :left)
        right = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :right)
        center + left + right

      _ ->
        # 默认使用 Slot777 的方式
        Teen.GameSystem.JackpotManager.get_jackpot_balance(40, :main)
    end
  end

  # 获取SlotCat三个奖池的真实余额
  defp get_slotcat_real_jackpot_balances() do
    center = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :center)
    left = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :left)
    right = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :right)

    %{
      center: center,
      left: left,
      right: right
    }
  end

  # 构建不同游戏的奖池协议消息
  defp build_jackpot_protocol_message(game_type, display_jackpot, virtual_pools \\ nil) do
    case game_type do
      "slot777" ->
        # Slot777: SC_SLOT777_JACKPOT_P = 1005, 单个jackpot字段
        %{
          "mainId" => 5,
          "subId" => 1005,
          "data" => %{
            "jackpot" => display_jackpot
          }
        }

      "slotniu" ->
        # SlotNiu: SC_SLOTNIU_JACKPOT_P = 1007, 单个jackpot字段
        %{
          "mainId" => 5,
          "subId" => 1007,
          "data" => %{
            "jackpot" => display_jackpot
          }
        }

      "slotcat" ->
        # SlotCat: SC_SLOTCAT_JACKPOT_P = 1005, 三个奖池字段
        # 使用三个虚拟奖池的余额
        virtual_balances = get_slotcat_virtual_jackpot_balances(virtual_pools)
        %{
          "mainId" => 5,
          "subId" => 1005,
          "data" => %{
            "jackpot" => virtual_balances.center,      # 中间虚拟奖池
            "jackpotleft" => virtual_balances.left,    # 左边虚拟奖池
            "jackpotright" => virtual_balances.right   # 右边虚拟奖池
          }
        }

      _ ->
        # 默认使用简单格式
        %{
          "mainId" => 5,
          "subId" => 1005,
          "data" => %{
            "jackpot" => display_jackpot
          }
        }
    end
  end

  # 获取SlotCat三个虚拟奖池的余额
  defp get_slotcat_virtual_jackpot_balances(virtual_pools) do
    # 如果传入了虚拟奖池数据，使用传入的数据
    if virtual_pools && virtual_pools["slotcat"] && virtual_pools["slotcat"][:pools] do
      slotcat_pools = virtual_pools["slotcat"][:pools]
      balances = %{
        center: slotcat_pools.center.virtual_pool,
        left: slotcat_pools.left.virtual_pool,
        right: slotcat_pools.right.virtual_pool
      }
      Logger.info("🐛 [DEBUG_DATA_SOURCE] 使用虚拟奖池数据: #{inspect(balances)}")
      balances
    else
      # 如果没有传入数据，回退到真实奖池余额
      real_balances = get_slotcat_real_jackpot_balances()
      Logger.info("🐛 [DEBUG_DATA_SOURCE] 回退到真实奖池数据: #{inspect(real_balances)}")
      real_balances
    end
  end

  # ==================== 真实奖池同步逻辑 ====================

  # 同步所有游戏的真实奖池余额
  defp sync_all_real_pools(state) do
    try do
      Logger.info("🔄 [REAL_POOL_SYNC] 开始同步所有游戏的真实奖池余额...")

      game_types = ["slot777", "slotniu", "slotcat"]
      updated_pools = Enum.reduce(game_types, state.virtual_pools, fn game_type, pools ->
        sync_single_game_real_pool(pools, game_type)
      end)

      Logger.info("✅ [REAL_POOL_SYNC] 所有真实奖池同步完成")

      %{state | virtual_pools: updated_pools}
    catch
      error ->
        Logger.error("❌ [REAL_POOL_SYNC] 同步真实奖池时发生错误: #{inspect(error)}")
        state
    end
  end

  # 同步单个游戏的真实奖池
  defp sync_single_game_real_pool(pools, game_type) do
    case game_type do
      "slotcat" ->
        # SlotCat 需要分别同步三个奖池
        slotcat_state = Map.get(pools, "slotcat")
        if slotcat_state && slotcat_state[:pools] do
          updated_slotcat_pools = Enum.reduce([:center, :left, :right], slotcat_state.pools, fn pool_id, pools_acc ->
            current_pool = pools_acc[pool_id]
            if current_pool do
              # 获取最新的真实奖池余额
              new_real_balance = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, pool_id)
              old_real_balance = current_pool.real_pool

              # 如果真实奖池发生了变化，记录日志
              if new_real_balance != old_real_balance do
                Logger.info("🔄 [REAL_POOL_SYNC] SlotCat #{pool_id} 奖池更新: #{old_real_balance} -> #{new_real_balance}")
              end

              # 更新真实奖池余额
              updated_pool = %{current_pool | real_pool: new_real_balance}
              Map.put(pools_acc, pool_id, updated_pool)
            else
              pools_acc
            end
          end)

          # 更新 SlotCat 的奖池结构
          updated_slotcat_state = %{slotcat_state | pools: updated_slotcat_pools}
          Map.put(pools, "slotcat", updated_slotcat_state)
        else
          pools
        end

      _ ->
        # 其他游戏的单一奖池同步
        pool_state = Map.get(pools, game_type)
        if pool_state do
          # 获取最新的真实奖池余额
          new_real_balance = get_current_jackpot_amount(game_type)
          old_real_balance = pool_state.real_pool

          # 如果真实奖池发生了变化，记录日志
          if new_real_balance != old_real_balance do
            Logger.info("🔄 [REAL_POOL_SYNC] #{game_type} 奖池更新: #{old_real_balance} -> #{new_real_balance}")
          end

          # 更新真实奖池余额
          updated_pool_state = %{pool_state | real_pool: new_real_balance}
          Map.put(pools, game_type, updated_pool_state)
        else
          pools
        end
    end
  end

  # ==================== 极端差异自动调整功能 ====================

  # 初始化极端差异调整状态
  defp init_extreme_adjustments do
    %{
      # 当前正在调整的游戏
      active_adjustments: %{},
      # 调整历史记录（用于防止频繁调整）
      adjustment_history: %{}
    }
  end

  # 检查是否需要极端差异调整
  defp check_extreme_adjustment_needed(state) do
    game_types = ["slot777", "slotniu", "slotcat"]
    
    Enum.each(game_types, fn game_type ->
      check_single_game_extreme_adjustment(state, game_type)
    end)
    
    state
  end

  # 检查单个游戏是否需要极端差异调整
  defp check_single_game_extreme_adjustment(state, game_type) do
    pool_state = Map.get(state.virtual_pools, game_type)
    active_adjustment = get_in(state, [:extreme_adjustments, :active_adjustments, game_type])
    
    if pool_state && !active_adjustment do
      case game_type do
        "slotcat" ->
          # SlotCat需要检查三个奖池
          check_slotcat_extreme_adjustment(state, pool_state)
        _ ->
          # 其他游戏检查单一奖池
          check_single_pool_extreme_adjustment(state, game_type, pool_state)
      end
    end
  end

  # 检查单一奖池是否需要极端调整
  defp check_single_pool_extreme_adjustment(state, game_type, pool_state) do
    real_pool = pool_state.real_pool
    virtual_pool = pool_state.virtual_pool
    
    if real_pool > 0 do
      ratio = virtual_pool / real_pool
      threshold = @extreme_adjustment_config.trigger_threshold
      
      # 检查是否超过触发阈值
      if abs(ratio - 1.0) > threshold do
        trigger_extreme_adjustment(state, game_type, real_pool, virtual_pool, ratio)
      end
    end
  end

  # 检查SlotCat三个奖池的极端调整需求
  defp check_slotcat_extreme_adjustment(state, slotcat_state) do
    if slotcat_state[:pools] do
      Enum.each([:center, :left, :right], fn pool_id ->
        pool_data = slotcat_state.pools[pool_id]
        if pool_data do
          real_pool = pool_data.real_pool
          virtual_pool = pool_data.virtual_pool
          
          if real_pool > 0 do
            ratio = virtual_pool / real_pool
            threshold = @extreme_adjustment_config.trigger_threshold
            
            if abs(ratio - 1.0) > threshold do
              game_type_with_pool = "slotcat_#{pool_id}"
              trigger_extreme_adjustment(state, game_type_with_pool, real_pool, virtual_pool, ratio)
            end
          end
        end
      end)
    end
  end

  # 触发极端差异调整
  defp trigger_extreme_adjustment(state, game_type, real_pool, virtual_pool, current_ratio) do
    # 检查调整历史，防止频繁调整
    last_adjustment = get_in(state, [:extreme_adjustments, :adjustment_history, game_type])
    current_time = System.system_time(:millisecond)
    
    # 如果距离上次调整不足30分钟，跳过
    if last_adjustment && (current_time - last_adjustment) < 1_800_000 do
      Logger.info("🔄 [EXTREME_ADJUSTMENT] #{game_type} 距离上次调整时间过短，跳过调整")
      state
    else
    
    {min_target, max_target} = @extreme_adjustment_config.target_range
    {min_phases, max_phases} = @extreme_adjustment_config.adjustment_phases
    
    # 确定调整目标
    target_ratio = if current_ratio < 1.0 do
      # 虚拟奖池过少，调整到85-95%区间
      min_target + :rand.uniform() * (max_target - min_target)
    else
      # 虚拟奖池过多，调整到85-95%区间
      min_target + :rand.uniform() * (max_target - min_target)
    end
    
    target_virtual_pool = trunc(real_pool * target_ratio)
    total_adjustment = target_virtual_pool - virtual_pool
    
    # 确定调整阶段数
    phases = min_phases + :rand.uniform(max_phases - min_phases)
    adjustment_per_phase = trunc(total_adjustment / phases)
    
    # 应用最大调整幅度限制
    max_adjustment = trunc(real_pool * @extreme_adjustment_config.max_adjustment_per_phase)
    adjustment_per_phase = max(min(adjustment_per_phase, max_adjustment), -max_adjustment)
    
    Logger.info("🚨 [EXTREME_ADJUSTMENT] 触发#{game_type}极端差异调整")
    Logger.info("   当前比例: #{Float.round(current_ratio * 100, 2)}%")
    Logger.info("   目标比例: #{Float.round(target_ratio * 100, 2)}%") 
    Logger.info("   真实奖池: #{real_pool}")
    Logger.info("   虚拟奖池: #{virtual_pool} -> #{target_virtual_pool}")
    Logger.info("   总调整量: #{total_adjustment}")
    Logger.info("   分#{phases}阶段调整，每阶段: #{adjustment_per_phase}")
    
    # 创建调整计划
    adjustment_plan = create_adjustment_plan(
      game_type, 
      virtual_pool, 
      target_virtual_pool, 
      phases, 
      adjustment_per_phase
    )
    
    # 更新状态并开始调整
    updated_adjustments = state.extreme_adjustments
      |> put_in([:active_adjustments, game_type], adjustment_plan)
      |> put_in([:adjustment_history, game_type], current_time)
    
    updated_state = %{state | extreme_adjustments: updated_adjustments}
    
    # 立即执行第一阶段调整
    execute_next_adjustment_phase(updated_state, game_type)
    end
  end

  # 创建调整计划
  defp create_adjustment_plan(game_type, current_virtual, target_virtual, phases, adjustment_per_phase) do
    {min_interval, max_interval} = @extreme_adjustment_config.phase_interval
    
    %{
      game_type: game_type,
      current_virtual: current_virtual,
      target_virtual: target_virtual,
      total_phases: phases,
      current_phase: 0,
      adjustment_per_phase: adjustment_per_phase,
      phase_interval: min_interval + :rand.uniform(max_interval - min_interval),
      started_at: System.system_time(:millisecond),
      last_adjustment_at: nil
    }
  end

  # 执行下一阶段调整
  defp execute_next_adjustment_phase(state, game_type) do
    adjustment_plan = get_in(state, [:extreme_adjustments, :active_adjustments, game_type])
    
    if adjustment_plan && adjustment_plan.current_phase < adjustment_plan.total_phases do
      # 执行当前阶段的调整
      new_phase = adjustment_plan.current_phase + 1
      current_time = System.system_time(:millisecond)
      
      # 计算新的虚拟奖池值
      adjustment_amount = adjustment_plan.adjustment_per_phase
      
      # 应用调整到对应的奖池
      updated_state = apply_virtual_pool_adjustment(state, game_type, adjustment_amount)
      
      Logger.info("⚡ [EXTREME_ADJUSTMENT] #{game_type} 执行第#{new_phase}/#{adjustment_plan.total_phases}阶段调整")
      Logger.info("   调整量: #{adjustment_amount}")
      
      # 更新调整计划
      updated_plan = %{adjustment_plan | 
        current_phase: new_phase, 
        last_adjustment_at: current_time
      }
      
      updated_adjustments = put_in(updated_state.extreme_adjustments, [:active_adjustments, game_type], updated_plan)
      final_state = %{updated_state | extreme_adjustments: updated_adjustments}
      
      # 如果还有更多阶段，安排下一次调整
      if new_phase < adjustment_plan.total_phases do
        Process.send_after(self(), {:execute_adjustment_phase, game_type}, adjustment_plan.phase_interval)
      else
        # 调整完成，清理状态
        Logger.info("✅ [EXTREME_ADJUSTMENT] #{game_type} 极端差异调整完成")
        completed_adjustments = Map.delete(final_state.extreme_adjustments.active_adjustments, game_type)
        final_extreme_adjustments = %{final_state.extreme_adjustments | active_adjustments: completed_adjustments}
        %{final_state | extreme_adjustments: final_extreme_adjustments}
      end
    else
      state
    end
  end

  # 应用虚拟奖池调整
  defp apply_virtual_pool_adjustment(state, game_type, adjustment_amount) do
    cond do
      String.starts_with?(game_type, "slotcat_") ->
        # SlotCat特定奖池调整
        pool_id = String.to_atom(String.replace(game_type, "slotcat_", ""))
        apply_slotcat_pool_adjustment(state, pool_id, adjustment_amount)
      
      true ->
        # 普通游戏奖池调整
        apply_single_pool_adjustment(state, game_type, adjustment_amount)
    end
  end

  # 应用单一奖池调整
  defp apply_single_pool_adjustment(state, game_type, adjustment_amount) do
    current_virtual = get_in(state, [:virtual_pools, game_type, :virtual_pool])
    if current_virtual do
      new_virtual = max(current_virtual + adjustment_amount, 0)
      
      # 安全检查：确保调整后的值合理
      real_pool = get_in(state, [:virtual_pools, game_type, :real_pool])
      if real_pool > 0 do
        new_ratio = new_virtual / real_pool
        # 确保调整后的比例在合理范围内（0.1 - 2.0）
        if new_ratio >= 0.1 && new_ratio <= 2.0 do
          updated_pools = put_in(state.virtual_pools, [game_type, :virtual_pool], new_virtual)
          updated_display = Map.put(state.display_jackpots, game_type, new_virtual)
          
          Logger.info("💰 [EXTREME_ADJUSTMENT] #{game_type} 虚拟奖池调整: #{current_virtual} -> #{new_virtual}")
          
          # 移除极端调整的立即广播，让定时更新统一处理
          # broadcast_jackpot_updates(updated_display, updated_pools)
          
          %{state | virtual_pools: updated_pools, display_jackpots: updated_display}
        else
          Logger.warn("⚠️ [EXTREME_ADJUSTMENT] #{game_type} 调整后比例异常(#{Float.round(new_ratio, 3)})，跳过调整")
          state
        end
      else
        state
      end
    else
      state
    end
  end

  # 应用SlotCat特定奖池调整
  defp apply_slotcat_pool_adjustment(state, pool_id, adjustment_amount) do
    current_virtual = get_in(state, [:virtual_pools, "slotcat", :pools, pool_id, :virtual_pool])
    if current_virtual do
      new_virtual = max(current_virtual + adjustment_amount, 0)
      
      # 安全检查
      real_pool = get_in(state, [:virtual_pools, "slotcat", :pools, pool_id, :real_pool])
      if real_pool > 0 do
        new_ratio = new_virtual / real_pool
        if new_ratio >= 0.1 && new_ratio <= 2.0 do
          # 更新特定奖池
          updated_pools = put_in(state.virtual_pools, ["slotcat", :pools, pool_id, :virtual_pool], new_virtual)
          
          # 重新计算SlotCat总显示奖池
          slotcat_pools = updated_pools["slotcat"][:pools]
          total_virtual = slotcat_pools.center.virtual_pool + 
                         slotcat_pools.left.virtual_pool + 
                         slotcat_pools.right.virtual_pool
          updated_display = Map.put(state.display_jackpots, "slotcat", total_virtual)
          
          Logger.info("💰 [EXTREME_ADJUSTMENT] slotcat_#{pool_id} 虚拟奖池调整: #{current_virtual} -> #{new_virtual}")
          
          # 移除极端调整的立即广播，让定时更新统一处理
          # broadcast_jackpot_updates(updated_display, updated_pools)
          
          %{state | virtual_pools: updated_pools, display_jackpots: updated_display}
        else
          Logger.warn("⚠️ [EXTREME_ADJUSTMENT] slotcat_#{pool_id} 调整后比例异常(#{Float.round(new_ratio, 3)})，跳过调整")
          state
        end
      else
        state
      end
    else
      state
    end
  end


  # ==================== 真实中奖处理功能 ====================

  @doc """
  处理真实Jackpot中奖的内部实现
  当游戏真实中了jackpot时，同步扣除虚拟奖池金额
  """
  defp handle_real_jackpot_win_internal(state, game_id, jackpot_amount, pool_type) do
    try do
      # 根据游戏ID确定游戏类型
      game_type = game_id_to_type(game_id)
      
      if game_type do
        Logger.info("🎰 [VIRTUAL_JACKPOT] 处理真实中奖: #{game_type}, 中奖金额: #{jackpot_amount}, 奖池类型: #{inspect(pool_type)}")
        
        case game_type do
          "slotcat" ->
            # SlotCat需要指定具体的奖池类型
            if pool_type in [:center, :left, :right] do
              handle_slotcat_real_win(state, pool_type, jackpot_amount)
            else
              {:error, "SlotCat游戏必须指定奖池类型 (:center, :left, :right)"}
            end
          
          _ ->
            # 其他游戏的单一奖池处理
            handle_single_pool_real_win(state, game_type, jackpot_amount)
        end
      else
        {:error, "不支持的游戏ID: #{game_id}"}
      end
    rescue
      error ->
        Logger.error("❌ [VIRTUAL_JACKPOT] 处理真实中奖时发生异常: #{inspect(error)}")
        {:error, "处理真实中奖时发生异常: #{inspect(error)}"}
    end
  end

  # 将游戏ID转换为游戏类型
  defp game_id_to_type(game_id) do
    case game_id do
      40 -> "slot777"
      41 -> "slotniu"
      42 -> "slotcat"
      _ -> nil
    end
  end

  # 处理单一奖池游戏的真实中奖
  defp handle_single_pool_real_win(state, game_type, jackpot_amount) do
    pool_state = Map.get(state.virtual_pools, game_type)
    
    if pool_state do
      current_virtual_pool = pool_state.virtual_pool
      
      # 扣除中奖金额，确保不会变成负数
      new_virtual_pool = max(current_virtual_pool - jackpot_amount, 0)
      
      # 计算实际扣除金额（可能小于请求金额，如果虚拟奖池不足）
      actual_deducted = current_virtual_pool - new_virtual_pool
      
      Logger.info("💰 [VIRTUAL_JACKPOT] #{game_type} 扣除虚拟奖池: #{current_virtual_pool} -> #{new_virtual_pool} (扣除: #{actual_deducted})")
      
      # 更新虚拟奖池
      updated_pools = put_in(state.virtual_pools, [game_type, :virtual_pool], new_virtual_pool)
      
      # 更新显示奖池
      updated_display = Map.put(state.display_jackpots, game_type, new_virtual_pool)
      
      # 更新状态
      updated_state = %{state | 
        virtual_pools: updated_pools, 
        display_jackpots: updated_display
      }
      
      # 广播奖池更新
      broadcast_jackpot_updates(updated_display, updated_pools)
      
      if actual_deducted < jackpot_amount do
        Logger.warn("⚠️ [VIRTUAL_JACKPOT] #{game_type} 虚拟奖池不足，只扣除了 #{actual_deducted}/#{jackpot_amount}")
      end
      
      {:ok, updated_state}
    else
      {:error, "找不到游戏类型 #{game_type} 的虚拟奖池"}
    end
  end

  # 处理SlotCat三奖池游戏的真实中奖
  defp handle_slotcat_real_win(state, pool_type, jackpot_amount) do
    slotcat_state = Map.get(state.virtual_pools, "slotcat")
    
    if slotcat_state && slotcat_state[:pools] do
      pool_data = slotcat_state.pools[pool_type]
      
      if pool_data do
        current_virtual_pool = pool_data.virtual_pool
        
        # 扣除中奖金额，确保不会变成负数
        new_virtual_pool = max(current_virtual_pool - jackpot_amount, 0)
        
        # 计算实际扣除金额
        actual_deducted = current_virtual_pool - new_virtual_pool
        
        Logger.info("💰 [VIRTUAL_JACKPOT] slotcat_#{pool_type} 扣除虚拟奖池: #{current_virtual_pool} -> #{new_virtual_pool} (扣除: #{actual_deducted})")
        
        # 更新特定奖池
        updated_pool_data = %{pool_data | virtual_pool: new_virtual_pool}
        updated_slotcat_pools = Map.put(slotcat_state.pools, pool_type, updated_pool_data)
        updated_pools = put_in(state.virtual_pools, ["slotcat", :pools], updated_slotcat_pools)
        
        # 重新计算SlotCat总显示奖池
        total_virtual = updated_slotcat_pools.center.virtual_pool + 
                       updated_slotcat_pools.left.virtual_pool + 
                       updated_slotcat_pools.right.virtual_pool
        updated_display = Map.put(state.display_jackpots, "slotcat", total_virtual)
        
        # 更新状态
        updated_state = %{state | 
          virtual_pools: updated_pools, 
          display_jackpots: updated_display
        }
        
        # 广播奖池更新
        broadcast_jackpot_updates(updated_display, updated_pools)
        
        if actual_deducted < jackpot_amount do
          Logger.warn("⚠️ [VIRTUAL_JACKPOT] slotcat_#{pool_type} 虚拟奖池不足，只扣除了 #{actual_deducted}/#{jackpot_amount}")
        end
        
        {:ok, updated_state}
      else
        {:error, "找不到SlotCat的 #{pool_type} 奖池"}
      end
    else
      {:error, "找不到SlotCat的奖池配置"}
    end
  end

end
