defmodule Teen.Events.GameEvent do
  @moduledoc """
  游戏相关事件定义

  统一的事件结构，用于在系统各组件间传递游戏相关的事件信息。
  支持用户行为、游戏状态变化、充值提现等各种事件类型。

  整合了原有的events、event_system、event_listeners三个系统的功能。
  """

  # 用户登录
  @type event_type ::
          :user_login
          # 用户登出
          | :user_logout
          # 用户注册
          | :user_register
          # 用户充值
          | :user_recharge
          # 用户提现
          | :user_withdrawal
          # 用户分享
          | :user_share
          # 游戏开始
          | :game_started
          # 游戏完成
          | :game_completed
          # 游戏获胜
          | :game_won
          # 游戏失败
          | :game_lost
          # 游戏下注
          | :game_bet_placed
          # VIP等级提升
          | :vip_level_up
          # 任务完成
          | :task_completed
          # 任务分配
          | :task_assigned
          # 奖励领取
          | :reward_claimed
          # 每日重置
          | :daily_reset
          # 充值完成
          | :recharge_completed
          # 充值失败
          | :recharge_failed

  @type t :: %__MODULE__{
          user_id: String.t(),
          event_type: event_type(),
          event_data: map(),
          timestamp: DateTime.t(),
          metadata: map(),
          source: String.t()
        }

  defstruct [
    :user_id,
    :event_type,
    :event_data,
    :timestamp,
    :metadata,
    :source
  ]

  @doc """
  创建新的游戏事件

  ## 参数
  - user_id: 用户ID
  - event_type: 事件类型
  - event_data: 事件数据
  - opts: 可选参数，包括metadata和source

  ## 示例
      iex> Teen.Events.GameEvent.new("user123", :user_login, %{ip: "127.0.0.1"})
      %Teen.Events.GameEvent{
        user_id: "user123",
        event_type: :user_login,
        event_data: %{ip: "127.0.0.1"},
        timestamp: ~U[2024-01-01 00:00:00.000000Z],
        metadata: %{},
        source: "unknown"
      }
  """
  def new(user_id, event_type, event_data, opts \\ []) do
    %__MODULE__{
      user_id: user_id,
      event_type: event_type,
      event_data: event_data || %{},
      timestamp: DateTime.utc_now(),
      metadata: Keyword.get(opts, :metadata, %{}),
      source: Keyword.get(opts, :source, "unknown")
    }
  end

  @doc """
  验证事件是否有效

  检查事件的必要字段是否存在且格式正确
  """
  def valid?(%__MODULE__{} = event) do
    not is_nil(event.user_id) and
      not is_nil(event.event_type) and
      is_map(event.event_data) and
      match?(%DateTime{}, event.timestamp)
  end

  def valid?(_), do: false

  @doc """
  将事件转换为可序列化的map格式
  """
  def to_map(%__MODULE__{} = event) do
    %{
      user_id: event.user_id,
      event_type: event.event_type,
      event_data: event.event_data,
      timestamp: DateTime.to_iso8601(event.timestamp),
      metadata: event.metadata,
      source: event.source
    }
  end

  @doc """
  从map格式恢复事件结构
  """
  def from_map(%{} = map) do
    {:ok, timestamp} = DateTime.from_iso8601(map["timestamp"] || map[:timestamp])

    %__MODULE__{
      user_id: map["user_id"] || map[:user_id],
      event_type: String.to_existing_atom(map["event_type"] || map[:event_type]),
      event_data: map["event_data"] || map[:event_data] || %{},
      timestamp: timestamp,
      metadata: map["metadata"] || map[:metadata] || %{},
      source: map["source"] || map[:source] || "unknown"
    }
  rescue
    _ -> {:error, :invalid_format}
  end

  @doc """
  获取事件的主题名称，用于PubSub订阅
  """
  def topic(%__MODULE__{} = event) do
    "game_events:#{event.event_type}:#{event.user_id}"
  end

  def topic(event_type, user_id) do
    "game_events:#{event_type}:#{user_id}"
  end

  @doc """
  获取广播主题名称，用于订阅所有用户的特定事件类型
  """
  def broadcast_topic(event_type) do
    "game_events:#{event_type}"
  end

  @doc """
  检查事件是否匹配指定的过滤条件
  """
  def matches?(%__MODULE__{} = event, filters) when is_map(filters) do
    Enum.all?(filters, fn {key, value} ->
      case key do
        :event_type -> event.event_type == value
        :user_id -> event.user_id == value
        :source -> event.source == value
        _ -> Map.get(event.event_data, key) == value
      end
    end)
  end

  @doc """
  为事件添加额外的元数据
  """
  def add_metadata(%__MODULE__{} = event, key, value) when is_atom(key) do
    %{event | metadata: Map.put(event.metadata, key, value)}
  end

  def add_metadata(%__MODULE__{} = event, metadata) when is_map(metadata) do
    %{event | metadata: Map.merge(event.metadata, metadata)}
  end
end
