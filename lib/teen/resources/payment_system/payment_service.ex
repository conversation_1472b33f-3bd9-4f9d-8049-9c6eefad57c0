defmodule Teen.PaymentSystem.PaymentService do
  @moduledoc """
  支付服务资源

  包装支付网关API，使用混合配置方案处理支付流程
  """
  require Logger
  alias Teen.PaymentSystem.{GatewaySelector, PaymentOrder}

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PaymentSystem,
    data_layer: Ash.DataLayer.Ets,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :order_id, :amount, :status, :created_at]
  end

  code_interface do
    define :create_order
    define :query_order
  end

  actions do
    defaults [:read]

    action :create_order, :struct do
      argument :user_id, :uuid, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false
      argument :currency, :string, allow_nil?: false, default: "INR"
      argument :channel_id, :string, allow_nil?: false, default: "3021"
      argument :notify_url, :string, allow_nil?: true
      argument :return_url, :string, allow_nil?: true

      run fn input, _context ->
        user_id = input.arguments.user_id
        amount = input.arguments.amount
        currency = input.arguments.currency
        channel_id = input.arguments.channel_id
        notify_url = input.arguments.notify_url
        return_url = input.arguments.return_url

        # 选择最佳支付网关
        case GatewaySelector.select_recharge_gateway(amount, currency, user_id) do
          {:ok, gateway_config} ->
            # gateway_config 现在是 PaymentGateway 实例
            # 生成订单ID
            order_id = generate_order_id()

            # 创建支付订单记录和充值记录
            case create_payment_and_recharge_records(
                   order_id,
                   user_id,
                   gateway_config,
                   amount,
                   currency
                 ) do
              {:ok, payment_order} ->
                # 调用支付API
                case create_payment_request(payment_order, gateway_config, notify_url, return_url) do
                  {:ok, response} ->
                    # 更新订单状态
                    {:ok, updated_order} =
                      payment_order
                      |> Ash.Changeset.for_update(:update_gateway_info, %{
                        gateway_order_id: response["payOrderId"],
                        payment_url: response["payUrl"],
                        gateway_response: response
                      })
                      |> Ash.update()

                    {:ok,
                     %{
                       id: Ash.UUID.generate(),
                       order_id: order_id,
                       amount: amount,
                       currency: currency,
                       payment_url: response["payUrl"],
                       status: "created",
                       gateway_name: gateway_config.gateway_name,
                       created_at: DateTime.utc_now()
                     }}

                  {:error, reason} ->
                    # 更新订单状态为失败
                    payment_order
                    |> Ash.Changeset.for_update(:fail_order, %{error_message: reason})
                    |> Ash.update!()

                    {:error, reason}
                end

              {:error, reason} ->
                {:error, reason}
            end

          {:error, reason} ->
            {:error, reason}
        end
      end
    end

    action :query_order, :struct do
      argument :order_id, :string, allow_nil?: false

      run fn input, _context ->
        order_id = input.arguments.order_id

        case query_payment_request(order_id) do
          {:ok, response} ->
            # 更新本地订单状态
            case Teen.PaymentSystem.PaymentOrder.get_by_order_id(order_id) do
              {:ok, payment_order} ->
                new_status = map_external_status(response["status"])
                Ash.update!(payment_order, %{status: new_status})

                {:ok,
                 %{
                   id: Ash.UUID.generate(),
                   order_id: order_id,
                   status: new_status,
                   external_status: response["status"],
                   amount: response["amount"],
                   created_at: DateTime.utc_now()
                 }}

              {:error, _} ->
                {:error, "订单不存在"}
            end

          {:error, reason} ->
            {:error, reason}
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :order_id, :string do
      allow_nil? false
      public? true
      description "订单ID"
    end

    attribute :amount, :decimal do
      allow_nil? false
      public? true
      description "支付金额"
    end

    attribute :currency, :string do
      allow_nil? false
      public? true
      description "货币类型"
      default "INR"
    end

    attribute :status, :string do
      allow_nil? false
      public? true
      description "支付状态"
    end

    attribute :payment_url, :string do
      allow_nil? true
      public? true
      description "支付链接"
    end

    attribute :created_at, :utc_datetime do
      allow_nil? false
      public? true
      description "创建时间"
      default &DateTime.utc_now/0
    end
  end

  # 私有函数

  defp generate_order_id do
    timestamp = DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    random = :rand.uniform(9999) |> Integer.to_string() |> String.pad_leading(4, "0")
    "CYP#{timestamp}#{random}"
  end

  defp create_payment_request(payment_order, gateway_config, notify_url, return_url) do
    # 检查是否强制使用模拟支付（通过环境变量控制）
    use_mock_payment = Application.get_env(:cypridina, :use_mock_payment, false)

    if use_mock_payment do
      Logger.info("💰 [PAYMENT] 使用模拟支付模式 (use_mock_payment=true)")
      create_mock_payment_response(payment_order, gateway_config)
    else
      Logger.info("💰 [PAYMENT] 使用真实支付网关: #{gateway_config.gateway_name}")
      create_real_payment_request(payment_order, gateway_config, notify_url, return_url)
    end
  end

  defp create_mock_payment_response(payment_order, gateway_config) do
    Logger.info("💰 [PAYMENT] 开发环境 - 返回模拟支付响应")

    Logger.info(
      "💰 [PAYMENT] 订单: #{payment_order.order_id}, 金额: #{payment_order.amount}, 网关: #{gateway_config.gateway_name}"
    )

    # 使用服务器自己的URL作为模拟支付地址
    base_url = Application.get_env(:cypridina, :base_url, "http://localhost:4000")

    mock_response = %{
      "orderId" => payment_order.order_id,
      "payUrl" => "#{base_url}/api/mock-payment/#{payment_order.order_id}",
      "externalOrderId" => "MOCK_#{payment_order.order_id}",
      "status" => "created"
    }

    {:ok, mock_response}
  end

  def generate_payment_signature(params, secret_key \\ nil) do
    secret_key = secret_key || Application.get_env(:cypridina, :payment)[:secret_key]

    Logger.info("generate_payment_signature: #{secret_key}")
    # 按照字母顺序排序参数，排除空值和sign字段
    sorted_params =
      params
      # |> Enum.reject(fn {key, value} -> key == "sign" or is_nil(value) or value == "" end)
      |> Enum.map(fn {key, value} -> "#{key}=#{value}&" end)
      |> Enum.sort_by(fn x -> String.downcase(x) end)
      |> Enum.join("")

    # 添加密钥
    sign_string = "#{sorted_params}secretKey=#{secret_key}"

    Logger.info("generate_payment_sign_string: #{sign_string}")

    # MD5签名
    :crypto.hash(:md5, sign_string)
    |> Base.encode16(case: :upper)
  end

  defp create_real_payment_request(payment_order, gateway_config, notify_url, return_url) do
    # 将金额转换为分（整数字符串），支付网关可能期望字符串格式的整数
    amount_in_cents =
      payment_order.amount |> Decimal.mult(100) |> Decimal.to_integer() |> to_string()

    params = %{
      "mchId" => gateway_config.merchant_id,
      "mchOrderNo" => payment_order.order_id,
      # "currency" => payment_order.currency,
      "amount" => amount_in_cents,
      "productId" => gateway_config.recharge_channel,
      "clientIp" => "0.0.0.0",
      "notifyUrl" => notify_url || get_in(gateway_config.config_data, ["notify_url"]),
      "returnUrl" => return_url || get_in(gateway_config.config_data, ["return_url"]),
      "subject" => "Recharge",
      "body" => "Game Recharge"
      # "feeRate" => "#{gateway_config.fee_rate}"
    }

    # 生成签名
    secret_key =
      get_in(gateway_config.config_data, ["secret_key"])

    sign = generate_payment_signature(params, secret_key)
    params_with_sign = Map.put(params, "sign", sign)

    # 获取API URL
    create_url = GatewaySelector.get_create_order_url(gateway_config)
    # 30秒超时
    timeout = 30 * 1000

    # 添加调试日志
    Logger.info("💰 [PAYMENT] 发送支付请求到: #{create_url}")

    Logger.info(
      "💰 [PAYMENT] 使用网关: #{gateway_config.gateway_name} (#{gateway_config.recharge_channel})"
    )

    Logger.info("💰 [PAYMENT] 商户ID: #{gateway_config.merchant_id}")
    Logger.info("💰 [PAYMENT] 订单ID: #{payment_order.order_id}")

    Logger.info(
      "💰 [PAYMENT] 金额: #{payment_order.amount} #{payment_order.currency} (#{amount_in_cents} 分)"
    )

    Logger.info("💰 [PAYMENT] 通知URL: #{params["notifyUrl"]}")
    Logger.info("💰 [PAYMENT] 返回URL: #{params["returnUrl"]}")
    Logger.debug("💰 [PAYMENT] 完整请求参数: #{inspect(params_with_sign)}")

    headers = [
      {"Content-Type", "application/json"},
      {"Accept", "application/json"}
    ]

    # 发送真实支付请求
    case Req.post(create_url,
           json: params_with_sign,
           headers: headers,
           receive_timeout: timeout
         ) do
      {:ok, %{status: 200, body: response}} ->
        Logger.info("💰 [PAYMENT] 收到响应: #{inspect(response)}")

        case response do
          %{"retCode" => "SUCCESS"} = data ->
            Logger.info("💰 [PAYMENT] 支付创建成功，支付URL: #{data["payUrl"]}")
            {:ok, data}

          %{"retCode" => "FAIL", "retMsg" => msg} ->
            Logger.error("💰 [PAYMENT] 支付创建失败: #{msg}")
            {:error, "支付创建失败: #{msg}"}

          _ ->
            Logger.error("💰 [PAYMENT] 支付创建失败: 未知错误 - #{inspect(response)}")
            {:error, "支付创建失败: 未知错误"}
        end

      {:ok, %{status: status, body: body}} ->
        Logger.error("💰 [PAYMENT] HTTP错误 #{status}: #{inspect(body)}")
        {:error, "支付创建失败: HTTP #{status}"}

      {:error, reason} ->
        Logger.error("💰 [PAYMENT] 请求失败: #{inspect(reason)}")
        {:error, "支付创建失败: #{inspect(reason)}"}
    end
  end

  defp query_payment_request(order_id) do
    # 检查是否强制使用模拟支付（通过环境变量控制）
    use_mock_payment = Application.get_env(:cypridina, :use_mock_payment, false)

    if use_mock_payment do
      Logger.info("💰 [PAYMENT] 使用模拟查询模式 (use_mock_payment=true)")
      query_mock_payment_response(order_id)
    else
      Logger.info("💰 [PAYMENT] 使用真实支付网关查询订单")
      query_real_payment_request(order_id)
    end
  end

  defp query_mock_payment_response(order_id) do
    Logger.info("💰 [PAYMENT] 开发环境 - 返回模拟查询响应")
    Logger.info("💰 [PAYMENT] 查询订单: #{order_id}")

    # 根据订单ID查找本地订单状态
    case Teen.PaymentSystem.PaymentOrder.get_by_order_id(order_id) do
      {:ok, payment_order} ->
        mock_response = %{
          "orderId" => order_id,
          # 1 表示成功
          "status" => "1",
          "amount" =>
            payment_order.amount |> Decimal.mult(100) |> Decimal.to_integer() |> to_string(),
          "currency" => payment_order.currency,
          "externalOrderId" => "MOCK_#{order_id}",
          "paidAt" => DateTime.utc_now() |> DateTime.to_unix(:millisecond) |> to_string()
        }

        {:ok, mock_response}

      {:error, reason} ->
        {:error, "订单不存在: #{reason}"}
    end
  end

  defp query_real_payment_request(order_id) do
    # 根据订单ID查找对应的网关配置
    case Teen.PaymentSystem.PaymentOrder.get_by_order_id(order_id) do
      {:ok, payment_order} ->
        case GatewaySelector.select_recharge_gateway(payment_order.amount, payment_order.currency) do
          {:ok, gateway_config} ->
            params = %{
              "mchId" => gateway_config.merchant_id,
              "mchOrderNo" => order_id
            }

            # 生成签名
            secret_key =
              get_in(gateway_config.config_data, ["secret_key"])

            sign = generate_payment_signature(params, secret_key)

            params_with_sign = Map.put(params, "sign", sign)

            headers = [
              {"Content-Type", "application/json"},
              {"Accept", "application/json"}
            ]

            query_url = GatewaySelector.get_query_order_url(gateway_config)
            timeout = (gateway_config.timeout_seconds || 30) * 1000

            case Req.post(query_url,
                   json: params_with_sign,
                   headers: headers,
                   receive_timeout: timeout
                 ) do
              {:ok, %{status: 200, body: response}} ->
                case response do
                  %{"retCode" => "SUCCESS"} = data -> {:ok, data}
                  %{"retCode" => "FAIL", "retMsg" => msg} -> {:error, "订单查询失败: #{msg}"}
                  _ -> {:error, "订单查询失败: 未知错误"}
                end

              {:ok, %{status: status}} ->
                {:error, "订单查询失败: HTTP #{status}"}

              {:error, reason} ->
                {:error, "订单查询失败: #{inspect(reason)}"}
            end

          {:error, reason} ->
            {:error, "查询失败: #{reason}"}
        end

      {:error, reason} ->
        {:error, "订单不存在: #{reason}"}
    end
  end

  defp map_external_status(external_status) do
    case external_status do
      "0" -> "pending"
      "1" -> "success"
      "2" -> "failed"
      _ -> "unknown"
    end
  end

  # 创建支付订单和充值记录
  # 确保两个记录使用相同的order_id，解决同步问题
  defp create_payment_and_recharge_records(order_id, user_id, gateway_config, amount, currency) do
    # 创建支付订单记录
    case PaymentOrder.create(%{
           order_id: order_id,
           user_id: user_id,
           gateway_config_id: gateway_config.id,
           order_type: "recharge",
           amount: amount,
           currency: currency,
           customer_info: %{},
           bank_info: %{},
           gateway_request: %{}
         }) do
      {:ok, payment_order} ->
        # 创建对应的充值记录
        case Teen.PaymentSystem.RechargeRecord.create(%{
               user_id: user_id,
               order_id: order_id,
               amount: amount,
               currency: currency,
               payment_method: "gateway"
             }) do
          {:ok, _recharge_record} ->
            Logger.info("💰 [PAYMENT] 创建支付订单和充值记录成功: #{order_id}")
            {:ok, payment_order}

          {:error, reason} ->
            Logger.error("💰 [PAYMENT] 创建充值记录失败: #{inspect(reason)}")

            # 如果充值记录创建失败，应该删除已创建的支付订单以保持数据一致性
            PaymentOrder.destroy(payment_order.id)
            {:error, "创建充值记录失败: #{inspect(reason)}"}
        end

      {:error, reason} ->
        Logger.error("💰 [PAYMENT] 创建支付订单失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  创建提现订单
  """
  def create_withdrawal_order(params, gateway_config) do
    Logger.info("💰 [PAYMENT] 创建提现订单: #{params.order_id}")

    # 解析银行信息
    bank_info =
      case Jason.decode(params.bank_info || "{}") do
        {:ok, info} -> info
        _ -> %{}
      end

    # 构建提现请求参数
    withdrawal_params = %{
      order_no: params.order_id,
      amount: params.amount,
      client_ip: "127.0.0.1",
      notify_url: params.notify_url,
      user_name: bank_info["account_name"] || "Unknown",
      card_number: bank_info["account_number"] || "",
      ifsc_code: bank_info["ifsc"] || bank_info["bank_id"] || "",
      bank_name: bank_info["bank_name"] || ""
    }

    # 调用网关API
    case gateway_config.gateway_name do
      "YidunPay" ->
        call_yidun_withdrawal(withdrawal_params)

      "SanQiPay" ->
        call_sanqi_withdrawal(withdrawal_params)

      "DuoDuoPay" ->
        call_duoduo_withdrawal(withdrawal_params)

      _ ->
        simulate_withdrawal_response(withdrawal_params)
    end
  end

  defp call_yidun_withdrawal(params) do
    # YidunPay 提现API调用
    {:ok,
     %{
       status: "processing",
       gateway_order_id: "YD#{System.unique_integer([:positive])}",
       message: "Withdrawal submitted to YidunPay",
       order_id: params.order_no
     }}
  end

  defp call_sanqi_withdrawal(params) do
    # SanQiPay 提现API调用
    {:ok,
     %{
       status: "processing",
       gateway_order_id: "SQ#{System.unique_integer([:positive])}",
       message: "Withdrawal submitted to SanQiPay",
       order_id: params.order_no
     }}
  end

  defp call_duoduo_withdrawal(params) do
    # DuoDuoPay 提现API调用
    {:ok,
     %{
       status: "processing",
       gateway_order_id: "DD#{System.unique_integer([:positive])}",
       message: "Withdrawal submitted to DuoDuoPay",
       order_id: params.order_no
     }}
  end

  defp simulate_withdrawal_response(params) do
    {:ok,
     %{
       status: "processing",
       gateway_order_id: "SIM#{System.unique_integer([:positive])}",
       message: "Simulated withdrawal processing",
       order_id: params.order_no,
       processing_time: "1-2 business days"
     }}
  end
end
