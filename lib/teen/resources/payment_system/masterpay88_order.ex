defmodule Teen.PaymentSystem.PaymentOrder do
  @moduledoc """
  支付订单资源

  记录支付订单的详细信息，包括：
  - 订单基本信息
  - 网关交互数据
  - 支付状态跟踪
  - 回调处理记录
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :order_id, :user_id, :order_type, :amount, :status, :inserted_at]
  end

  postgres do
    table "payment_orders"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_by_status
    define :get_by_order_id, action: :read, get_by: :order_id

    define :update_status
    define :complete_order
    define :fail_order
  end

  actions do
    defaults [:update, :destroy, :read]

    create :create do
      primary? true
      accept [
        :order_id,
        :user_id,
        :gateway_config_id,
        :order_type,
        :amount,
        :currency,
        :payment_method,
        :customer_info,
        :bank_info,
        :gateway_request
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, "pending")
        |> Ash.Changeset.change_attribute(
          :actual_amount,
          Ash.Changeset.get_attribute(changeset, :amount)
        )
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :created_at], load: [:user])
    end

    read :list_by_status do
      argument :status, :string, allow_nil?: false
      filter expr(status == ^arg(:status))
      prepare build(sort: [desc: :created_at], load: [:user])
    end

    update :update_status do
      require_atomic? false
      accept [:status, :gateway_status, :gateway_response, :error_message]
    end

    update :update_gateway_info do
      require_atomic? false
      accept [:gateway_order_id, :payment_url, :qr_code, :gateway_response]
    end

    update :complete_order do
      require_atomic? false
      accept [:gateway_response, :callback_data, :fee_amount, :actual_amount]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, "completed")
        |> Ash.Changeset.change_attribute(:completed_at, DateTime.utc_now())
      end
    end

    update :fail_order do
      require_atomic? false
      accept [:error_message, :gateway_response, :callback_data]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, "failed")
        |> Ash.Changeset.change_attribute(:cancelled_at, DateTime.utc_now())
      end
    end

    update :cancel_order do
      require_atomic? false
      accept [:error_message]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, "cancelled")
        |> Ash.Changeset.change_attribute(:cancelled_at, DateTime.utc_now())
      end
    end
  end

  validations do
    validate present(:order_id), message: "订单ID不能为空"
    validate present(:user_id), message: "用户ID不能为空"
    validate present(:gateway_config_id), message: "网关配置ID不能为空"
    validate present(:order_type), message: "订单类型不能为空"
    validate present(:amount), message: "订单金额不能为空"

    validate compare(:amount, greater_than: 0), message: "订单金额必须大于0"
    validate compare(:fee_amount, greater_than_or_equal_to: 0), message: "手续费不能为负数"
    validate compare(:actual_amount, greater_than_or_equal_to: 0), message: "实际金额不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :order_id, :string do
      allow_nil? false
      public? true
      description "订单ID"
      constraints max_length: 100
    end

    attribute :gateway_order_id, :string do
      allow_nil? true
      public? true
      description "网关订单ID"
      constraints max_length: 100
    end

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :gateway_config_id, :uuid do
      allow_nil? false
      public? true
      description "网关配置ID"
    end

    attribute :order_type, :string do
      allow_nil? false
      public? true
      description "订单类型"
      # constraints one_of: ["recharge", "withdrawal"]
    end

    attribute :amount, :decimal do
      allow_nil? false
      public? true
      description "订单金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :currency, :string do
      allow_nil? false
      public? true
      description "货币类型"
      constraints max_length: 10
      default "INR"
    end

    attribute :status, :string do
      allow_nil? false
      public? true
      description "订单状态"
      constraints max_length: 20
      default "pending"
    end

    attribute :gateway_status, :string do
      allow_nil? true
      public? true
      description "网关状态"
      constraints max_length: 50
    end

    attribute :payment_method, :string do
      allow_nil? true
      public? true
      description "支付方式"
      constraints max_length: 50
    end

    attribute :payment_url, :string do
      allow_nil? true
      public? true
      description "支付链接"
    end

    attribute :qr_code, :string do
      allow_nil? true
      public? true
      description "二维码数据"
    end

    attribute :customer_info, :map do
      allow_nil? false
      public? true
      description "客户信息"
      default %{}
    end

    attribute :bank_info, :map do
      allow_nil? false
      public? true
      description "银行信息"
      default %{}
    end

    attribute :gateway_request, :map do
      allow_nil? false
      public? true
      description "网关请求数据"
      default %{}
    end

    attribute :gateway_response, :map do
      allow_nil? false
      public? true
      description "网关响应数据"
      default %{}
    end

    attribute :callback_data, :map do
      allow_nil? false
      public? true
      description "回调数据"
      default %{}
    end

    attribute :fee_amount, :decimal do
      allow_nil? false
      public? true
      description "手续费（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :actual_amount, :decimal do
      allow_nil? false
      public? true
      description "实际金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :error_message, :string do
      allow_nil? true
      public? true
      description "错误信息"
    end

    attribute :completed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "完成时间"
    end

    attribute :cancelled_at, :utc_datetime do
      allow_nil? true
      public? true
      description "取消时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :gateway_config, Teen.PaymentSystem.PaymentGateway do
      public? true
      source_attribute :gateway_config_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :is_pending, :boolean do
      public? true
      description "是否待处理"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          record.status == "pending"
        end)
      end
    end

    calculate :is_completed, :boolean do
      public? true
      description "是否已完成"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          record.status == "completed"
        end)
      end
    end

    calculate :net_amount, :decimal do
      public? true
      description "净金额（扣除手续费）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          Decimal.sub(record.amount, record.fee_amount)
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            "pending" -> "待处理"
            "processing" -> "处理中"
            "completed" -> "已完成"
            "failed" -> "失败"
            "cancelled" -> "已取消"
            _ -> "未知状态"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_order_id, [:order_id]
  end
end
