defmodule Teen.ActivitySystem.GameTask.Changes.AtomicUpdateWrapper do
  @moduledoc """
  原子变更包装器

  为更新操作提供原子变更支持，包括：
  - 唯一性验证
  - 数据完整性检查
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    # 对于非原子操作，执行唯一性验证
    game_id = Ash.Changeset.get_attribute(changeset, :game_id)
    task_type = Ash.Changeset.get_attribute(changeset, :task_type)
    current_data = changeset.data

    # 检查是否需要进行唯一性验证
    if should_validate_uniqueness?(current_data, game_id, task_type) do
      case check_game_task_uniqueness(game_id, task_type, current_data.id) do
        :ok ->
          changeset
        {:error, existing_task} ->
          Ash.Changeset.add_error(changeset,
            field: :game_id,
            message: "该游戏(ID: #{game_id})的#{get_task_type_display(task_type)}任务已存在。现有任务: \"#{existing_task.task_name}\"。请选择其他游戏或任务类型，或编辑现有任务。"
          )
      end
    else
      changeset
    end
  end

  @impl true
  def atomic(changeset, _opts, _context) do
    # 获取变更的属性
    game_id = Ash.Changeset.get_attribute(changeset, :game_id)
    task_type = Ash.Changeset.get_attribute(changeset, :task_type)
    current_data = changeset.data

    # 如果 game_id 或 task_type 发生变化，需要进行唯一性检查
    if should_validate_uniqueness?(current_data, game_id, task_type) do
      # 构建原子条件：确保没有其他记录具有相同的 game_id 和 task_type
      atomic_conditions = %{
        game_id: game_id || current_data.game_id,
        task_type: task_type || current_data.task_type
      }

      # 返回原子更新条件
      {:atomic, [
        # 确保更新时没有冲突的记录存在
        where: expr(
          not exists(
            from t in Teen.ActivitySystem.GameTask,
            where: t.game_id == ^atomic_conditions.game_id and 
                   t.task_type == ^atomic_conditions.task_type and
                   t.id != parent_as(:self).id
          )
        )
      ]}
    else
      # 如果不需要唯一性验证，使用默认原子操作
      {:atomic, []}
    end
  end

  # 检查是否需要进行唯一性验证
  defp should_validate_uniqueness?(current_data, new_game_id, new_task_type) do
    # 如果游戏ID或任务类型发生了变化，则需要验证唯一性
    (not is_nil(new_game_id) and new_game_id != current_data.game_id) or
    (not is_nil(new_task_type) and new_task_type != current_data.task_type)
  end

  # 检查游戏任务唯一性（排除当前记录）
  defp check_game_task_uniqueness(game_id, task_type, current_id) do
    case Teen.ActivitySystem.GameTask
         |> Ash.Query.filter(game_id == ^game_id and task_type == ^task_type and id != ^current_id)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_task} -> {:error, existing_task}
      {:error, _} -> :ok
    end
  end

  # 获取任务类型显示名称
  defp get_task_type_display(task_type) do
    case task_type do
      :game_rounds -> "游戏局数"
      :recharge_amount -> "充值金额"
      :win_rounds -> "游戏胜利"
      :wheel_spins -> "转盘次数"
      :task_completion -> "完成任务"
      _ -> "未知类型"
    end
  end
end
