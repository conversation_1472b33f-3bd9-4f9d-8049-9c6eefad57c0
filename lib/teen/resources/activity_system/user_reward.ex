defmodule Teen.ActivitySystem.UserReward do
  @moduledoc """
  用户奖励资源

  统一管理所有用户奖励的发放和领取记录，包括：
  - 自动发放的奖励（如游戏胜利奖励）
  - 需要手动领取的奖励（如月卡、任务奖励、储钱罐等）
  - 奖励的发放、领取、过期等完整生命周期

  通过 is_pending 字段区分是否需要手动领取
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource, AshOban]

  admin do
    table_columns [
      :id,
      :user_id,
      :reward_type,
      :source_type,
      :reward_amount,
      :is_pending,
      :status,
      :expires_at,
      :created_at
    ]
  end

  postgres do
    table "user_rewards"
    repo Cypridina.Repo
  end

  # AshOban 配置 - 过期奖励清理
  oban do
    domain Teen.ActivitySystem

    scheduled_actions do
      schedule :cleanup_expired_rewards, "0 */6 * * *" do
        action :cleanup_expired_rewards
        worker_module_name(Teen.Workers.ExpiredRewardCleanup)
        max_attempts(2)
        queue(:cleanup_tasks)
      end
    end
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_pending_rewards
    define :list_claimed_rewards
    define :list_auto_rewards
    define :claim_reward
    define :auto_distribute
    define :expire_rewards
    define :cleanup_expired_rewards
    define :get_user_pending_count
    define :get_user_total_rewards
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true

      accept [
        :user_id,
        :reward_type,
        :source_type,
        :source_id,
        :reward_amount,
        :reward_data,
        :expires_at,
        :description,
        :is_pending
      ]

      change fn changeset, _context ->
        # 根据 is_pending 设置初始状态
        is_pending = Ash.Changeset.get_attribute(changeset, :is_pending) || false
        initial_status = if is_pending, do: :pending, else: :distributed

        changeset
        |> Ash.Changeset.change_attribute(:status, initial_status)
        |> Ash.Changeset.change_attribute(:created_at, DateTime.utc_now())
        |> Ash.Changeset.change_attribute(
          :distributed_at,
          if(is_pending, do: nil, else: DateTime.utc_now())
        )
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :created_at])
    end

    read :list_pending_rewards do
      argument :user_id, :uuid, allow_nil?: false

      filter expr(
               user_id == ^arg(:user_id) and
                 is_pending == true and
                 status == :pending and
                 (is_nil(expires_at) or expires_at > ^DateTime.utc_now())
             )

      prepare build(sort: [asc: :created_at])
    end

    read :list_claimed_rewards do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and status == :claimed)
      prepare build(sort: [desc: :claimed_at])
    end

    read :list_auto_rewards do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and is_pending == false)
      prepare build(sort: [desc: :distributed_at])
    end

    update :claim_reward do
      require_atomic? false
      accept []

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :claimed)
        |> Ash.Changeset.change_attribute(:claimed_at, DateTime.utc_now())
      end
    end

    update :auto_distribute do
      require_atomic? false
      accept []

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :distributed)
        |> Ash.Changeset.change_attribute(:distributed_at, DateTime.utc_now())
      end
    end

    update :expire_rewards do
      require_atomic? false
      accept []

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :expired)
      end
    end

    read :get_user_pending_count do
      argument :user_id, :uuid, allow_nil?: false

      filter expr(
               user_id == ^arg(:user_id) and
                 is_pending == true and
                 status == :pending and
                 (is_nil(expires_at) or expires_at > ^DateTime.utc_now())
             )
    end

    read :get_user_total_rewards do
      argument :user_id, :uuid, allow_nil?: false
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true
      filter expr(user_id == ^arg(:user_id))
    end

    # 清理过期奖励的定时任务
    create :cleanup_expired_rewards do
      accept []

      change fn changeset, _context ->
        case cleanup_all_expired_rewards() do
          {:ok, result} ->
            require Logger
            Logger.info("过期奖励清理完成: #{inspect(result)}")
            changeset

          {:error, reason} ->
            require Logger
            Logger.error("过期奖励清理失败: #{inspect(reason)}")

            changeset
            |> Ash.Changeset.add_error(field: :base, message: "过期奖励清理失败: #{inspect(reason)}")
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :cash, :items, :points, :vip_exp]
    end

    attribute :source_type, :atom do
      allow_nil? false
      public? true
      description "奖励来源类型"

      constraints one_of: [
                    # 周卡
                    :weekly_card,
                    # 游戏任务
                    :game_task,
                    # 每日任务
                    :daily_task,
                    # 七日任务
                    :seven_day_task,
                    # VIP礼包
                    :vip_gift,
                    # 储钱罐
                    :piggy_bank,
                    # 充值奖励
                    :recharge_bonus,
                    # 登录奖励
                    :login_bonus,
                    # 成就奖励
                    :achievement,
                    # 活动奖励
                    :event_reward
                  ]
    end

    attribute :source_id, :uuid do
      allow_nil? true
      public? true
      description "奖励来源ID（如任务ID、活动ID等）"
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :reward_data, :map do
      allow_nil? true
      public? true
      description "奖励详细数据（JSON格式）"
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "奖励描述"
      constraints max_length: 500
    end

    attribute :is_pending, :boolean do
      allow_nil? false
      public? true
      description "是否需要手动领取"
      default false
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "奖励状态"
      constraints one_of: [:pending, :claimed, :expired, :distributed]
      default :pending
    end

    attribute :expires_at, :utc_datetime do
      allow_nil? true
      public? true
      description "过期时间（为空表示永不过期）"
    end

    attribute :claimed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "领取时间"
    end

    attribute :distributed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "自动发放时间"
    end

    attribute :created_at, :utc_datetime do
      allow_nil? false
      public? true
      description "创建时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :is_expired, :boolean do
      public? true
      description "是否已过期"

      calculation fn records, _context ->
        now = DateTime.utc_now()

        Enum.map(records, fn record ->
          case record.expires_at do
            nil -> false
            expires_at -> DateTime.compare(expires_at, now) == :lt
          end
        end)
      end
    end

    calculate :days_until_expiry, :integer do
      public? true
      description "距离过期天数"

      calculation fn records, _context ->
        now = DateTime.utc_now()

        Enum.map(records, fn record ->
          case record.expires_at do
            nil ->
              nil

            expires_at ->
              diff = DateTime.diff(expires_at, now, :day)
              if diff >= 0, do: diff, else: 0
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :pending -> "待领取"
            :claimed -> "已领取"
            :expired -> "已过期"
            :distributed -> "已发放"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "游戏币"
            :cash -> "现金"
            :items -> "道具"
            :points -> "积分"
            :vip_exp -> "VIP经验"
            _ -> "其他"
          end
        end)
      end
    end
  end

  # 私有函数 - 过期奖励清理逻辑

  defp cleanup_all_expired_rewards do
    require Logger
    Logger.info("🧹 开始清理过期奖励...")

    now = DateTime.utc_now()

    # 查找所有过期的待领取奖励
    case Ash.read(__MODULE__,
           filter: [
             is_pending: true,
             status: :pending
           ]
         ) do
      {:ok, pending_rewards} ->
        # 过滤出真正过期的奖励
        expired_rewards =
          Enum.filter(pending_rewards, fn reward ->
            reward.expires_at != nil and DateTime.compare(reward.expires_at, now) == :lt
          end)

        # 批量更新过期奖励状态
        results =
          Enum.map(expired_rewards, fn reward ->
            case expire_rewards(reward) do
              {:ok, updated_reward} ->
                Logger.debug("标记奖励为过期: #{reward.id}")
                {:ok, updated_reward}

              {:error, reason} ->
                Logger.error("标记奖励过期失败 - 奖励ID: #{reward.id}, 原因: #{inspect(reason)}")
                {:error, reason}
            end
          end)

        successful = Enum.count(results, fn {status, _} -> status == :ok end)
        total = length(results)

        Logger.info("🧹 过期奖励清理完成 - 处理: #{total}个奖励, 成功清理: #{successful}个")
        {:ok, %{total: total, successful: successful, results: results}}

      {:error, reason} ->
        Logger.error("🧹 查找过期奖励失败: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
