defmodule Teen.ActivitySystem.LossRebateJar do
  @moduledoc """
  输钱返利金罐子资源

  管理输钱返利配置
  规则：每日领取前一天损失的指定百分比
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource, AshOban]

  admin do
    table_columns [
      :id,
      :title,
      :max_claims,
      :loss_threshold,
      :rebate_percentage,
      :max_rebate,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "loss_rebate_jars"
    repo Cypridina.Repo
  end

  oban do
    domain Teen.ActivitySystem

    scheduled_actions do
      # 每天8点执行损失返利结算
      schedule :daily_settlement, "0 8 * * *" do
        action :daily_settlement
        max_attempts(3)

        worker_module_name(
          Teen.ActivitySystem.LossRebateJar.AshOban.ActionWorker.DailySettlement
        )
      end
    end
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_jars
    define :get_rebate_config
    define :enable_jar
    define :disable_jar
    define :daily_settlement
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      primary? true
      accept :*

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:is_active, true)
      end
    end

    update :update do
      primary? true
      accept :*
    end

    read :list_active_jars do
      filter expr(status == :enabled)
    end

    read :get_rebate_config do
      get? true
      filter expr(status == :enabled)
    end

    update :enable_jar do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_jar do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    # 每日结算所有用户
    action :daily_settlement, :struct do
      run fn _input, _context ->
        settlement_time = DateTime.utc_now()

        case settle_all_users(settlement_time) do
          {:ok, count} ->
            # 更新所有启用的返利罐子的结算时间
            update_settlement_time(settlement_time)
            {:ok, %{processed_users: count, settlement_time: settlement_time}}

          {:error, reason} ->
            {:error, reason}
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :max_claims, :integer do
      allow_nil? false
      public? true
      description "领取次数"
      constraints min: 1
      default 1
    end

    attribute :loss_threshold, :decimal do
      allow_nil? false
      public? true
      description "用户输钱金币值（分）"
      constraints min: Decimal.new("0")
    end

    attribute :rebate_percentage, :decimal do
      allow_nil? false
      public? true
      description "输钱奖励（百分比）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
      default Decimal.new("10")
    end

    attribute :max_rebate, :decimal do
      allow_nil? false
      public? true
      description "奖励上限（分）"
      constraints min: Decimal.new("0")
    end

    attribute :calculation_period, :atom do
      allow_nil? false
      public? true
      description "计算周期"
      constraints one_of: [:daily, :weekly, :monthly]
      default :daily
    end

    attribute :rebate_type, :atom do
      allow_nil? false
      public? true
      description "返利类型"
      constraints one_of: [:coins, :points, :cash]
      default :coins
    end

    attribute :auto_distribute, :boolean do
      allow_nil? false
      public? true
      description "自动发放"
      default false
    end

    attribute :is_active, :boolean do
      allow_nil? false
      public? true
      description "是否激活"
      default true
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    attribute :last_settlement_at, :utc_datetime do
      allow_nil? true
      public? true
      description "上次结算时间"
    end

    timestamps()
  end

  # 私有函数 - 结算相关逻辑
  defp settle_all_users(settlement_time) do
    require Logger

    # 获取最后结算时间，如果没有则使用24小时前
    last_settlement = get_last_settlement_time()
    start_time = last_settlement || DateTime.add(settlement_time, -24 * 60 * 60, :second)

    # 获取所有有游戏记录的用户
    users_to_settle = get_active_users(start_time, settlement_time)

    Logger.info("开始结算 #{length(users_to_settle)} 个用户的损失返利")

    results =
      users_to_settle
      |> Task.async_stream(
        fn user_id -> settle_single_user(user_id, start_time) end,
        max_concurrency: 10,
        timeout: 30_000
      )
      |> Enum.to_list()

    success_count = count_successful_results(results)
    Logger.info("损失返利结算完成: 成功处理 #{success_count} 个用户")

    {:ok, success_count}
  end

  defp update_settlement_time(settlement_time) do
    require Logger

    # 获取所有启用的返利罐子并更新结算时间
    case __MODULE__.list_active_jars() do
      {:ok, jars} ->
        Enum.each(jars, fn jar ->
          case __MODULE__.update(jar, %{last_settlement_at: settlement_time}) do
            {:ok, _} ->
              Logger.info("更新返利罐子 #{jar.id} 结算时间: #{settlement_time}")
            {:error, reason} ->
              Logger.error("更新返利罐子 #{jar.id} 结算时间失败: #{inspect(reason)}")
          end
        end)
      {:error, reason} ->
        Logger.error("获取活跃返利罐子失败: #{inspect(reason)}")
    end
  end

  defp get_active_users(start_time, settlement_time) do
    alias Teen.GameManagement.GameRecord
    import Ecto.Query
    require Logger

    Logger.info("查询活跃用户 - 开始时间: #{start_time}, 结束时间: #{settlement_time}")

    query =
      from g in GameRecord,
        where: g.inserted_at >= ^start_time and
               g.inserted_at < ^settlement_time and
               not is_nil(g.inserted_at),
        distinct: g.user_id,
        select: g.user_id

    users = Cypridina.Repo.all(query)
    Logger.info("找到活跃用户数: #{length(users)}")

    users
  end

  defp get_last_settlement_time do
    case __MODULE__.list_active_jars() do
      {:ok, [jar | _]} -> jar.last_settlement_at
      _ -> nil
    end
  end

  defp settle_single_user(user_id, settlement_time) do
    require Logger
    Logger.info("开始结算用户 #{user_id}")

    # 直接调用 upsert 实现，无需先查询
    result = Teen.ActivitySystem.UserLossRebate.settle_user_rebate(user_id, settlement_time)
    Logger.info("用户 #{user_id} 结算结果: #{inspect(result)}")
    result
  end

  defp count_successful_results(results) do
    Enum.count(results, fn
      {:ok, {:ok, _}} -> true
      _ -> false
    end)
  end
end
