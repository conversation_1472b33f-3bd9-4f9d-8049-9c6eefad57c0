defmodule Teen.ActivitySystem.UserLossRebate do
  @moduledoc """
  用户损失返利资源

  存储玩家的损失返利数据，每个用户只保存一条记录
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource, AshOban]

  admin do
    table_columns [
      :id,
      :user_id,
      :last_settlement_at,
      :total_bet_amount,
      :total_win_amount,
      :net_loss_amount,
      :rebate_amount,
      :vip_level,
      :rebate_rate,
      :updated_at
    ]
  end

  postgres do
    table "user_loss_rebates"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :get_by_user_id, action: :read, get_by: :user_id
    define :get_or_create, args: [:user_id]
    define :get_pending_rebate, args: [:user_id]
    define :settle_user_rebate, args: [:user_id, :start_time]
    define :claim_rebate
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    # 获取或创建用户返利记录
    action :get_or_create, :struct do
      argument :user_id, :uuid, allow_nil?: false

      run fn input, _context ->
        user_id = input.arguments.user_id

        case __MODULE__.get_by_user_id(user_id) do
          {:ok, user_rebate} ->
            {:ok, user_rebate}

          {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Query.NotFound{} | _]}} ->
            # 创建新记录
            Ash.create(__MODULE__, %{
              user_id: user_id,
              last_settlement_at: nil,
              total_bet_amount: Decimal.new("0"),
              total_win_amount: Decimal.new("0"),
              net_loss_amount: Decimal.new("0"),
              rebate_amount: Decimal.new("0"),
              vip_level: 0,
              rebate_rate: Decimal.new("0")
            })

          {:error, reason} ->
            {:error, reason}
        end
      end
    end

    # 获取待确认的返利（自上次结算后的新返利）
    action :get_pending_rebate, :struct do
      argument :user_id, :uuid, allow_nil?: false

      run fn input, _context ->
        user_id = input.arguments.user_id

        case __MODULE__.get_or_create(user_id) do
          {:ok, user_rebate} ->
            # 计算自上次结算后的待确认返利
            pending_amount = calculate_pending_rebate(user_id, user_rebate.last_settlement_at)

            {:ok, %{
              user_id: user_id,
              settled_rebate: user_rebate.rebate_amount,
              pending_rebate: pending_amount,
              total_available: Decimal.add(user_rebate.rebate_amount, pending_amount),
              last_settlement_at: user_rebate.last_settlement_at
            }}

          {:error, reason} ->
            {:error, reason}
        end
      end
    end

    # 结算用户返利 - 将待确认返利划拨到rebate_amount
    action :settle_user_rebate, :struct do
      argument :user_id, :uuid, allow_nil?: false
      argument :start_time, :utc_datetime, allow_nil?: false

      run fn input, _context ->
        user_id = input.arguments.user_id
        start_time = input.arguments.start_time

        case calculate_user_rebate(user_id, start_time) do
          {:ok, rebate_data} ->
            case __MODULE__.get_or_create(user_id) do
              {:ok, user_rebate} ->
                # 直接覆盖rebate_amount，更新结算时间
                Ash.update(user_rebate, %{
                  last_settlement_at: DateTime.utc_now(),
                  total_bet_amount: rebate_data.total_bet,
                  total_win_amount: rebate_data.total_win,
                  net_loss_amount: rebate_data.net_loss,
                  rebate_amount: rebate_data.rebate_amount,  # 直接覆盖
                  vip_level: rebate_data.vip_level,
                  rebate_rate: rebate_data.rebate_rate
                })

              {:error, reason} ->
                {:error, reason}
            end

          {:error, reason} ->
            {:error, reason}
        end
      end
    end

    # 领取返利 - 将rebate_amount清零并发放奖励
    update :claim_rebate do
      require_atomic? false
      argument :user_id, :uuid, allow_nil?: false

      change fn changeset, _context ->
        # 获取当前的返利金额
        current_rebate = Ash.Changeset.get_attribute(changeset, :rebate_amount)

        if Decimal.compare(current_rebate, 0) == :gt do
          # 发放奖励
          case distribute_rebate_reward(changeset.data.user_id, current_rebate) do
            {:ok, _} ->
              # 将返利金额清零
              Ash.Changeset.change_attribute(changeset, :rebate_amount, Decimal.new("0"))

            {:error, reason} ->
              Ash.Changeset.add_error(changeset, :rebate_amount, "奖励发放失败: #{inspect(reason)}")
          end
        else
          # 没有可领取的返利
          Ash.Changeset.add_error(changeset, :rebate_amount, "没有可领取的返利")
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :last_settlement_at, :utc_datetime do
      allow_nil? true
      public? true
      description "上次结算时间"
    end

    attribute :total_bet_amount, :decimal do
      allow_nil? false
      public? true
      description "结算期间总下注金额（分）"
      default Decimal.new("0")
    end

    attribute :total_win_amount, :decimal do
      allow_nil? false
      public? true
      description "结算期间总获胜金额（分）"
      default Decimal.new("0")
    end

    attribute :net_loss_amount, :decimal do
      allow_nil? false
      public? true
      description "净损失金额（分）"
      default Decimal.new("0")
    end

    attribute :rebate_amount, :decimal do
      allow_nil? false
      public? true
      description "已结算但尚未领取的返利金额（分）"
      default Decimal.new("0")
    end

    attribute :vip_level, :integer do
      allow_nil? false
      public? true
      description "结算时的VIP等级"
      default 0
    end

    attribute :rebate_rate, :decimal do
      allow_nil? false
      public? true
      description "返利比例（百分比）"
      default Decimal.new("0")
    end

    timestamps()
  end

  calculations do
    calculate :pending_rebate, :decimal do
      public? true
      description "新计算的返利"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          # 获取已结算未领取的返利
          # 计算自上次结算后的新返利
          new_rebate = calculate_pending_rebate(record.user_id, record.last_settlement_at)
          new_rebate
        end)
      end
    end
  end

  identities do
    identity :unique_user, [:user_id]
  end

  # 私有函数
  defp calculate_user_rebate(user_id, start_time) do
    alias Teen.GameManagement.GameRecord
    alias Teen.VipSystem.UserVipInfo
    import Ecto.Query
    end_time = DateTime.utc_now()

    # 获取用户记录
    case __MODULE__.get_by_user_id(user_id) do
      {:ok, user_rebate} ->
        # 确定结算时间范围
        # 统计游戏数据
        game_stats = get_game_stats(user_id, start_time, end_time)

        # 获取VIP等级
        vip_level = get_user_vip_level(user_id)

        # 计算返利
        rebate_data = calculate_rebate(game_stats, vip_level)

        {:ok, rebate_data}

      {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Query.NotFound{} | _]}} ->
        # 用户记录不存在，创建默认数据

        game_stats = get_game_stats(user_id, start_time, end_time)
        vip_level = get_user_vip_level(user_id)
        rebate_data = calculate_rebate(game_stats, vip_level)

        {:ok, rebate_data}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_game_stats(user_id, start_time, end_time) do
    alias Teen.GameManagement.GameRecord
    import Ecto.Query

    require Logger
    Logger.info("获取用户游戏数据 - 用户: #{user_id}, 开始时间: #{start_time}, 结束时间: #{end_time}")

    query =
      from g in GameRecord,
        where:
          g.user_id == ^user_id and
            g.inserted_at >= ^start_time and
            g.inserted_at <= ^end_time and
            not is_nil(g.inserted_at),
        select: {
          sum(g.bet_amount),
          sum(g.win_amount),
          sum(g.net_amount)
        }

    case Cypridina.Repo.one(query) do
      {total_bet, total_win, net_result} ->
        # 处理 nil 值，转换为 Decimal
        total_bet = total_bet || Decimal.new("0")
        total_win = total_win || Decimal.new("0")
        net_result = net_result || Decimal.new("0")

        Logger.info(
          "用户游戏数据 - 用户: #{user_id}, 总下注: #{total_bet}, 总获胜: #{total_win}, 净结果: #{net_result}"
        )

        %{
          total_bet: total_bet,
          total_win: total_win,
          net_result: net_result
        }

      x ->
        Logger.info("用户游戏数据 - 用户: #{user_id}, #{inspect(x)}无游戏记录")

        %{
          total_bet: Decimal.new("0"),
          total_win: Decimal.new("0"),
          net_result: Decimal.new("0")
        }
    end
  end

  defp get_user_vip_level(user_id) do
    case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
      {:ok, info} -> info.vip_level
      _ -> 0
    end
  end

  defp calculate_rebate(game_stats, vip_level) do
    # 只有净损失时才给返利
    if Decimal.compare(game_stats.net_result, 0) == :lt do
      net_loss = Decimal.abs(game_stats.net_result)  # 取绝对值作为损失
      rebate_rate = get_rebate_rate(vip_level)

      rebate_amount =
        net_loss
        |> Decimal.mult(rebate_rate)
        |> Decimal.div(100)
        |> Decimal.round(0)

      %{
        total_bet: game_stats.total_bet,
        total_win: game_stats.total_win,
        net_loss: net_loss,
        rebate_amount: rebate_amount,
        vip_level: vip_level,
        rebate_rate: rebate_rate
      }
    else
      %{
        total_bet: game_stats.total_bet,
        total_win: game_stats.total_win,
        net_loss: Decimal.new("0"),
        rebate_amount: Decimal.new("0"),
        vip_level: vip_level,
        rebate_rate: get_rebate_rate(vip_level)
      }
    end
  end

  defp get_rebate_rate(vip_level) do
    # 根据VIP等级返回对应的返利率
    case vip_level do
      # VIP0 5%
      0 -> Decimal.new("5")
      # VIP1 5%
      1 -> Decimal.new("5")
      # VIP2 6%
      2 -> Decimal.new("6")
      # VIP3 6%
      3 -> Decimal.new("6")
      # VIP4 7%
      4 -> Decimal.new("7")
      # VIP5 7%
      5 -> Decimal.new("7")
      # VIP6 8%
      6 -> Decimal.new("8")
      # VIP7 8%
      7 -> Decimal.new("8")
      # VIP8 9%
      8 -> Decimal.new("9")
      # VIP9 9%
      9 -> Decimal.new("9")
      # VIP10 10%
      10 -> Decimal.new("10")
      # VIP11 15%
      11 -> Decimal.new("15")
      # VIP12 20%
      12 -> Decimal.new("20")
      _ -> Decimal.new("20")
    end
  end

  # 计算自上次结算后的待结算返利
  defp calculate_pending_rebate(user_id, last_settlement_at) do
    start_time = last_settlement_at || DateTime.add(DateTime.utc_now(), -24, :hour)
    end_time = DateTime.utc_now()

    # 获取自上次结算后的游戏数据
    game_stats = get_game_stats(user_id, start_time, end_time)
    vip_level = get_user_vip_level(user_id)

    # 计算新的返利
    rebate_data = calculate_rebate(game_stats, vip_level)
    rebate_data.rebate_amount
  end

  # 发放返利奖励
  defp distribute_rebate_reward(user_id, rebate_amount) do
    # 构建奖励列表
    rewards = [
      %{
        type: :coins,
        amount: Decimal.to_integer(rebate_amount),
        description: "损失返利奖励"
      }
    ]

    # 构建奖励来源信息
    source = %{
      source_type: :loss_rebate_jar,
      source_id: nil,  # 设为 nil，因为损失返利不需要特定的活动ID
      activity_name: "损失返利领取",
      metadata: %{
        "claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
        "rebate_amount" => Decimal.to_string(rebate_amount),
        "user_id" => user_id
      }
    }

    # 通过统一奖励发放服务处理
    Teen.ActivitySystem.RewardDistributionService.distribute_rewards(user_id, rewards, source)
  end
end
