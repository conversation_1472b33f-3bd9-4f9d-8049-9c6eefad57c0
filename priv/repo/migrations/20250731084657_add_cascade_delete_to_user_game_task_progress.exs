defmodule Cypridina.Repo.Migrations.AddCascadeDeleteToUserGameTaskProgress do
  use Ecto.Migration

  def up do
    # 删除现有的外键约束
    drop constraint(:user_game_task_progress, "user_game_task_progress_game_task_id_fkey")
    
    # 添加新的CASCADE DELETE外键约束
    alter table(:user_game_task_progress) do
      modify :game_task_id, references(:game_tasks, on_delete: :delete_all, type: :uuid), null: false
    end
  end

  def down do
    # 删除CASCADE DELETE约束
    drop constraint(:user_game_task_progress, "user_game_task_progress_game_task_id_fkey")
    
    # 恢复原来的NO ACTION约束
    alter table(:user_game_task_progress) do
      modify :game_task_id, references(:game_tasks, on_delete: :nothing, type: :uuid), null: false
    end
  end
end
