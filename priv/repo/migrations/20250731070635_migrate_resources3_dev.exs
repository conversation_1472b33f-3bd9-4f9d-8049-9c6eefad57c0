defmodule Cypridina.Repo.Migrations.MigrateResources3 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:user_loss_rebates, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, :uuid, null: false
      add :last_settlement_at, :utc_datetime
      add :total_bet_amount, :decimal, null: false, default: "0"
      add :total_win_amount, :decimal, null: false, default: "0"
      add :net_loss_amount, :decimal, null: false, default: "0"
      add :rebate_amount, :decimal, null: false, default: "0"
      add :vip_level, :bigint, null: false, default: 0
      add :rebate_rate, :decimal, null: false, default: "0"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:user_loss_rebates, [:user_id],
             name: "user_loss_rebates_unique_user_index"
           )

    alter table(:game_records) do
      add :net_amount, :decimal, null: false, default: "0"
    end
  end

  def down do
    alter table(:game_records) do
      remove :net_amount
    end

    drop_if_exists unique_index(:user_loss_rebates, [:user_id],
                     name: "user_loss_rebates_unique_user_index"
                   )

    drop table(:user_loss_rebates)
  end
end
