defmodule Cypridina.Repo.Migrations.MigrateResources4 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:loss_rebate_jars) do
      add :last_settlement_at, :utc_datetime
    end
  end

  def down do
    alter table(:loss_rebate_jars) do
      remove :last_settlement_at
    end
  end
end
