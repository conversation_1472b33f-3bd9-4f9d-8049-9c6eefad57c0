defmodule Cypridina.Repo.Migrations.MigrateResources1 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:game_tasks) do
      modify :game_id,
             references(:game_configs,
               column: :game_id,
               name: "game_tasks_game_id_fkey",
               type: :bigint,
               prefix: "public"
             )
    end

    create index(:game_tasks, [:game_id])
  end

  def down do
    drop_if_exists index(:game_tasks, [:game_id])

    drop constraint(:game_tasks, "game_tasks_game_id_fkey")

    alter table(:game_tasks) do
      modify :game_id, :bigint
    end
  end
end
