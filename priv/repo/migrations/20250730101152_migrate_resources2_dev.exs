defmodule Cypridina.Repo.Migrations.MigrateResources2 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:game_tasks) do
      remove :reward_type
      remove :reward_amount
      add :rewards, {:array, :map}, null: false, default: []
    end

    alter table(:vip_gifts) do
      remove :monthly_reward
      remove :weekly_reward
      remove :daily_reward
      add :daily_rewards, {:array, :map}, null: false, default: []
      add :weekly_rewards, {:array, :map}, null: false, default: []
      add :monthly_rewards, {:array, :map}, null: false, default: []
    end

    alter table(:scratch_card_activities) do
      remove :max_reward
      remove :min_reward
      add :rewards, {:array, :map}, null: false, default: []
    end

    alter table(:first_recharge_gifts) do
      remove :reward_type
      remove :reward_coins
      add :rewards, {:array, :map}, null: false, default: []
    end

    alter table(:weekly_cards) do
      remove :daily_reward
      remove :initial_reward
      add :initial_rewards, {:array, :map}, null: false, default: []
      add :daily_rewards, {:array, :map}, null: false, default: []
    end

    alter table(:scratch_card_level_rewards) do
      remove :reward_type
      add :rewards, {:array, :map}, null: false, default: []
    end

    alter table(:binding_rewards) do
      remove :reward_type
      remove :reward_amount
      add :rewards, {:array, :map}, null: false, default: []
    end

    alter table(:seven_day_tasks) do
      remove :reward_amount
      add :rewards, {:array, :map}, null: false, default: []
    end

    alter table(:free_bonus_tasks) do
      remove :reward_amount
      add :rewards, {:array, :map}, null: false, default: []
    end
  end

  def down do
    alter table(:free_bonus_tasks) do
      remove :rewards
      add :reward_amount, :decimal, null: false, default: "0"
    end

    alter table(:seven_day_tasks) do
      remove :rewards
      add :reward_amount, :decimal, null: false, default: "0"
    end

    alter table(:binding_rewards) do
      remove :rewards
      add :reward_amount, :decimal, null: false
      add :reward_type, :text, null: false, default: "coins"
    end

    alter table(:scratch_card_level_rewards) do
      remove :rewards
      add :reward_type, :text, null: false
    end

    alter table(:weekly_cards) do
      remove :daily_rewards
      remove :initial_rewards
      add :initial_reward, :decimal, null: false, default: "0"
      add :daily_reward, :decimal, null: false, default: "0"
    end

    alter table(:first_recharge_gifts) do
      remove :rewards
      add :reward_coins, :decimal, null: false
      add :reward_type, :text, null: false, default: "coins"
    end

    alter table(:scratch_card_activities) do
      remove :rewards
      add :min_reward, :decimal, null: false, default: "0"
      add :max_reward, :decimal, null: false, default: "0"
    end

    alter table(:vip_gifts) do
      remove :monthly_rewards
      remove :weekly_rewards
      remove :daily_rewards
      add :daily_reward, :decimal, null: false, default: "0"
      add :weekly_reward, :decimal, null: false, default: "0"
      add :monthly_reward, :decimal, null: false, default: "0"
    end

    alter table(:game_tasks) do
      remove :rewards
      add :reward_amount, :decimal, null: false, default: "0"
      add :reward_type, :text, default: "coins"
    end
  end
end
