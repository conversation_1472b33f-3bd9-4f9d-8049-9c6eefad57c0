{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "\"新游戏任务\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "task_name", "type": "text"}, {"allow_nil?": false, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "game_id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "game_tasks_game_id_fkey", "on_delete": null, "on_update": null, "primary_key?": false, "schema": "public", "table": "game_configs"}, "scale": null, "size": null, "source": "game_id", "type": "bigint"}, {"allow_nil?": false, "default": "\"未知游戏名称\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "game_name", "type": "text"}, {"allow_nil?": false, "default": "\"game_rounds\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "task_type", "type": "text"}, {"allow_nil?": false, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "required_count", "type": "bigint"}, {"allow_nil?": false, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "max_claims", "type": "bigint"}, {"allow_nil?": true, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "target_value", "type": "bigint"}, {"allow_nil?": false, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "rewards", "type": ["array", "map"]}, {"allow_nil?": false, "default": "\"enabled\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "game_config_id", "type": "uuid"}, {"allow_nil?": true, "default": "true", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "is_active", "type": "boolean"}, {"allow_nil?": true, "default": "fragment(\"CURRENT_DATE\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "start_date", "type": "date"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "end_date", "type": "date"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "541ADDFF3042A3FE4358A8313691AF2505D8A5B06E86DC46B2427CD59A6B4DCD", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "game_tasks_unique_game_task_index", "keys": [{"type": "atom", "value": "game_id"}, {"type": "atom", "value": "task_type"}], "name": "unique_game_task", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "game_tasks"}